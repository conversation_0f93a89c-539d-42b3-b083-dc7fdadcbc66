@charset "utf-8";
@import url("reset.css");

/* 基础样式 */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* custom */
body {background: #c6eaff;}
body, td, th, input, select, textarea {color: #666; font: 12px/1.5 "微软雅黑";}
a {color: #07c; text-decoration: none;}
a:hover {color: #f30; text-decoration: underline;}
.blank10 {display: block; height: 10px; width: 100%;}
.ipt {background: url(ipt.png) no-repeat; border: solid 1px #c9c9c9; font: 14px normal; padding: 5px; vertical-align: middle; max-width: 100%;}
.btn {background: #08c; border: 0; color: #fff; height: 28px; padding: 0 7px;}
.tips {color: #999; padding-left: 10px;}
/* clearfix */
.clearfix:after {clear: both; content: "."; display: block; height: 0; visibility: hidden;}
.clearfix {display: block;}
/* hides from IE-mac \*/
.clearfix {height: 1%;}
.clearfix {display: block;}
/* wrapper */
#wrapper {background: #fff; margin: 0 auto; width: 1200px;}
/* header */
#header {}
#topbox {height: auto; min-height: 100px; padding: 10px;}
.logo {background: url(logo.png) center; display: block; float: left; height: 100px; width: 220px;}
/* sobox */
#sobox {
    float: right;
    padding: 20px 0;
    width: 500px;
}

.sofrm {
    position: relative;
    width: 100%;
    display: flex;
    align-items: center;
}

.sipt {
    flex: 1;
    height: 36px;
    border: 1px solid #dadada;
    padding: 0 10px 0 90px;
    font-size: 14px;
    border-radius: 2px;
}

.sbtn {
    width: 70px;
    height: 36px;
    background: #65bc0b;
    border: none;
    color: #fff;
    font-size: 14px;
    margin-left: -1px;
    cursor: pointer;
    border-radius: 0 2px 2px 0;
}

.sbtn:hover {
    background: #549c09;
}

/* selopt */
#selopt {
    position: absolute;
    left: 2px;
    top: 2px;
    width: 88px;
    z-index: 10;
}

#cursel {
    display: block;
    height: 32px;
    line-height: 32px;
    padding-left: 10px;
    cursor: pointer;
    background: url(select.gif) no-repeat right center;
}

#options {
    display: none;
    position: absolute;
    top: 100%;
    left: -2px;
    width: 88px;
    border: 1px solid #dadada;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#options li a {
    display: block;
    padding: 8px 10px;
    color: #555;
    transition: all 0.2s;
}

#options li a:hover {
    background: #1791de;
    color: #fff;
}

/* navbox */
#navbox {
    background: url(blue.png) repeat-x;
    min-height: 35px;
    width: 100%;
    margin-top: 10px;
}

.navbar {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    height: 35px;
}

.navbar li {
    position: relative;
    min-width: auto;
    flex: 0 1 auto;
    display: flex;
    align-items: center;
    height: 35px;
}

.navbar li a {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    padding: 0 20px;
    color: #fff;
    font-size: 14px;
    text-align: center;
    white-space: nowrap;
    transition: all 0.2s;
}

.navbar li a:hover {
    background: #0080c6;
    color: #fff;
    text-decoration: none;
}

.navbar .cur {
    background: #0067ae;
}

.navbar .navline {
    width: 1px;
    height: 35px;
    background: #0797e5;
}

/* txtbox */
#txtbox {background: url(blue.png) repeat-x 0 -55px; border-left: solid 1px #e2ecf1; border-right: solid 1px #e2ecf1; height: 40px;}
.count {float: left; padding: 10px;}
.count b {color: #f60; font: bold 16px Arial; padding-right: 3px;}
.link {color: #999; float: right; padding: 10px;}
.link a {color: #07c;}
/* sitepath */
.sitepath {padding-top: 10px; padding-left: 10px;}
/* mainbox */
#mainbox {padding: 10px;}
#mainbox-left {float: left; width: 200px;}
#mainbox-right {float: right; width: calc(100% - 220px);}
/* sidebar */
.sidebar {border: solid 1px #e8e8e8; padding-bottom: 10px;}
.sidebar h3 {background: url(blue.png) repeat-x 0 -65px; padding: 6px;}
.sidebar li {border-bottom: dashed 1px #ccc; margin: 0 10px; padding: 7px 0 7px 25px;}
/* content */
.content {border: solid 1px #e8e8e8;}
.title {background: url(blue.png) repeat-x 0 -95px; padding: 7px;}
.body {padding: 10px;}
/* icon */
#icon1 {background: url(icon.gif) 0px 0px no-repeat; padding-left: 20px;}
#icon2 {background: url(icon.gif) 0px -16px no-repeat; padding-left: 20px;}
#icon3 {background: url(icon.gif) 0px -48px no-repeat; padding-left: 20px;}
#icon4 {background: url(icon.gif) 0px -31px no-repeat; padding-left: 20px;}
#icon5 {background: url(icon.gif) 0px -76px no-repeat; padding-left: 20px;}
#icon6 {background: url(icon.gif) 0px -63px no-repeat; padding-left: 20px;}
#icon7 {background: url(icon.gif) 0px -92px no-repeat; padding-left: 20px;}
#icon8 {background: url(icon.gif) 0px -109px no-repeat; padding-left: 20px;}
#icon9 {background: url(icon.gif) 0px -125px no-repeat; padding-left: 20px;}
#icon10 {background: url(icon.gif) 0px -141px no-repeat; padding-left: 20px;}
/* notice */
.notice {background: #ffc; border: dashed 1px #f60; padding: 15px; text-align: center;}
.userinfo {margin-top: 15px;}
.userinfo h3 {font-size: 14px; padding-bottom: 10px;}
.userinfo li {padding: 6px 0;}
.userinfo li strong {color: #f30; font: normal 20px Arial;}
#homebox {padding: 10px;}
#homebox h2 {font: bold 14px normal;}
#homebox ol {padding: 10px 0;}
#homebox ol li {font-size: 13px; padding: 5px 0;}
#homebox ol li strong {color: #f00;}
/* listbox */
#listbox {}
#listbox table {}
#listbox table tr th {background: #f9f9f9; height: 30px;}
#listbox table tr td {background: #fff; border-bottom: solid 1px #ececec; height: 30px; text-align: center;}
#listbox table tr .ltext {text-align: left;}
#listbox table tr .data {font-size: 10px;}
.red {color: #f00;}
.gre {color: #080;}
.org {color: #f60;}
/* showpage */
#showpage {display: block; font-size: 12px; text-align: left; padding: 10px 0;}
.total_page, .jump_page {background: #1678bd; border: solid 1px #096cb2; color: #fff; display: block; float: left; margin-right: 5px; padding: 3px 5px;}
.first_page, .last_page, .prev_page, .next_page, .pages {background: #fff; border: 1px solid #096cb2; color: #486ba2; display: block; float: left; margin-right: 5px; padding: 3px 5px; text-decoration: none;}
.current {background: #1678bd; color: #fff; display: block; float: left; margin-right: 5px; padding: 4px 5px;}
/* formbox */
#formbox {margin-left: 0; padding: 15px;}
#formbox ul {padding-bottom: 10px;}
#formbox li {padding: 10px;}
#formbox li strong {display: block; float: left; font-weight: normal; text-align: right; width: 90px;}
#formbox li p {color: #ccc; float: left; padding: 8px 0 0 90px;}
/* claimbox */
#claimbox {}
#claimbox table {border: solid 1px #e8e8e8;}
#claimbox table tr th {background: #eff9ff; font-weight: normal; padding: 8px; text-align: left;}
#claimbox table tr td {background: #fefefe; line-height: 23px; padding: 8px;}
/* step */
.step {}
.step dt {border-bottom: solid 1px #e1e1e1; font: normal 20px normal; padding: 10px;}
.step dd {padding: 10px;}
/* footer */
#footer {background: url(fbg.gif) no-repeat; color: #555; height: 80px; padding: 10px; text-align: center;}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
    #wrapper {
        width: 95%;
    }
    
    #sobox {
        width: 400px;
    }
    
    #mainbox-left {
        width: 180px;
    }
    
    #mainbox-right {
        width: calc(100% - 200px);
    }
    
    .navbar li a {
        padding: 0 15px;
    }
}

@media screen and (max-width: 768px) {
    #header {
        text-align: center;
    }
    
    .logo {
        float: none;
        margin: 0 auto;
    }
    
    #sobox {
        float: none;
        width: 100%;
        max-width: 500px;
        margin: 20px auto;
        padding: 0 15px;
    }
    
    #navbox {
        height: auto;
    }
    
    .navbar {
       height: auto;
       flex-wrap: wrap;
       justify-content: flex-start;  /* 从左开始排列 */
   }
    
    .navbar li {
        height: 35px;
    }
    
    .navbar li a {
        padding: 0 15px;
        font-size: 14px;
    }
    
    .navbar .navline {
        display: none;
    }
    
    #mainbox-left,
    #mainbox-right {
        float: none;
        width: 100%;
        margin-bottom: 20px;
    }
    
    #formbox {
        margin: 0;
    }
    
    #formbox li strong {
        float: none;
        text-align: left;
        margin-bottom: 5px;
    }
    
    #formbox li p {
        float: none;
        padding-left: 0;
    }
    
    input[type="text"],
    textarea {
        width: 100%;
    }
}

@media screen and (max-width: 480px) {
    #sobox {
        padding: 0 10px;
    }
    
    .sipt {
        padding-left: 80px;
        font-size: 13px;
    }
    
    #selopt {
        width: 78px;
    }
    
    #options {
        width: 78px;
    }
    
    .navbar li {
        flex: 0 1 auto;
    }
    
    .navbar li a {
        padding: 0 10px;
        font-size: 13px;
    }
    
    .step dt {
        font-size: 16px;
    }
}