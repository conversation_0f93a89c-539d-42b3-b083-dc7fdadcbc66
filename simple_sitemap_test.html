<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>站点地图测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 10px; border: 1px solid #ddd; text-decoration: none; }
        .test-link:hover { background-color: #f8f9fa; }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        .loading { border-color: #ffc107; background-color: #fff3cd; }
    </style>
</head>
<body>
    <h1>站点地图测试</h1>
    <p>点击下面的链接测试各个站点地图：</p>
    
    <div id="test-results">
        <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link" data-type="webdir">
            <strong>Webdir 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=webdir&format=xml</small>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=weblink&format=xml" target="_blank" class="test-link" data-type="weblink">
            <strong>Weblink 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=weblink&format=xml</small>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=article&format=xml" target="_blank" class="test-link" data-type="article">
            <strong>Article 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=article&format=xml</small>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=category&format=xml" target="_blank" class="test-link" data-type="category">
            <strong>Category 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=category&format=xml</small>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=vip&format=xml" target="_blank" class="test-link" data-type="vip">
            <strong>VIP 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=vip&format=xml</small>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=all&format=xml" target="_blank" class="test-link" data-type="all">
            <strong>All 站点地图</strong><br>
            <small>https://www.95dir.com/?mod=sitemap&type=all&format=xml</small>
        </a>
    </div>
    
    <script>
        // 自动测试所有链接
        async function testSitemaps() {
            const links = document.querySelectorAll('.test-link');
            
            for (let link of links) {
                const url = link.href;
                const type = link.dataset.type;
                
                try {
                    link.classList.add('loading');
                    link.innerHTML += '<br><span style="color: orange;">测试中...</span>';
                    
                    const response = await fetch(url);
                    const text = await response.text();
                    
                    link.classList.remove('loading');
                    
                    if (response.ok && text.includes('<?xml') && (text.includes('</urlset>') || text.includes('</sitemapindex>'))) {
                        link.classList.add('success');
                        const urlCount = (text.match(/<url>/g) || []).length + (text.match(/<sitemap>/g) || []).length;
                        link.innerHTML = link.innerHTML.replace('测试中...', `✓ 成功 (${urlCount} 个链接)`);
                    } else {
                        link.classList.add('error');
                        link.innerHTML = link.innerHTML.replace('测试中...', `✗ 失败 (HTTP ${response.status})`);
                    }
                } catch (error) {
                    link.classList.remove('loading');
                    link.classList.add('error');
                    link.innerHTML = link.innerHTML.replace('测试中...', `✗ 错误: ${error.message}`);
                }
                
                // 等待一秒再测试下一个
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 页面加载后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(testSitemaps, 1000);
        });
    </script>
</body>
</html>
