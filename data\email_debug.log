=== 邮件发送调试 2025-07-19 04:02:41 ===
Action Type: 提交
User ID: 4
Web Name: 汽水音乐
Web URL: www.qishui.com
SMTP Host: smtp.163.com
SMTP Port: 465
SMTP Auth: yes
SMTP User: <EMAIL>
SMTP Pass: SET
Admin Email: <EMAIL>
=== 邮件发送调试 2025-07-19 04:02:41 ===
Action Type: 提交
User ID: 4
Web Name: 汽水音乐
Web URL: www.qishui.com
SMTP Host: smtp.163.com
SMTP Port: 465
SMTP Auth: yes
SMTP User: <EMAIL>
SMTP Pass: SET
Admin Email: <EMAIL>
配置检查通过
用户邮箱: <EMAIL>
邮件模板存在
邮件发送异常: Unable to load template 'file:admin_notify_mail.html'
=== 调试结束 ===


=== 邮件通知函数调用 2025-07-19 04:11:49 ===
函数被调用，开始处理邮件通知
Action Type: 提交
Web Name: 网易云音乐
Web URL: music.163.com

=== 邮件通知函数调用 2025-07-19 04:11:49 ===
函数被调用，开始处理邮件通知
Action Type: 提交
Web Name: 网易云音乐
Web URL: music.163.com
邮件配置检查通过
SMTP Host: smtp.163.com
SMTP Port: 465
Admin Email: <EMAIL>
提交用户邮箱: <EMAIL>
开始发送邮件通知
邮件主题: [95分类目录] 有新的网站提交需要审核
收件人: <EMAIL>
邮件内容长度: 3125 字符
邮件发送结果: SUCCESS
=== 邮件通知处理完成 ===

