<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta http-equiv="X-UA-Compatible" content="IE=Edge">
<meta name="renderer" content="webkit|ie-comp|ie-stand" />
<meta name="viewport" content="initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
<meta http-equiv="Cache-Control" content="no-transform"/>
<meta name="applicable-device" content="pc,wap">
<meta name="MobileOptimized" content="width"/>
<meta name="HandheldFriendly" content="true"/>
<title>404-[对不起！您访问的页面不存在]-麻城康乐兴家政服务有限公司</title>
<meta name="description" content="" />
<meta name="keywords" content="" />
<link href="/favicon.ico" rel="shortcut icon" type="image/x-icon" />
</head>
<body>
<!--404页面开始-->
<link rel="stylesheet" type="text/css" href="/404/css/404.css" />
<style>
@media screen and (max-width:760px){
.si_img{height: 100%;width: 100%;}
.errormod {width: auto;}
}
</style>
<div id="wrap">
	<div class="errormod">
		<h1><strong>网页出错</strong>抱歉，您所查看的页面无法浏览或已不存在。<span id="totalSecond">5</span><em>秒</em>后跳转到首页。</h1>
		<img class="si_img" src="/404/picture/error-img.png" width="626" height="258" alt="error">		
	</div>
<!--定义js变量及方法-->
<script language="javascript" type="text/javascript">
	var second = document.getElementById('totalSecond').textContent;
	if (navigator.appName.indexOf("Explorer") > -1)
	{
		second = document.getElementById('totalSecond').innerText; 
		} else
		{
			second = document.getElementById('totalSecond').textContent; 
		}
		setInterval("redirect()", 1000); 
		function redirect()
		{
		if (second < 0)
		{
			<!--定义倒计时后跳转页面-->
			location.href = 'https://www.klxjz.cn/'; 
			} else
			{
			if (navigator.appName.indexOf("Explorer") > -1)
			{
				document.getElementById('totalSecond').innerText = second--; 
			} else
			{
				document.getElementById('totalSecond').textContent = second--; 
			}
		}
	}
</script>
</div>
<!--404页面结束-->
</body>
</html>