/**
 * LOGO显示优化组件 - 自动适配版本
 * 自动检测白色/透明LOGO并应用黑色背景
 */

class LogoOptimizer {
    constructor() {
        this.init();
    }

    init() {
        // 确保CSS已加载
        this.loadCSS();
        // 自动优化页面中的LOGO
        this.optimizeLogos();
        // 监听动态添加的图片
        this.observeNewImages();
    }

    loadCSS() {
        // 检查CSS是否已加载
        if (!document.querySelector('link[href*="logo-preview.css"]')) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/public/css/logo-preview.css';
            document.head.appendChild(link);
        }
    }

    optimizeLogos() {
        // 查找所有需要优化的LOGO图片
        const selectors = [
            'img.wthumb',           // 网站详情页大图
            'img.thumb',            // 列表页缩略图
            'img[src*="web_pic"]',  // 包含web_pic的图片
            'img[alt*="logo"]',     // alt包含logo的图片
            'img[src*="logo"]'      // src包含logo的图片
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(img => {
                this.optimizeImage(img);
            });
        });
    }

    optimizeImage(img) {
        // 避免重复优化
        if (img.parentNode.classList.contains('logo-bg-container')) {
            return;
        }

        // 直接为图片的父容器添加渐变背景类
        const parent = img.parentNode;
        if (parent && parent.tagName !== 'BODY') {
            parent.classList.add('logo-bg-container');
        } else {
            // 如果父容器不合适，创建一个包装容器
            const wrapper = document.createElement('div');
            wrapper.className = 'logo-bg-container';
            parent.insertBefore(wrapper, img);
            wrapper.appendChild(img);
        }
    }

    // 不再需要创建复杂的容器结构

    observeNewImages() {
        // 使用MutationObserver监听新添加的图片
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1) { // Element node
                        // 检查新添加的节点是否包含需要优化的图片
                        const images = node.querySelectorAll ?
                            node.querySelectorAll('img.wthumb, img.thumb, img[src*="web_pic"], img[alt*="logo"], img[src*="logo"]') :
                            [];

                        images.forEach(img => this.optimizeImage(img));

                        // 如果节点本身就是图片
                        if (node.tagName === 'IMG' && this.shouldOptimize(node)) {
                            this.optimizeImage(node);
                        }
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    shouldOptimize(img) {
        return img.classList.contains('wthumb') ||
               img.classList.contains('thumb') ||
               img.src.includes('web_pic') ||
               img.alt.includes('logo') ||
               img.src.includes('logo');
    }

    // 静态方法：手动优化指定图片
    static optimizeElement(element) {
        const optimizer = new LogoOptimizer();
        if (element.tagName === 'IMG') {
            optimizer.optimizeImage(element);
        } else {
            element.querySelectorAll('img').forEach(img => {
                if (optimizer.shouldOptimize(img)) {
                    optimizer.optimizeImage(img);
                }
            });
        }
    }

    // 静态方法：为特定选择器添加优化
    static optimizeSelector(selector) {
        const optimizer = new LogoOptimizer();
        document.querySelectorAll(selector).forEach(img => {
            optimizer.optimizeImage(img);
        });
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    new LogoOptimizer();
});

// 为了兼容性，也在window.onload时初始化
window.addEventListener('load', () => {
    // 延迟一点确保所有图片都加载完成
    setTimeout(() => {
        new LogoOptimizer();
    }, 500);
});

// 导出到全局作用域
window.LogoOptimizer = LogoOptimizer;
