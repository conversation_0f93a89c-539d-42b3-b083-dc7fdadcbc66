{#include file="header.html"#}

<div class="main">
    <div class="title">
        <h2>价格配置管理</h2>
        <div class="nav">
            <a href="{#$fileurl#}" class="btn">配置列表</a>
            <a href="{#$fileurl#}?act=add" class="btn">添加配置</a>
        </div>
    </div>
    
    {#if $h_action == 'add'#}
    <!-- 添加价格配置表单 -->
    <div class="form">
        <form method="post" action="{#$fileurl#}">
            <input type="hidden" name="act" value="saveadd">
            <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
                <tr bgcolor="#E7E7E7">
                    <th colspan="2">添加价格配置</th>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td width="120">服务类型：</td>
                    <td>
                        <select name="service_type" required>
                            <option value="">请选择服务类型</option>
                            {#foreach from=$service_types key=type_id item=type_name#}
                            <option value="{#$type_id#}">{#$type_name#}</option>
                            {#/foreach#}
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务名称：</td>
                    <td><input type="text" name="service_name" size="30" maxlength="50" required placeholder="如：VIP服务"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>价格：</td>
                    <td><input type="number" name="price" step="0.01" min="0" required placeholder="0.00"> 元</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>计费单位：</td>
                    <td>
                        <select name="unit" required>
                            <option value="">请选择单位</option>
                            <option value="年">年</option>
                            <option value="月">月</option>
                            <option value="次">次</option>
                            <option value="天">天</option>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务时长：</td>
                    <td><input type="number" name="duration_days" min="0" placeholder="0"> 天 <span style="color: #999;">（0表示不限时长，如快审服务）</span></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效日期：</td>
                    <td><input type="date" name="effective_date" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效时间：</td>
                    <td><input type="time" name="effective_time" value="00:00"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>备注：</td>
                    <td><textarea name="remark" rows="3" cols="50" placeholder="可选"></textarea></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td colspan="2" align="center">
                        <input type="submit" value="添加配置" class="btn">
                        <input type="button" value="返回" class="btn" onclick="location.href='{#$fileurl#}'">
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    {#elseif $h_action == 'saveedit'#}
    <!-- 编辑价格配置表单 -->
    <div class="form">
        <form method="post" action="{#$fileurl#}">
            <input type="hidden" name="act" value="saveedit">
            <input type="hidden" name="id" value="{#$price_config.id#}">
            <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
                <tr bgcolor="#E7E7E7">
                    <th colspan="2">编辑价格配置</th>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td width="120">服务类型：</td>
                    <td>
                        <select name="service_type" required>
                            {#foreach from=$service_types key=type_id item=type_name#}
                            <option value="{#$type_id#}" {#if $price_config.service_type == $type_id#}selected{#/if#}>{#$type_name#}</option>
                            {#/foreach#}
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务名称：</td>
                    <td><input type="text" name="service_name" value="{#$price_config.service_name#}" size="30" maxlength="50" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>价格：</td>
                    <td><input type="number" name="price" value="{#$price_config.price#}" step="0.01" min="0" required> 元</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>计费单位：</td>
                    <td>
                        <select name="unit" required>
                            <option value="年" {#if $price_config.unit == '年'#}selected{#/if#}>年</option>
                            <option value="月" {#if $price_config.unit == '月'#}selected{#/if#}>月</option>
                            <option value="次" {#if $price_config.unit == '次'#}selected{#/if#}>次</option>
                            <option value="天" {#if $price_config.unit == '天'#}selected{#/if#}>天</option>
                        </select>
                    </td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>服务时长：</td>
                    <td><input type="number" name="duration_days" value="{#$price_config.duration_days#}" min="0"> 天</td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效日期：</td>
                    <td><input type="date" name="effective_date" value="{#$price_config.effective_date#}" required></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>生效时间：</td>
                    <td><input type="time" name="effective_time" value="{#$price_config.effective_hour#}"></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td>备注：</td>
                    <td><textarea name="remark" rows="3" cols="50">{#$price_config.remark#}</textarea></td>
                </tr>
                <tr bgcolor="#FFFFFF">
                    <td colspan="2" align="center">
                        <input type="submit" value="保存修改" class="btn">
                        <input type="button" value="返回" class="btn" onclick="location.href='{#$fileurl#}'">
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    {#else#}
    <!-- 当前有效价格 -->
    <div class="current-prices" style="margin-bottom: 30px;">
        <h3>当前有效价格</h3>
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="100">服务类型</th>
                <th width="120">服务名称</th>
                <th width="100">价格</th>
                <th width="80">时长</th>
                <th width="140">生效时间</th>
                <th width="100">操作员</th>
                <th>备注</th>
            </tr>
            {#foreach from=$current_prices item=price#}
            <tr bgcolor="#FFFFFF">
                <td align="center">
                    {#if $price.service_type == 1#}<span style="color: #ff6600;">VIP</span>
                    {#elseif $price.service_type == 2#}<span style="color: #28a745;">推荐</span>
                    {#elseif $price.service_type == 3#}<span style="color: #007bff;">快审</span>
                    {#/if#}
                </td>
                <td>{#$price.service_name#}</td>
                <td align="center" style="color: #28a745; font-weight: bold;">{#$price.price_display#}</td>
                <td align="center">{#if $price.duration_days > 0#}{#$price.duration_days#}天{#else#}不限{#/if#}</td>
                <td align="center">{#$price.effective_date#}</td>
                <td align="center">{#$price.operator#}</td>
                <td>{#$price.remark#}</td>
            </tr>
            {#foreachelse#}
            <tr bgcolor="#FFFFFF">
                <td colspan="7" align="center" style="padding: 30px; color: #999;">暂无价格配置</td>
            </tr>
            {#/foreach#}
        </table>
    </div>
    
    <!-- 价格历史记录 -->
    <div class="price-history">
        <h3>价格历史记录</h3>
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="60">ID</th>
                <th width="100">服务类型</th>
                <th width="120">服务名称</th>
                <th width="100">价格</th>
                <th width="80">时长</th>
                <th width="140">生效时间</th>
                <th width="80">状态</th>
                <th width="100">操作员</th>
                <th width="120">操作</th>
            </tr>
            {#foreach from=$price_history item=history#}
            <tr bgcolor="#FFFFFF">
                <td align="center">{#$history.id#}</td>
                <td align="center">
                    {#if $history.service_type == 1#}<span style="color: #ff6600;">VIP</span>
                    {#elseif $history.service_type == 2#}<span style="color: #28a745;">推荐</span>
                    {#elseif $history.service_type == 3#}<span style="color: #007bff;">快审</span>
                    {#/if#}
                </td>
                <td>{#$history.service_name#}</td>
                <td align="center" style="color: #28a745; font-weight: bold;">{#$history.price_display#}</td>
                <td align="center">{#if $history.duration_days > 0#}{#$history.duration_days#}天{#else#}不限{#/if#}</td>
                <td align="center">{#$history.effective_date#}</td>
                <td align="center">
                    <span style="color: {#if $history.status#}#28a745{#else#}#dc3545{#/if#};">{#$history.status_text#}</span>
                </td>
                <td align="center">{#$history.operator#}</td>
                <td align="center">
                    <a href="{#$fileurl#}?act=edit&id={#$history.id#}" class="btn" style="font-size: 12px; padding: 2px 6px;">编辑</a>
                    {#if $history.status#}
                    <a href="{#$fileurl#}?act=disable&id={#$history.id#}" onclick="return confirm('确认停用此配置？')" class="btn" style="font-size: 12px; padding: 2px 6px;">停用</a>
                    {#else#}
                    <a href="{#$fileurl#}?act=enable&id={#$history.id#}" class="btn" style="font-size: 12px; padding: 2px 6px;">启用</a>
                    {#/if#}
                    <a href="{#$fileurl#}?act=delete&id={#$history.id#}" onclick="return confirm('确认删除此配置？删除后不可恢复！')" class="btn" style="font-size: 12px; padding: 2px 6px; color: #dc3545;">删除</a>
                </td>
            </tr>
            {#foreachelse#}
            <tr bgcolor="#FFFFFF">
                <td colspan="9" align="center" style="padding: 30px; color: #999;">暂无历史记录</td>
            </tr>
            {#/foreach#}
        </table>
    </div>
    {#/if#}
</div>

{#include file="footer.html"#}
