<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
	<div id="mainbox" class="clearfix mtop10">
		<div id="subbox">
		<div style="line-height: 25px; padding: 10px 20px;">
        <div class="quick-submit-header">
            <h2>🚀 快速提交网站</h2>
            <p>无需注册，直接提交您的网站到我们的分类目录！</p>
        </div>

        <div class="submit-notice">
        <strong style="color: #f00;">提交须知：</strong><br />
1. 不收录有反动、色情、赌博等不良内容或提供不良内容链接的网站；<br />
2. 不收录含有病毒、木马，弹出插件或恶意更改他人电脑设置的网站；<br />
3. 不收录网站名称和实际内容不符的网站，请确保网站内容完整；<br />
4. 不收录非顶级域名、挂靠其他站点、无实际内容的网站；<br />
5. 不收录在正常情况下无法访问的网站；<br />
6. 公益性网站，或内容确实有独特之处的网站将优先收录；<br />
<span style="color: #f00;"><strong>特别提示：</strong><br />
为了达到共赢的效果，凡是申请收录的网站，请在其首页设置本站的友情链接；<br />
本站会定期对已收录网站进行复审，对不符合要求的网站，本站将随时删除该站信息。</span><br />
        </div>
		</div>
        
        {#if $cfg.is_enabled_submit == 'yes'#}
        <form name="quickform" id="quickform" method="post" action="{#$pageurl#}">
        <ul class="formbox">
        	<li><strong>选择分类：</strong><p><select name="cate_id" id="cate_id" required>
        		<option value="">请选择网站分类</option>
        		{#$category_option#}
        	</select> <span style="color: #f00;">*</span></p></li>

            <li><strong>网站域名：</strong><p><input type="text" name="web_url" id="web_url" class="fipt" size="50" maxlength="100" placeholder="请输入网站域名，如：example.com" required onblur="checkurl(this.value)" />
            <input type="button" id="meta_btn" class="fbtn" value="获取Meta信息" onclick="getmeta()" style="margin-left: 10px;" />
            <br><span id="url_msg">不需要输入http://或www</span> <span style="color: #f00;">*</span></p>
            </li>

        	<li><strong>网站名称：</strong><p><input type="text" name="web_name" id="web_name" class="fipt" size="50" maxlength="12" onblur="checkWebName(this.value)" onkeyup="checkWebNameLength(this)" placeholder="请输入网站名称" required />
        	<span id="web_name_msg">最多12个字符（6个汉字）</span> <span style="color: #f00;">*</span></p>
        	</li>

            <li><strong>TAG标签：</strong><p><input type="text" name="web_tags" id="web_tags" class="fipt" size="50" maxlength="30" onblur="javascript:this.value=this.value.replace(/，/ig,',');" placeholder="请输入相关标签，用逗号分隔" /> <span>例：旅游,酒店,机票,度假</span></p></li>

        	<li><strong>网站简介：</strong><p><textarea name="web_intro" id="web_intro" cols="50" rows="6" class="fipt" placeholder="请详细描述您的网站内容和特色，至少10个字符" required></textarea>
        	<span id="intro_msg">至少10个字符，最多500个字符</span> <span style="color: #f00;">*</span></p></li>

            <li><strong>站长姓名：</strong><p><input type="text" name="web_owner" id="web_owner" class="fipt" size="30" maxlength="20" placeholder="请输入站长姓名（可选）" /></p></li>

            <li><strong>电子邮箱：</strong><p><input type="email" name="web_email" id="web_email" class="fipt" size="30" maxlength="50" placeholder="请输入有效的电子邮箱" required /> <span style="color: #f00;">*</span></p></li>

        	<li><strong>验 证 码：</strong><p><input type="text" name="check_code" id="check_code" size="10" maxlength="5" class="fipt" onfocus="refreshimg('mycode');" placeholder="请输入验证码" required /> <span id="mycode">点击输入框即可显示验证码</span> <span style="color: #f00;">*</span></p></li>

        	<li><strong>&nbsp;</strong><p>
        		<input type="hidden" name="act" id="act" value="submit">
        		<input type="submit" name="submit" class="fbtn" value="立即提交" onclick="return validateForm();">
        		<input type="reset" name="reset" class="fbtn" value="重新填写" onclick="return confirm('确定要重新填写吗？');">
        	</p></li>
        </ul>
        </form>

        <div class="tips-box">
        	<strong>🎯 快速提交优势：</strong><br>
        	• 标有 <span style="color: #f00;">*</span> 的为必填项<br>
        	• 无需注册，直接提交，节省时间<br>
        	• 提交后我们会在1-3个工作日内审核您的网站<br>
        	• 审核通过后，您的网站将出现在相应分类中<br>
        	• 免费收录，提升网站知名度和SEO效果
        </div>
        {#else#}
        <div style="padding: 20px; text-align: center; color: #f00; font-size: 16px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; margin: 20px;">
        	⚠️ {#$cfg.submit_close_reason#}
        </div>
        {#/if#}
		</div>
		</div>
    </div>
    {#include file="footer.html"#}
</div>

<style>
/* 快速提交表单样式优化 */
.formbox {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    font-size: 13px;
    line-height: 1.6;
}

.formbox li {
    margin-bottom: 18px;
}

.formbox strong {
    display: inline-block;
    width: 100px;
    color: #333;
    font-size: 13px;
    font-weight: bold;
}

.formbox .fipt, .formbox select, .formbox textarea {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 13px;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.formbox .fipt:focus, .formbox select:focus, .formbox textarea:focus {
    border-color: #007bff;
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.formbox .fbtn {
    background: #007bff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 10px 20px;
    cursor: pointer;
    font-size: 13px;
    font-weight: normal;
    margin-right: 10px;
    transition: background-color 0.3s ease;
}

.formbox .fbtn:hover {
    background: #0056b3;
}

.formbox .fbtn[type="reset"] {
    background: #6c757d;
}

.formbox .fbtn[type="reset"]:hover {
    background: #545b62;
}

/* Meta按钮特殊样式 */
#meta_btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(76, 175, 80, 0.3);
}

#meta_btn:hover {
    background: linear-gradient(135deg, #45a049 0%, #4CAF50 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
}

#meta_btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.quick-submit-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    text-align: center;
}

.quick-submit-header h2 {
    margin: 0 0 10px 0;
    font-size: 20px;
    font-weight: bold;
}

.quick-submit-header p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
}

.tips-box {
    background: #f8f9fa;
    border-left: 4px solid #28a745;
    border-radius: 4px;
    padding: 15px;
    margin: 20px 0;
    font-size: 13px;
    line-height: 1.6;
}

.tips-box strong {
    color: #28a745;
    font-size: 13px;
    font-weight: bold;
}

/* 表单提示文字样式 */
.formbox span {
    font-size: 12px;
    color: #666;
}

.formbox span[style*="color: #f00"] {
    font-size: 12px;
    color: #f00 !important;
}

/* 提交须知样式 */
.submit-notice {
    font-size: 13px;
    line-height: 1.8;
    color: #333;
}

.submit-notice strong {
    font-size: 13px;
    font-weight: bold;
}

@media (max-width: 768px) {
    .formbox {
        margin: 10px;
        padding: 15px;
        font-size: 12px;
    }

    .formbox strong {
        width: auto;
        display: block;
        margin-bottom: 5px;
        font-size: 12px;
    }

    .formbox .fipt, .formbox select, .formbox textarea {
        width: 100%;
        box-sizing: border-box;
        font-size: 12px;
    }

    .formbox .fbtn {
        font-size: 12px;
        padding: 8px 16px;
    }

    .quick-submit-header h2 {
        font-size: 18px;
    }

    .quick-submit-header p {
        font-size: 13px;
    }

    .tips-box {
        font-size: 12px;
    }
}
</style>

<script>
// 网站名称长度检查函数
function checkWebNameLength(input) {
    const value = input.value;
    const msgElement = document.getElementById('web_name_msg');

    // 计算字符长度（中文算2个字符）
    let length = 0;
    for (let i = 0; i < value.length; i++) {
        if (value.charCodeAt(i) > 127) {
            length += 2; // 中文字符算2个字符
        } else {
            length += 1; // 英文字符算1个字符
        }
    }

    if (length > 12) {
        msgElement.innerHTML = '<span style="color: #f00; font-size: 12px;">网站名称过长！最多12个字符（6个汉字）</span>';
        return false;
    } else if (length === 0) {
        msgElement.innerHTML = '<span style="color: #999; font-size: 12px;">最多12个字符（6个汉字）</span>';
    } else {
        msgElement.innerHTML = `<span style="color: #666; font-size: 12px;">已输入${length}/12个字符</span>`;
    }
    return true;
}

// 网站名称失焦验证
function checkWebName(name) {
    const msgElement = document.getElementById('web_name_msg');

    if (!name.trim()) {
        msgElement.innerHTML = '<span style="color: #f00; font-size: 12px;">请输入网站名称！</span>';
        return false;
    }

    // 计算字符长度
    let length = 0;
    for (let i = 0; i < name.length; i++) {
        if (name.charCodeAt(i) > 127) {
            length += 2;
        } else {
            length += 1;
        }
    }

    if (length > 12) {
        msgElement.innerHTML = '<span style="color: #f00; font-size: 12px;">网站名称过长！最多12个字符（6个汉字）</span>';
        return false;
    }

    msgElement.innerHTML = `<span style="color: #666; font-size: 12px;">已输入${length}/12个字符</span>`;
    return true;
}

// 完整的表单验证函数
function validateForm() {
    // 验证分类选择
    const cateId = document.getElementById('cate_id').value;
    if (!cateId || cateId == '') {
        alert('请选择网站分类！');
        document.getElementById('cate_id').focus();
        return false;
    }
    
    // 验证网站域名
    const webUrl = document.getElementById('web_url').value.trim();
    if (!webUrl) {
        alert('请输入网站域名！');
        document.getElementById('web_url').focus();
        return false;
    }

    // 域名格式验证 - 支持带路径和查询参数的URL
    const cleanUrl = webUrl.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');

    // 使用更灵活的URL验证，支持路径和查询参数
    let urlToValidate = cleanUrl;
    try {
        // 尝试解析URL以提取主机名
        const tempUrl = new URL('http://' + cleanUrl);
        urlToValidate = tempUrl.hostname;
    } catch (e) {
        // 如果解析失败，使用原始清理后的URL进行基本验证
        urlToValidate = cleanUrl.split('/')[0].split('?')[0];
    }

    // 验证主机名部分
    const domainPattern = /^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainPattern.test(urlToValidate)) {
        alert('请输入正确的网站域名！例如：example.com 或 example.com/path?param=value');
        document.getElementById('web_url').focus();
        return false;
    }

    // 检查域名状态提示
    const urlMsg = document.getElementById('url_msg').innerHTML;
    if (urlMsg.indexOf('该网站已被拉黑') !== -1 ||
        urlMsg.indexOf('正在审核中') !== -1 ||
        urlMsg.indexOf('已收录') !== -1 ||
        urlMsg.indexOf('审核不通过') !== -1 ||
        urlMsg.indexOf('已存在') !== -1) {
        alert('该域名已存在或状态不允许重复提交，请检查域名状态提示！');
        document.getElementById('web_url').focus();
        return false;
    }
    
    // 验证网站名称
    const webName = document.getElementById('web_name').value.trim();
    if (!checkWebName(webName)) {
        document.getElementById('web_name').focus();
        return false;
    }
    
    // 验证网站简介
    const webIntro = document.getElementById('web_intro').value.trim();
    if (!webIntro) {
        alert('请输入网站简介！');
        document.getElementById('web_intro').focus();
        return false;
    }
    
    if (webIntro.length < 10) {
        alert('网站简介至少需要10个字符！');
        document.getElementById('web_intro').focus();
        return false;
    }
    
    if (webIntro.length > 500) {
        alert('网站简介不能超过500个字符！');
        document.getElementById('web_intro').focus();
        return false;
    }
    
    // 验证电子邮箱
    const webEmail = document.getElementById('web_email').value.trim();
    if (!webEmail) {
        alert('请输入电子邮箱！');
        document.getElementById('web_email').focus();
        return false;
    }
    
    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(webEmail)) {
        alert('请输入正确的电子邮箱格式！');
        document.getElementById('web_email').focus();
        return false;
    }
    
    // 验证验证码
    const checkCode = document.getElementById('check_code').value.trim();
    if (!checkCode) {
        alert('请输入验证码！');
        document.getElementById('check_code').focus();
        return false;
    }
    
    return true;
}

// 网站简介字符计数
function updateIntroCount() {
    const intro = document.getElementById('web_intro').value;
    const msgElement = document.getElementById('intro_msg');
    const length = intro.length;
    
    if (length < 10) {
        msgElement.innerHTML = `<span style="color: #f00; font-size: 12px;">至少需要10个字符，当前${length}个字符</span>`;
    } else if (length > 500) {
        msgElement.innerHTML = `<span style="color: #f00; font-size: 12px;">超出字符限制！当前${length}/500个字符</span>`;
    } else {
        msgElement.innerHTML = `<span style="color: #666; font-size: 12px;">当前${length}/500个字符</span>`;
    }
}

// 检查域名是否已提交 - 适配原有的ajaxget.php逻辑
function checkurl(url) {
    const msgElement = document.getElementById('url_msg');

    if (!url || url.trim() === '') {
        msgElement.innerHTML = '不需要输入http://或www';
        return false;
    }

    url = url.trim();

    // 显示检测中状态
    msgElement.innerHTML = '<img src="' + sitepath + 'public/images/loading.gif" width="16" height="16" style="vertical-align: middle;"> 正在检测域名状态，请稍候...';

    // 需要检测多种格式，因为数据库中可能存储不同的格式
    const urlsToCheck = [];

    // 添加原始输入
    urlsToCheck.push(url);

    // 添加清理后的格式（去掉协议和www）
    let cleanUrl = url.replace(/^https?:\/\//, '').replace(/^www\./, '').replace(/\/$/, '');
    if (cleanUrl !== url) {
        urlsToCheck.push(cleanUrl);
    }

    // 添加带www的格式
    if (!cleanUrl.startsWith('www.')) {
        urlsToCheck.push('www.' + cleanUrl);
    }

    // 逐个检测不同格式
    checkUrlFormats(urlsToCheck, 0, msgElement);

    return true;
}

// 递归检测不同的URL格式
function checkUrlFormats(urls, index, msgElement) {
    if (index >= urls.length) {
        // 所有格式都检测完了，都没找到 - 域名可以提交
        msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <span style="color: #666;">正在自动获取Meta信息...</span>';

        // 自动获取Meta信息
        setTimeout(function() {
            autoGetMeta();
        }, 500);
        return;
    }

    const currentUrl = urls[index];

    // 使用jQuery发送AJAX请求
    if (typeof $ !== 'undefined') {
        $.ajax({
            type: "GET",
            url: sitepath + '?mod=ajaxget&type=check',
            data: 'url=' + encodeURIComponent(currentUrl),
            cache: false,
            timeout: 8000,
            success: function(data) {
                console.log('域名检测 (' + currentUrl + '):', data);

                // 如果找到了记录（不是"可以提交"的消息）
                if (data.indexOf('该域名可以提交') === -1) {
                    // 移除ajaxget.php返回的多余"自动抓取"链接
                    data = data.replace(/<a[^>]*onclick="getmeta[^"]*"[^>]*>.*?<\/a>/gi, '');
                    data = data.replace(/自动抓取&raquo;/gi, '');
                    msgElement.innerHTML = data;
                } else {
                    // 继续检测下一个格式
                    checkUrlFormats(urls, index + 1, msgElement);
                }
            },
            error: function(xhr, status, error) {
                console.error('域名检测失败 (' + currentUrl + '):', xhr.status, status, error);
                // 继续检测下一个格式
                checkUrlFormats(urls, index + 1, msgElement);
            }
        });
    } else {
        msgElement.innerHTML = '<span style="color: #f00;">系统错误：jQuery未加载</span>';
    }
}

// 自动获取Meta信息的函数 - 简化版本
function autoGetMeta() {
    const msgElement = document.getElementById('url_msg');
    const url = $("#web_url").val();

    if (!url || url.trim() === '') {
        msgElement.innerHTML = '<span style="color: #f00;">请先输入网站域名</span>';
        return;
    }

    // 显示获取状态
    msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <img src="' + sitepath + 'public/images/loading.gif" width="16" height="16" style="vertical-align: middle;"> 正在自动获取Meta信息...';

    // 直接调用现有的getmeta逻辑，但修改按钮状态显示
    $(document).ready(function(){
        $.ajax({
            type: "GET",
            url: sitepath + '?mod=ajaxget&type=crawl',
            data: 'url=' + url,
            datatype: "script",
            cache: false,
            success: function(data){
                console.log('Meta信息返回:', data);

                // 执行返回的JavaScript
                $("body").append(data);

                // 延迟检查结果
                setTimeout(function() {
                    const webName = $("#web_name").val();
                    const webTags = $("#web_tags").val();
                    const webIntro = $("#web_intro").val();

                    if (webName || webTags || webIntro) {
                        msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <span style="color: #28a745;">✅ Meta信息获取成功</span>';

                        // 触发字符计数更新
                        if (webName) checkWebNameLength(document.getElementById('web_name'));
                        if (webIntro) updateIntroCount();

                        // 3秒后显示简化提示
                        setTimeout(function() {
                            msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <span style="color: #666;">已自动填充网站信息</span>';
                        }, 3000);
                    } else {
                        msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <span style="color: #f60;">Meta信息获取失败，请手动填写</span>';
                    }
                }, 500);
            },
            error: function(xhr, status, error) {
                console.error('Meta信息获取失败:', xhr.status, status, error);
                msgElement.innerHTML = '<span style="color: #090; font-weight: bold;">✓ 该域名可以提交</span> <span style="color: #f60;">Meta信息获取失败，请手动填写</span>';
            }
        });
    });
}

// 获取META信息 - 直接使用会员中心的成熟方案
function getmeta() {
    var url = $("#web_url").attr("value");
    if (url == '') {
        alert('请输入网站域名！');
        $("#web_url").focus();
        return false;
    }
    $(document).ready(function(){$("#meta_btn").val('正在获取，请稍候...'); $.ajax({type: "GET", url: sitepath + '?mod=ajaxget&type=crawl', data: 'url=' + url, datatype: "script", cache: false, success: function(data){$("body").append(data); $("#meta_btn").val('获取Meta信息');}});});
}

// 表单提交验证
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form[name="quickform"]');
    if (form) {
        // 添加简介字符计数
        const introTextarea = document.getElementById('web_intro');
        if (introTextarea) {
            introTextarea.addEventListener('input', updateIntroCount);
            introTextarea.addEventListener('keyup', updateIntroCount);
        }

        form.addEventListener('submit', function(e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }
        });
    }
});
</script>

</body>
</html>
