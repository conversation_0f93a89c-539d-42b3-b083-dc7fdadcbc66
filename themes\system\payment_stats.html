{#include file="header.html"#}

<div class="main">
    <div class="title">
        <h2>付费统计</h2>
    </div>
    
    <!-- 统计概览 -->
    <div class="stats-overview" style="margin-bottom: 20px;">
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
            <h3 style="margin: 0 0 10px 0; color: #333;">统计概览</h3>
            <div style="display: flex; gap: 20px; flex-wrap: wrap;">
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #007bff;">
                    <strong>总记录数：</strong><span style="color: #007bff; font-size: 18px;">{#$stats.total_count#}</span>
                </div>
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; border-left: 4px solid #28a745;">
                    <strong>总金额：</strong><span style="color: #28a745; font-size: 18px;">￥{#$stats.total_amount#}</span>
                </div>
            </div>
        </div>
        
        <div style="background: #f8f9fa; padding: 15px; border-radius: 5px;">
            <h4 style="margin: 0 0 10px 0; color: #333;">按类型统计</h4>
            <div style="display: flex; gap: 15px; flex-wrap: wrap;">
                {#foreach from=$type_stats item=stat#}
                <div style="background: #fff; padding: 10px 15px; border-radius: 3px; min-width: 150px;">
                    <div style="font-weight: bold; color: #333;">{#$stat.name#}</div>
                    <div style="color: #666; font-size: 14px;">实际数量: <span style="color: #28a745; font-weight: bold;">{#$stat.count#}</span></div>
                    <div style="color: #666; font-size: 14px;">记录数量: {#$stat.record_count#}</div>
                    <div style="color: #666; font-size: 14px;">金额: ￥{#$stat.amount#}</div>
                </div>
                {#/foreach#}
            </div>
            <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 3px;">
                <div style="color: #856404; font-size: 14px;">
                    <strong>💡 提示：</strong>推荐统计不包含VIP+推荐的网站，避免重复计费。如果实际数量与记录数量不匹配，可能存在历史数据问题。
                    <a href="payment_fix.php" style="color: #007bff; margin-left: 10px;">点击检查和修复数据</a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 筛选表单 -->
    <div class="filter-form" style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
        <form method="get" action="{#$fileurl#}">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div>
                    <label>付费类型：</label>
                    <select name="type" style="padding: 5px;">
                        <option value="0">全部</option>
                        <option value="1" {#if $payment_type == 1#}selected{#/if#}>VIP</option>
                        <option value="2" {#if $payment_type == 2#}selected{#/if#}>推荐</option>
                        <option value="3" {#if $payment_type == 3#}selected{#/if#}>快审</option>
                    </select>
                </div>
                <div>
                    <label>开始日期：</label>
                    <input type="date" name="start_date" value="{#$start_date#}" style="padding: 5px;">
                </div>
                <div>
                    <label>结束日期：</label>
                    <input type="date" name="end_date" value="{#$end_date#}" style="padding: 5px;">
                </div>
                <div>
                    <label>关键词：</label>
                    <input type="text" name="keywords" value="{#$keywords#}" placeholder="网站名称或网址" style="padding: 5px; width: 150px;">
                </div>
                <div>
                    <input type="submit" value="筛选" class="btn" style="padding: 6px 15px;">
                    <a href="{#$fileurl#}" class="btn" style="padding: 6px 15px; margin-left: 5px;">重置</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 记录列表 -->
    <div class="list">
        <table width="100%" border="0" cellpadding="8" cellspacing="1" bgcolor="#D5D5D5">
            <tr bgcolor="#E7E7E7">
                <th width="60">ID</th>
                <th width="120">网站名称</th>
                <th width="150">网站网址</th>
                <th width="80">付费类型</th>
                <th width="80">单价</th>
                <th width="80">付费金额</th>
                <th width="140">付费时间</th>
                <th width="140">到期时间</th>
                <th width="80">状态</th>
                <th width="80">操作员</th>
                <th>备注</th>
            </tr>
            {#foreach from=$records item=record#}
            <tr bgcolor="#FFFFFF">
                <td align="center">{#$record.id#}</td>
                <td><a href="../?mod=website_detail&id={#$record.web_id#}" target="_blank" title="查看网站详情">{#$record.web_name|truncate:20#}</a></td>
                <td><a href="{#$record.web_url#}" target="_blank" title="访问网站">{#$record.web_url|truncate:25#}</a></td>
                <td align="center"><span style="color: {#$record.type_color#}; font-weight: bold;">{#$record.type_name#}</span></td>
                <td align="center" style="color: #666; font-size: 12px;">{#$record.unit_price#}</td>
                <td align="center" style="color: #28a745; font-weight: bold;">￥{#$record.payment_amount#}</td>
                <td align="center">{#$record.payment_date#}</td>
                <td align="center">{#$record.expire_date#}</td>
                <td align="center"><span style="color: {#$record.status_color#}; font-weight: bold;">{#$record.status_name#}</span></td>
                <td align="center">{#$record.operator#}</td>
                <td>{#$record.remark|truncate:30#}</td>
            </tr>
            {#foreachelse#}
            <tr bgcolor="#FFFFFF">
                <td colspan="11" align="center" style="padding: 30px; color: #999;">暂无付费记录</td>
            </tr>
            {#/foreach#}
        </table>
    </div>
    
    <!-- 分页 -->
    {#if $showpage#}
    <div class="pages">
        {#$showpage#}
    </div>
    {#/if#}
</div>

{#include file="footer.html"#}
