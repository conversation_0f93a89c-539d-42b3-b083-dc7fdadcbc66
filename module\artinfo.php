<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '文章详细';
$pageurl = '?mod=artinfo';
$tempfile = 'artinfo.html';
$table = $DB->table('articles');

$art_id = intval($_GET['aid']);
$cache_id = $art_id;
		
if (!$smarty->isCached($tempfile, $cache_id)) {
	$where = "a.art_status=3 AND a.art_id=$art_id";
	$art = get_one_article($where);
	if (!$art) {
		unset($art);
		redirect('./?mod=index');
	}
	
	$DB->query("UPDATE $table SET art_views=art_views+1 WHERE art_id=".$art['art_id']." LIMIT 1");
	
	$cate = get_one_category($art['cate_id']);
	$user = get_one_user($art['user_id']);

	// 优化SEO标题 - 更具吸引力
	$seo_title = $art['art_title'] . ' - ' . $cate['cate_name'] . '资讯 - ' . $options['site_name'];
	$smarty->assign('site_title', $seo_title);

	// 优化关键词 - 结合文章标签、分类和网站关键词
	$seo_keywords = array();
	if (!empty($art['art_tags'])) {
		$tags_array = explode(',', $art['art_tags']);
		$seo_keywords = array_merge($seo_keywords, $tags_array);
	}
	$seo_keywords[] = $cate['cate_name'];
	$seo_keywords[] = '站长资讯';
	$seo_keywords[] = '网站建设';
	if (!empty($options['site_keywords'])) {
		$site_keywords = explode(',', $options['site_keywords']);
		$seo_keywords = array_merge($seo_keywords, $site_keywords);
	}
	$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));

	// 优化描述 - 更丰富的描述信息
	$seo_description = '';
	if (!empty($art['art_intro'])) {
		$seo_description = $art['art_intro'];
	} else {
		// 从文章内容中提取前150个字符作为描述
		$content_text = strip_tags($art['art_content']);
		$content_text = preg_replace('/\s+/', ' ', $content_text);
		$seo_description = mb_substr($content_text, 0, 150, 'UTF-8');
	}

	// 添加发布信息
	$seo_description .= ' - 发布于' . $options['site_name'] . $cate['cate_name'] . '栏目';
	if (isset($art['art_views']) && $art['art_views'] > 0) {
		$seo_description .= '，已有' . $art['art_views'] . '人阅读';
	}

	// 确保描述长度适中
	if (mb_strlen($seo_description, 'UTF-8') > 160) {
		$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
	}

	$smarty->assign('site_description', $seo_description);
	$smarty->assign('site_path', get_sitepath($cate['cate_mod'], $art['cate_id']).' &raquo; '.$pagename);
	$smarty->assign('site_rss', get_rssfeed($cate['cate_mod'], $art['cate_id']));
	
	$smarty->assign('cate_id', $cate['cate_id']);
	$smarty->assign('cate_name', $cate['cate_name']);
	$smarty->assign('cate_keywords', $cate['cate_keywords']);
	$smarty->assign('cate_description', $cate['cate_description']);
	
	$art['art_content'] = str_replace('[upload_dir]', $options['site_root'].$options['upload_dir'].'/', $art['art_content']);
	$art['art_ctime'] = date('Y-m-d', $art['art_ctime']);

	// 格式化标签，让每个标签都能单独链接到文章搜索
	$art_tags_formatted = get_format_tags($art['art_tags'], 'article');
	$smarty->assign('art_tags', $art_tags_formatted);

	$smarty->assign('art', $art);
	$smarty->assign('user', $user);
	$smarty->assign('prev', get_prev_article($art['art_id']));
	$smarty->assign('next', get_next_article($art['art_id']));
	$smarty->assign('related_article', get_articles($art['cate_id'], 8, false, 'ctime'));
}
		
smarty_output($tempfile, $cache_id);
?>