<?php
/*
 * 违规管理页面
 * 重定向到实际的违规管理功能
 */

require('common.php');

// 检查管理员权限
if (!$myself['user_id']) {
    exit('Access Denied');
}

// 检查是否存在新的违规管理文件
if (file_exists('violation_simple_working.php')) {
    // 重定向到新的违规管理页面
    header('Location: violation_simple_working.php');
    exit;
} elseif (file_exists('blacklist.php')) {
    // 重定向到黑名单管理页面
    header('Location: blacklist.php');
    exit;
} else {
    // 如果都不存在，显示简单的违规管理页面
    $fileurl = 'violation.php';
    $tempfile = 'violation.html';
    $pagetitle = '违规管理';
    
    $action = trim($_GET['act']);
    
    if ($action == 'list' || $action == '') {
        // 获取黑名单网站
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites')." WHERE web_status = 1 ORDER BY web_ctime DESC LIMIT 50";
        $query = $DB->query($sql);
        
        $websites = array();
        while ($row = $DB->fetch_array($query)) {
            $row['ctime_formatted'] = date('Y-m-d H:i:s', $row['web_ctime']);
            $websites[] = $row;
        }
        
        // 获取总数
        $total_sql = "SELECT COUNT(*) as total FROM ".$DB->table('websites')." WHERE web_status = 1";
        $total_result = $DB->fetch_one($total_sql);
        $total = $total_result['total'];
        
        $smarty->assign('websites', $websites);
        $smarty->assign('total', $total);
        $smarty->assign('fileurl', $fileurl);
        $smarty->assign('pagetitle', $pagetitle);
        
        smarty_output($tempfile);
    }
}
?>
