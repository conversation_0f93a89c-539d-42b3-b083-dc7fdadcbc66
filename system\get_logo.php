<?php
// 关闭错误输出到页面，避免影响JSON格式
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 错误处理函数
function sendError($message, $debug_info = null) {
    $response = ['status' => 'error', 'message' => $message];
    if ($debug_info && isset($_GET['debug'])) {
        $response['debug'] = $debug_info;
    }
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data) {
    echo json_encode(array_merge(['status' => 'success'], $data), JSON_UNESCAPED_UNICODE);
    exit;
}

// 捕获所有输出缓冲，确保只输出JSON
ob_start();

try {
    // 接收URL参数
    $url = isset($_POST['url']) ? trim($_POST['url']) : '';
    if (empty($url)) {
        sendError('网站URL不能为空');
    }

    // 确保URL格式正确
    if (!preg_match('/^https?:\/\//', $url)) {
        $url = 'https://' . $url;
    }

    // 验证URL
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        sendError('无效的URL格式');
    }

    // 定义保存目录
    $upload_dir = '/www/wwwroot/www.95dir.com/uploads/website/';
    
    // 检查目录是否存在
    if (!file_exists($upload_dir)) {
        if (!mkdir($upload_dir, 0755, true)) {
            sendError('无法创建上传目录');
        }
    }

    // 检查目录权限
    if (!is_writable($upload_dir)) {
        sendError('上传目录没有写入权限');
    }

    // 生成文件名
    $domain = parse_url($url, PHP_URL_HOST);
    if (!$domain) {
        sendError('无法解析域名');
    }

    // 设置HTTP请求上下文
    $context = stream_context_create([
        'http' => [
            'timeout' => 10,
            'user_agent' => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'follow_location' => true,
            'max_redirects' => 3,
            'ignore_errors' => true
        ],
        'ssl' => [
            'verify_peer' => false,
            'verify_peer_name' => false
        ]
    ]);

    // 获取网站HTML内容
    $html = @file_get_contents($url, false, $context);
    if ($html === false) {
        sendError('无法访问网站，请检查URL是否正确');
    }

    // 查找LOGO候选URL
    $logo_candidates = [];
    $priority_candidates = []; // 高优先级候选（包含logo关键字）
    $regular_candidates = [];  // 普通候选

    // 1. 从HTML中提取所有可能的logo图片
    $logo_patterns = [
        // 查找包含logo关键字的img标签
        '/<img[^>]*src=["\']([^"\']*logo[^"\']*)["\'][^>]*>/i',
        '/<img[^>]*src=["\']([^"\']*brand[^"\']*)["\'][^>]*>/i',
        // 查找class或id包含logo的img
        '/<img[^>]*(?:class|id)=["\'][^"\']*logo[^"\']*["\'][^>]*src=["\']([^"\']+)["\'][^>]*>/i',
        '/<img[^>]*src=["\']([^"\']+)["\'][^>]*(?:class|id)=["\'][^"\']*logo[^"\']*["\'][^>]*>/i',
        // 查找logo相关的div背景图
        '/background-image:\s*url\(["\']?([^"\']*logo[^"\']*)["\']?\)/i',
        // 查找CSS中的logo图片
        '/\.logo[^{]*{[^}]*background[^:]*:\s*url\(["\']?([^"\']+)["\']?\)/i'
    ];

    foreach ($logo_patterns as $pattern) {
        if (preg_match_all($pattern, $html, $matches)) {
            foreach ($matches[1] as $match) {
                $resolved_url = resolveUrl($match, $url);
                if (stripos($resolved_url, 'logo') !== false) {
                    $priority_candidates[] = $resolved_url;
                } else {
                    $regular_candidates[] = $resolved_url;
                }
            }
        }
    }

    // 2. 从HTML中提取favicon和icon
    $icon_patterns = [
        '/<link[^>]*rel=["\'](?:icon|shortcut icon|apple-touch-icon)["\'][^>]*href=["\']([^"\']+)["\'][^>]*>/i',
        '/<link[^>]*href=["\']([^"\']+)["\'][^>]*rel=["\'](?:icon|shortcut icon|apple-touch-icon)["\'][^>]*>/i',
        '/<meta[^>]*property=["\']og:image["\'][^>]*content=["\']([^"\']+)["\'][^>]*>/i'
    ];

    foreach ($icon_patterns as $pattern) {
        if (preg_match_all($pattern, $html, $matches)) {
            foreach ($matches[1] as $match) {
                $resolved_url = resolveUrl($match, $url);
                if (stripos($resolved_url, 'logo') !== false) {
                    $priority_candidates[] = $resolved_url;
                } else {
                    $regular_candidates[] = $resolved_url;
                }
            }
        }
    }

    // 3. 添加常见的logo路径（优先级）
    $parsed_url = parse_url($url);
    $base_url = $parsed_url['scheme'] . '://' . $parsed_url['host'];

    $priority_paths = [
        '/logo.svg', '/logo.png', '/logo.jpg', '/logo.jpeg', '/logo.gif',
        '/assets/logo.svg', '/assets/logo.png', '/assets/logo.jpg',
        '/images/logo.svg', '/images/logo.png', '/images/logo.jpg',
        '/img/logo.svg', '/img/logo.png', '/img/logo.jpg',
        '/static/logo.svg', '/static/logo.png', '/static/logo.jpg',
        '/public/logo.svg', '/public/logo.png', '/public/logo.jpg'
    ];

    foreach ($priority_paths as $path) {
        $priority_candidates[] = $base_url . $path;
    }

    // 4. 添加常见的favicon路径（普通优先级）
    $regular_paths = [
        '/favicon.ico', '/favicon.png', '/favicon.svg',
        '/apple-touch-icon.png', '/apple-touch-icon-precomposed.png'
    ];

    foreach ($regular_paths as $path) {
        $regular_candidates[] = $base_url . $path;
    }

    // 5. 合并候选列表，优先级候选在前
    $logo_candidates = array_merge(
        array_unique($priority_candidates),
        array_unique($regular_candidates)
    );

    $logo_candidates = array_unique($logo_candidates);

    // 尝试下载LOGO - 智能选择最佳logo
    $logo_found = false;
    $logo_info = null;
    $logo_content = null;
    $logo_extension = 'jpg';
    $best_logo = null;
    $best_score = 0;

    foreach ($logo_candidates as $index => $logo_url) {
        $logo_content = @file_get_contents($logo_url, false, $context);

        if ($logo_content !== false && strlen($logo_content) > 100) { // 至少100字节
            $current_logo = null;
            $score = 0;

            // 检查是否是SVG
            if (stripos($logo_url, '.svg') !== false || strpos($logo_content, '<svg') !== false) {
                $current_logo = [
                    'content' => $logo_content,
                    'extension' => 'svg',
                    'width' => 100,
                    'height' => 100,
                    'mime' => 'image/svg+xml',
                    'url' => $logo_url,
                    'is_svg' => true
                ];
                $score += 20; // SVG格式加分
            } else {
                // 检查其他图片格式
                $temp_file = tempnam(sys_get_temp_dir(), 'logo');
                file_put_contents($temp_file, $logo_content);
                $img_info = @getimagesize($temp_file);
                unlink($temp_file);

                if ($img_info !== false) {
                    $mime_to_ext = [
                        'image/jpeg' => 'jpg',
                        'image/png' => 'png',
                        'image/gif' => 'gif',
                        'image/x-icon' => 'ico',
                        'image/webp' => 'webp'
                    ];
                    $ext = $mime_to_ext[$img_info['mime']] ?? 'jpg';

                    $current_logo = [
                        'content' => $logo_content,
                        'extension' => $ext,
                        'width' => $img_info[0],
                        'height' => $img_info[1],
                        'mime' => $img_info['mime'],
                        'url' => $logo_url,
                        'is_svg' => false
                    ];

                    // 根据图片质量评分
                    $width = $img_info[0];
                    $height = $img_info[1];

                    // 尺寸评分
                    if ($width >= 200 && $height >= 200) $score += 30;
                    elseif ($width >= 100 && $height >= 100) $score += 20;
                    elseif ($width >= 64 && $height >= 64) $score += 10;

                    // 格式评分
                    if ($ext === 'png') $score += 15;
                    elseif ($ext === 'jpg' || $ext === 'jpeg') $score += 10;
                    elseif ($ext === 'webp') $score += 12;

                    // 文件大小评分
                    $size = strlen($logo_content);
                    if ($size > 10000) $score += 10; // 大于10KB
                    elseif ($size > 5000) $score += 5; // 大于5KB
                }
            }

            if ($current_logo) {
                // URL路径评分
                if (stripos($logo_url, 'logo') !== false) $score += 50;
                if (stripos($logo_url, 'brand') !== false) $score += 30;
                if (stripos($logo_url, 'assets') !== false) $score += 10;
                if (stripos($logo_url, 'images') !== false) $score += 10;

                // 优先级位置评分（越靠前分数越高）
                $score += max(0, 100 - $index * 2);

                // 如果是第一个找到的logo或者分数更高，则更新最佳选择
                if (!$best_logo || $score > $best_score) {
                    $best_logo = $current_logo;
                    $best_score = $score;
                }

                // 如果找到高质量logo（分数>80），直接使用
                if ($score > 80) {
                    break;
                }
            }
        }
    }

    if ($best_logo) {
        $logo_found = true;
        $logo_info = $best_logo;
        $logo_content = $best_logo['content'];
        $logo_extension = $best_logo['extension'];
    }

    if (!$logo_found || !$logo_content) {
        $debug_info = [
            'total_candidates' => count($logo_candidates),
            'priority_candidates' => count($priority_candidates),
            'regular_candidates' => count($regular_candidates),
            'tried_urls' => array_slice($logo_candidates, 0, 5),
            'best_score' => $best_score
        ];
        sendError('无法获取网站LOGO，已尝试 ' . count($logo_candidates) . ' 个候选URL', $debug_info);
    }

    // 保存图片到本地
    $filename = $domain . '.' . $logo_extension;
    $save_path = $upload_dir . $filename;
    $input_path = 'website/' . $filename;
    $preview_path = 'uploads/website/' . $filename;

    if (!file_put_contents($save_path, $logo_content)) {
        sendError('无法保存LOGO到服务器');
    }

    // 返回成功响应
    sendSuccess([
        'logo_path' => $input_path,
        'preview_path' => $preview_path,
        'width' => $logo_info['width'],
        'height' => $logo_info['height'],
        'is_svg' => $logo_info['is_svg'],
        'source_url' => $logo_info['url'],
        'file_size' => strlen($logo_content),
        'mime_type' => $logo_info['mime'],
        'quality_score' => $best_score,
        'total_candidates' => count($logo_candidates),
        'is_priority' => stripos($logo_info['url'], 'logo') !== false
    ]);

} catch (Exception $e) {
    sendError('服务器错误: ' . $e->getMessage());
} catch (Error $e) {
    sendError('PHP错误: ' . $e->getMessage());
}

// 辅助函数
function resolveUrl($url, $base_url) {
    if (preg_match('/^https?:\/\//', $url)) {
        return $url;
    } elseif (substr($url, 0, 1) === '/') {
        $parsed = parse_url($base_url);
        return $parsed['scheme'] . '://' . $parsed['host'] . $url;
    } else {
        return rtrim($base_url, '/') . '/' . $url;
    }
}

// 清理输出缓冲区
ob_end_clean();
?>