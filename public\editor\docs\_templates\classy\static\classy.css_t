/*
* flasky.css_t
* ~~~~~~~~~~~~
*
* Sphinx stylesheet -- flasky theme based on nature theme.
*
* :copyright: Copyright 2007-2010 by the Sphinx team, see AUTHORS.
* :license: BSD, see LICENSE for details.
*
*/

@import url("basic.css");

/* -- page layout ----------------------------------------------------------- */

body {
	font-family:"sans serif",tahoma,verdana,helvetica;
	font-size: 14px;
	color: #000;
	background: white;
	margin: 0;
	padding: 0;
}

div.documentwrapper {
	float: left;
	width: 100%;
}

div.bodywrapper {
	margin: 40px auto 0 auto;
	width: 950px;
}

hr {
	border: 1px solid #B1B4B6;
}

div.body {
	background-color: #ffffff;
	color: #3E4349;
	padding: 0 0 30px 0;
}

img.floatingflask {
	padding: 0 0 10px 10px;
	float: right;
}

div.footer {
	text-align: right;
	color: #888;
	padding: 10px;
	font-size: 14px;
	width: 950px;
	margin: 0 auto 40px auto;
}

div.footer a {
	color: #888;
	text-decoration: underline;
}

div.related {
	line-height: 32px;
	font-size: 14px;
	color: #888;
	width: 950px;
	margin: 0 auto;
}

div.related ul {
	padding: 0;
}

/* -- body styles ----------------------------------------------------------- */

a {
	color: #1870a9;
	text-decoration: none;
}

a:hover {
	color: #BC2A4D;
}

div.body {
	padding-bottom: 40px; /* saved for footer */
}

div.body h1,
div.body h2,
div.body h3,
div.body h4,
div.body h5,
div.body h6 {
	font-family:"sans serif",tahoma,verdana,helvetica;
	font-weight: bold;
	margin: 30px 0px 10px 0px;
	padding: 0;
}

#classy-classes-for-javascript h1 {
	text-indent: -999999px;
	background: url(classyjs.png) no-repeat center;
	height: 460px;
	margin: 0;
}

div.body h1 { font-size: 24px; }
div.body h2 { font-size: 20px; }
div.body h3 { font-size: 18px; }
div.body h4 { font-size: 14px; }
div.body h5 { font-size: 14px; }
div.body h6 { font-size: 14px; }

a.headerlink {
	color: white;
	padding: 0 4px;
	text-decoration: none;
}

a.headerlink:hover {
	color: #444;
	background: #eaeaea;
}

div.body p, div.body dd, div.body li {
	line-height: 1.4em;
}

div.admonition {
	background: #fafafa;
	margin: 20px 0;
	padding: 10px;
	border-top: 1px solid #ccc;
	border-bottom: 1px solid #ccc;
}

div.admonition p.admonition-title {
	font-family:"sans serif",tahoma,verdana,helvetica;
	font-weight: normal;
	font-size: 18px;
	margin: 0 0 10px 0;
	padding: 0;
	line-height: 1;
}

div.admonition p.last {
	margin-bottom: 0;
}

div.highlight{
	background-color: white;
}

dt:target, .highlight {
	background: #FAF3E8;
}

div.note {
	background-color: #fafafa;
	border: 1px solid #ccc;
}

div.seealso {
	background-color: #ffc;
	border: 1px solid #ff6;
}

div.topic {
	background-color: #fafafa;
	border: 1px solid #ccc;
	margin: 10px 0;
	padding: 7px;
}

div.warning {
	background-color: #ffe4e4;
	border: 1px solid #f66;
}

p.admonition-title {
	display: inline;
}

p.admonition-title:after {
	content: ":";
}

pre, tt {
	font-family: "Consolas", "Monaco", "Deja Vu Sans Mono", "Courier New", Courier, monospace;
	font-size: 14px;
}

img.screenshot {
}

tt.descname, tt.descclassname {
	font-size: 14px;
}

tt.descname {
	padding-right: 0.08em;
}

img.screenshot {
	-moz-box-shadow: 2px 2px 4px #eee;
	-webkit-box-shadow: 2px 2px 4px #eee;
	box-shadow: 2px 2px 4px #eee;
}

table.docutils {
	border: 1px solid #888;
	-moz-box-shadow: 2px 2px 4px #eee;
	-webkit-box-shadow: 2px 2px 4px #eee;
	box-shadow: 2px 2px 4px #eee;
}

table.docutils td, table.docutils th {
	border: 1px solid #888;
	padding: 0.25em 0.7em;
}

table.field-list, table.footnote {
	border: none;
	-moz-box-shadow: none;
	-webkit-box-shadow: none;
	box-shadow: none;
}

table.footnote {
	margin: 15px 0;
	width: 100%;
	border: 1px solid #eee;
}

table.field-list th {
	padding: 0 0.8em 0 0;
	font-weight: normal;
}

table.field-list td {
	padding: 0;
}

table.field-list strong {
	font-weight: normal;
}

table.footnote td {
	padding: 0.5em;
}

dl {
	margin: 0;
	padding: 0;
}

dl dd {
	margin-left: 30px;
}

pre {
	padding: 0;
	margin: 15px 0;
	padding: 8px;
	line-height: 1.3em;
	border: 1px solid #A4D4EC;
	background: #E9F5FC;
	border-radius: 2px;
	-moz-border-radius: 2px;
	-webkit-border-radius: 2px;
}

tt {
	background-color: #ecf0f3;
	color: #222;
	/* padding: 1px 2px; */
}

tt.xref, a tt {
	background-color: #FBFBFB;
}

a:hover tt {
	background: #EEE;
}
