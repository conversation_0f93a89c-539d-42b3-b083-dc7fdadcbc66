/* VIP专用样式文件 */

/* VIP头部样式 */
.vip-header {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 50%, #ffd700 100%);
    color: #333;
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.4);
    border: 3px solid #ffed4e;
}

.vip-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 15px,
        rgba(255, 255, 255, 0.1) 15px,
        rgba(255, 255, 255, 0.1) 30px
    );
    animation: shimmer 4s linear infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* VIP皇冠动画 */
.vip-crown {
    font-size: 52px;
    margin-bottom: 15px;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.3);
    animation: bounce 2.5s ease-in-out infinite;
    display: inline-block;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-12px) scale(1.1); }
    60% { transform: translateY(-6px) scale(1.05); }
}

/* VIP标题样式 */
.vip-title {
    font-size: 26px;
    font-weight: bold;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    z-index: 10;
    position: relative;
    background: linear-gradient(45deg, #333, #555);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.vip-subtitle {
    font-size: 18px;
    margin: 10px 0 0 0;
    opacity: 0.9;
    z-index: 10;
    position: relative;
    font-weight: 500;
}

/* VIP徽章样式 */
.vip-badge {
    display: inline-block;
    background: linear-gradient(45deg, #ff6b35, #ff8c42);
    color: white;
    padding: 10px 20px;
    border-radius: 30px;
    font-size: 16px;
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
    animation: glow 2.5s ease-in-out infinite alternate;
    position: relative;
    overflow: hidden;
}

.vip-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: slide 3s infinite;
}

@keyframes slide {
    0% { left: -100%; }
    50% { left: 100%; }
    100% { left: 100%; }
}

@keyframes glow {
    from { 
        box-shadow: 0 6px 20px rgba(255, 107, 53, 0.5);
        transform: scale(1);
    }
    to { 
        box-shadow: 0 8px 30px rgba(255, 107, 53, 0.8);
        transform: scale(1.05);
    }
}

/* VIP内容容器 */
.vip-content {
    background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    border: 4px solid #ffd700;
    border-radius: 20px;
    padding: 30px;
    margin-bottom: 25px;
    box-shadow: 0 15px 40px rgba(0,0,0,0.1);
    position: relative;
}

.vip-content::before {
    content: '👑';
    position: absolute;
    top: -20px;
    right: 25px;
    font-size: 36px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    padding: 8px 12px;
    border-radius: 50%;
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
    animation: rotate 4s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* VIP统计数据网格 */
.vip-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 20px;
    margin: 25px 0;
}

.vip-stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.vip-stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s;
}

.vip-stat-item:hover::before {
    transform: translateX(100%);
}

.vip-stat-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
}

.vip-stat-number {
    font-size: 28px;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.vip-stat-label {
    font-size: 13px;
    opacity: 0.9;
    margin-top: 8px;
    font-weight: 500;
}

/* VIP访问按钮 */
.vip-access-button {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    padding: 18px 35px;
    border: none;
    border-radius: 50px;
    font-size: 18px;
    font-weight: bold;
    text-decoration: none;
    display: inline-block;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.5);
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.vip-access-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.vip-access-button:hover::before {
    left: 100%;
}

.vip-access-button:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 12px 35px rgba(40, 167, 69, 0.7);
    color: white;
    text-decoration: none;
}

/* VIP特权功能区 */
.vip-features {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    margin: 25px 0;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.vip-features h3 {
    color: #ffd700;
    margin-bottom: 20px;
    text-align: center;
    font-size: 20px;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.vip-feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.vip-feature-list li {
    padding: 12px 0;
    border-bottom: 1px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
    font-size: 14px;
    line-height: 1.6;
}

.vip-feature-list li:last-child {
    border-bottom: none;
}

.vip-feature-list li:hover {
    padding-left: 10px;
    background: rgba(255,255,255,0.1);
    border-radius: 5px;
}

.vip-feature-list li:before {
    content: '✨';
    margin-right: 12px;
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .vip-header {
        padding: 20px 15px;
    }
    
    .vip-crown {
        font-size: 40px;
    }
    
    .vip-title {
        font-size: 20px;
    }
    
    .vip-subtitle {
        font-size: 14px;
    }
    
    .vip-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }
    
    .vip-stat-item {
        padding: 15px;
    }
    
    .vip-stat-number {
        font-size: 22px;
    }
    
    .vip-access-button {
        padding: 15px 25px;
        font-size: 16px;
    }
    
    .vip-content {
        padding: 20px;
    }
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
    .vip-crown,
    .vip-badge,
    .vip-content::before {
        animation: none;
    }
    
    .vip-stat-item:hover {
        transform: none;
    }
    
    .vip-access-button:hover {
        transform: none;
    }
}
