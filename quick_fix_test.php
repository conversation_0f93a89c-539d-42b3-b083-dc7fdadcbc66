<?php
/**
 * 快速测试修复效果
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

// 如果是测试修复后的函数
if (isset($_GET['test'])) {
    try {
        get_website_sitemap(0);
    } catch (Exception $e) {
        header("Content-Type: text/plain;");
        echo "Error: " . $e->getMessage();
    }
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>快速修复测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
        .test-link.success { background: #28a745; }
        .test-link.success:hover { background: #218838; }
    </style>
</head>
<body>
    <h1>快速修复测试</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section warning">
        <h2>🔧 已实施的修复</h2>
        <p>基于问题分析，我已经修复了 <code>get_website_sitemap</code> 函数中的URL生成问题：</p>
        <ul>
            <li><strong>移除复杂参数：</strong>不再向 <code>get_website_url</code> 传递 <code>web_name</code> 和 <code>web_url</code> 参数</li>
            <li><strong>使用简化URL：</strong>避免复杂的正则表达式和多字节字符处理</li>
            <li><strong>减少失败点：</strong>降低字符串处理失败的可能性</li>
        </ul>
        <p><strong>修改内容：</strong></p>
        <pre style="background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6;">
// 修改前（可能导致问题）：
$row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));

// 修改后（简化处理）：
$row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true));
        </pre>
    </div>
    
    <div class="test-section">
        <h2>🧪 测试修复效果</h2>
        <p>点击下面的链接测试修复后的站点地图：</p>
        
        <div style="margin: 15px 0;">
            <strong>1. 本地测试（修复后的函数）：</strong><br>
            <a href="?test=1" target="_blank" class="test-link">测试修复后的站点地图函数</a>
        </div>
        
        <div style="margin: 15px 0;">
            <strong>2. 在线测试（实际环境）：</strong><br>
            <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link success">测试在线webdir站点地图</a>
            <a href="https://www.95dir.com/?mod=sitemap&type=weblink&format=xml" target="_blank" class="test-link success">测试在线weblink站点地图</a>
        </div>
    </div>
    
    <div class="test-section success">
        <h2>✅ 预期结果</h2>
        <p>如果修复成功，您应该看到：</p>
        <ul>
            <li><strong>完整的XML格式</strong> - 包含开始标签 <code>&lt;?xml</code> 和结束标签 <code>&lt;/urlset&gt;</code></li>
            <li><strong>正确的命名空间</strong> - <code>xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"</code></li>
            <li><strong>网站URL列表</strong> - 包含首页和网站详情页面</li>
            <li><strong>快速响应</strong> - 不再出现超时或中断</li>
        </ul>
    </div>
    
    <div class="test-section error">
        <h2>❌ 如果仍然失败</h2>
        <p>如果问题仍然存在，可能的原因：</p>
        <ul>
            <li><strong>数据库查询问题</strong> - 某个特定的数据记录有问题</li>
            <li><strong>其他函数调用失败</strong> - 除了URL生成之外的其他问题</li>
            <li><strong>服务器配置问题</strong> - 内存、超时等限制</li>
        </ul>
        <p>请使用以下工具进一步诊断：</p>
        <a href="test_url_generation.php" target="_blank" class="test-link">URL生成诊断工具</a>
        <a href="diagnose_sitemap_failure.php" target="_blank" class="test-link">详细诊断工具</a>
    </div>
    
    <div class="test-section info">
        <h2>📊 技术说明</h2>
        <p><strong>问题根源：</strong></p>
        <p>原始的 <code>get_website_url</code> 函数在接收到 <code>web_name</code> 和 <code>web_url</code> 参数时，会执行复杂的字符串处理：</p>
        <ul>
            <li>使用正则表达式 <code>preg_replace('/[^\w\x{4e00}-\x{9fa5}]/u', '', $web_name)</code> 清理网站名称</li>
            <li>使用 <code>mb_substr</code> 截取字符串长度</li>
            <li>处理域名和特殊字符</li>
        </ul>
        <p>当某些网站名称包含特殊字符或编码问题时，这些处理可能失败并导致脚本中断。</p>
        
        <p><strong>修复方案：</strong></p>
        <p>通过只传递 <code>web_id</code> 和 <code>abs_path</code> 参数，函数会使用简单的URL结构，避免复杂的字符串处理，从而提高稳定性。</p>
    </div>
</body>
</html>
