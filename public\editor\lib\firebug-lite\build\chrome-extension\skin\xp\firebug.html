<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/DTD/strict.dtd">
<html>
<head>
<meta http-equiv="content-type" content="text/html; charset=utf-8">
<title>Firebug Lite</title>
<link href="firebug.css" rel="stylesheet" type="text/css" />
<style>html,body{margin:0;padding:0;overflow:hidden;}</style>
</head>
<body class="fbBody">
<table id="fbChrome" cellpadding="0" cellspacing="0" border="0">
  <tbody>
    <tr>
      <!-- Interface - Top Area -->
      <td id="fbTop" colspan="2">
      
        <!-- 
        <div>
          --><!-- <span id="fbToolbarErrors" class="fbErrors">2 errors</span> --><!-- 
          <input type="text" id="fbToolbarSearch" />
        </div>
        -->
              
        <!-- Window Buttons -->
        <div id="fbWindowButtons">
          <a id="fbWindow_btDeactivate" class="fbSmallButton fbHover" title="Deactivate Firebug for this web page">&nbsp;</a>
          <a id="fbWindow_btDetach" class="fbSmallButton fbHover" title="Open Firebug in popup window">&nbsp;</a>
          <a id="fbWindow_btClose" class="fbSmallButton fbHover" title="Minimize Firebug">&nbsp;</a>
        </div>
        
        <!-- Toolbar buttons and Status Bar -->
        <div id="fbToolbar">
          <div id="fbToolbarContent">
        
          <!-- Firebug Button -->
          <span id="fbToolbarIcon">
            <a id="fbFirebugButton" class="fbIconButton" class="fbHover" target="_blank">&nbsp;</a>
          </span>
          
          <!-- 
          <span id="fbLeftToolbarErrors" class="fbErrors">2 errors</span>
           -->
           
          <!-- Toolbar Buttons -->
          <span id="fbToolbarButtons">
            <!-- Fixed Toolbar Buttons -->
            <span id="fbFixedButtons">
                <a id="fbChrome_btInspect" class="fbButton fbHover" title="Click an element in the page to inspect">Inspect</a>
            </span>
            
            <!-- Console Panel Toolbar Buttons -->
            <span id="fbConsoleButtons" class="fbToolbarButtons">
              <a id="fbConsole_btClear" class="fbButton fbHover" title="Clear the console">Clear</a>
            </span>
            
            <!-- HTML Panel Toolbar Buttons -->
            <!-- 
            <span id="fbHTMLButtons" class="fbToolbarButtons">
              <a id="fbHTML_btEdit" class="fbHover" title="Edit this HTML">Edit</a>
            </span>
             -->
          </span>
          
          <!-- Status Bar -->
          <span id="fbStatusBarBox">
            <span class="fbToolbarSeparator"></span>
            <!-- HTML Panel Status Bar -->
            <!-- 
            <span id="fbHTMLStatusBar" class="fbStatusBar fbToolbarButtons">
            </span>
             -->
          </span>
          
          </div>
          
        </div>
        
        <!-- PanelBars -->
        <div id="fbPanelBarBox">
        
          <!-- Main PanelBar -->
          <div id="fbPanelBar1" class="fbPanelBar">
            <a id="fbConsoleTab" class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">Console</span>
                <span class="fbTabMenuTarget"></span>
                <span class="fbTabR"></span>
            </a>
            <a id="fbHTMLTab" class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">HTML</span>
                <span class="fbTabR"></span>
            </a>
            <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">CSS</span>
                <span class="fbTabR"></span>
            </a>
            <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">Script</span>
                <span class="fbTabR"></span>
            </a>
            <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">DOM</span>
                <span class="fbTabR"></span>
            </a>
          </div>

          <!-- Side PanelBars -->
          <div id="fbPanelBar2Box" class="hide">
            <div id="fbPanelBar2" class="fbPanelBar">
            <!-- 
              <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">Style</span>
                <span class="fbTabR"></span>
              </a>
              <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">Layout</span>
                <span class="fbTabR"></span>
              </a>
              <a class="fbTab fbHover">
                <span class="fbTabL"></span>
                <span class="fbTabText">DOM</span>
                <span class="fbTabR"></span>
              </a>
           -->
            </div>
          </div>
          
        </div>
        
        <!-- Horizontal Splitter -->
        <div id="fbHSplitter">&nbsp;</div>
        
      </td>
    </tr>
    
    <!-- Interface - Main Area -->
    <tr id="fbContent">
    
      <!-- Panels  -->
      <td id="fbPanelBox1">
        <div id="fbPanel1" class="fbFitHeight">
          <div id="fbConsole" class="fbPanel"></div>
          <div id="fbHTML" class="fbPanel"></div>
        </div>
      </td>
      
      <!-- Side Panel Box -->
      <td id="fbPanelBox2" class="hide">
      
        <!-- VerticalSplitter -->
        <div id="fbVSplitter" class="fbVSplitter">&nbsp;</div>
        
        <!-- Side Panels -->
        <div id="fbPanel2" class="fbFitHeight">
        
          <!-- HTML Side Panels -->
          <div id="fbHTML_Style" class="fbPanel"></div>
          <div id="fbHTML_Layout" class="fbPanel"></div>
          <div id="fbHTML_DOM" class="fbPanel"></div>
          
        </div>
        
        <!-- Large Command Line -->
        <textarea id="fbLargeCommandLine" class="fbFitHeight"></textarea>
        
        <!-- Large Command Line Buttons -->
        <div id="fbLargeCommandButtons">
            <a id="fbCommand_btRun" class="fbButton fbHover">Run</a>
            <a id="fbCommand_btClear" class="fbButton fbHover">Clear</a>
            
            <a id="fbSmallCommandLineIcon" class="fbSmallButton fbHover"></a>
        </div>
        
      </td>
      
    </tr>
    
    <!-- Interface - Bottom Area -->
    <tr id="fbBottom" class="hide">
    
      <!-- Command Line -->
      <td id="fbCommand" colspan="2">
        <div id="fbCommandBox">
          <div id="fbCommandIcon">&gt;&gt;&gt;</div>
          <input id="fbCommandLine" name="fbCommandLine" type="text" />
          <a id="fbLargeCommandLineIcon" class="fbSmallButton fbHover"></a>
        </div>
      </td>
      
    </tr>
    
  </tbody>
</table> 
<span id="fbMiniChrome">
  <span id="fbMiniContent">
    <span id="fbMiniIcon" title="Open Firebug Lite"></span>
    <span id="fbMiniErrors" class="fbErrors">2 errors</span>
  </span>
</span>
<!-- 
<div id="fbErrorPopup">
  <div id="fbErrorPopupContent">
    <div id="fbErrorIndicator" class="fbErrors">2 errors</div>
  </div>
</div>
 -->
</body>
</html>