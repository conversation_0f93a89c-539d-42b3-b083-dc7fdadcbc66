<?php
require('common.php');

// 检查管理员权限
if (!$myself['user_id']) {
    exit('Access Denied');
}

$action = trim($_GET['action']);

if ($action == 'run') {
    // 手动执行链接检测
    echo "<h2>手动执行友情链接检测</h2>";
    
    // 检查配置
    if (!isset($options['is_enabled_linkcheck']) || $options['is_enabled_linkcheck'] != 'yes') {
        echo "<p style='color: red;'>链接检测功能未启用，请先在系统设置中启用。</p>";
        echo "<p><a href='option.php'>前往系统设置</a></p>";
        exit;
    }
    
    // 引入链接检测函数
    require_once('../module/getdata.php');
    
    echo "<p>检测配置：</p>";
    echo "<ul>";
    echo "<li>检测域名：" . $options['check_link_url'] . "</li>";
    echo "<li>检测名称：" . $options['check_link_name'] . "</li>";
    echo "<li>更新周期：" . $options['data_update_cycle'] . " 天</li>";
    echo "</ul>";
    
    $batch_size = intval($_GET['limit']) ?: 10;
    echo "<p>本次检测数量：$batch_size 个网站</p>";
    
    // 获取需要检测的网站
    $table = $DB->table('websites');
    $sql = "SELECT web_id, web_name, web_url, web_islink 
            FROM $table 
            WHERE web_status=3 AND web_ispay=0 
            ORDER BY web_id DESC 
            LIMIT $batch_size";
    
    $query = $DB->query($sql);
    $websites = array();
    while ($row = $DB->fetch_array($query)) {
        $websites[] = $row;
    }
    
    if (empty($websites)) {
        echo "<p>没有需要检测的网站</p>";
        exit;
    }
    
    echo "<h3>检测结果：</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>序号</th><th>网站名称</th><th>网站URL</th><th>原状态</th><th>新状态</th><th>检测详情</th></tr>";
    
    $checked_count = 0;
    $updated_count = 0;
    
    foreach ($websites as $website) {
        $checked_count++;
        
        echo "<tr>";
        echo "<td>$checked_count</td>";
        echo "<td>" . htmlspecialchars($website['web_name']) . "</td>";
        echo "<td><a href='" . $website['web_url'] . "' target='_blank'>" . htmlspecialchars($website['web_url']) . "</a></td>";
        
        $old_status_text = $website['web_islink'] ? '未链接' : '已链接';
        echo "<td style='color: " . ($website['web_islink'] ? 'red' : 'green') . ";'>$old_status_text</td>";
        
        try {
            // 执行链接检测
            $result = smart_check_website_link($website['web_url'], $options);
            
            $old_status = $website['web_islink'];
            $new_status = $result['has_link'] ? 0 : 1;
            
            $new_status_text = $new_status ? '未链接' : '已链接';
            $status_color = $new_status ? 'red' : 'green';
            
            echo "<td style='color: $status_color;'>$new_status_text</td>";
            
            // 如果状态发生变化，更新数据库
            if ($old_status != $new_status) {
                $DB->query("UPDATE $table SET web_islink=$new_status, web_utime=" . time() . " WHERE web_id=" . $website['web_id']);
                $updated_count++;
                echo "<td style='background: #ffffcc;'><strong>状态已更新</strong><br>" . htmlspecialchars($result['details']) . "</td>";
            } else {
                // 更新检测时间
                $DB->query("UPDATE $table SET web_utime=" . time() . " WHERE web_id=" . $website['web_id']);
                echo "<td>" . htmlspecialchars($result['details']) . "</td>";
            }
            
        } catch (Exception $e) {
            echo "<td style='color: red;'>检测出错：" . htmlspecialchars($e->getMessage()) . "</td>";
        }
        
        echo "</tr>";
        
        // 刷新输出缓冲区，实时显示结果
        if (ob_get_level()) {
            ob_flush();
        }
        flush();
        
        // 避免请求过于频繁
        usleep(500000); // 0.5秒延迟
    }
    
    echo "</table>";
    
    echo "<h3>检测统计：</h3>";
    echo "<ul>";
    echo "<li>检测网站数：$checked_count</li>";
    echo "<li>状态更新数：$updated_count</li>";
    echo "</ul>";
    
    // 获取当前统计
    $stats = array();
    $stats['total'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3");
    $stats['linked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_islink=0");
    $stats['unlinked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_islink=1");
    
    echo "<h3>当前链接状态统计：</h3>";
    echo "<ul>";
    echo "<li>已审核网站总数：" . $stats['total'] . "</li>";
    echo "<li>已链接网站数：" . $stats['linked'] . "</li>";
    echo "<li>未链接网站数：" . $stats['unlinked'] . "</li>";
    echo "</ul>";
    
    echo "<p><a href='manual_linkcheck.php'>返回</a> | <a href='linkcheck.php'>链接检测管理</a></p>";
    
} else {
    // 显示操作界面
    echo "<h2>手动友情链接检测</h2>";
    
    echo "<p>此工具用于手动执行友情链接检测，可以立即检测指定数量的网站。</p>";
    
    echo "<form method='get' action='manual_linkcheck.php'>";
    echo "<input type='hidden' name='action' value='run'>";
    echo "<p>";
    echo "检测数量：<select name='limit'>";
    echo "<option value='5'>5个网站</option>";
    echo "<option value='10' selected>10个网站</option>";
    echo "<option value='20'>20个网站</option>";
    echo "<option value='50'>50个网站</option>";
    echo "</select>";
    echo "</p>";
    echo "<p><input type='submit' value='开始检测' class='btn' onclick='this.disabled=true; this.value=\"检测中...\"; this.form.submit();'></p>";
    echo "</form>";
    
    echo "<h3>注意事项：</h3>";
    echo "<ul>";
    echo "<li>检测过程可能需要较长时间，请耐心等待</li>";
    echo "<li>检测会实时显示结果，请不要关闭页面</li>";
    echo "<li>建议先测试少量网站，确认功能正常后再批量检测</li>";
    echo "<li>付费网站不会被检测</li>";
    echo "</ul>";
    
    echo "<p><a href='linkcheck.php'>返回链接检测管理</a></p>";
}
?>
