<?php
/**
 * 最小化站点地图测试
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');

// 创建一个简化版本的get_website_sitemap函数
function test_minimal_sitemap() {
    global $DB, $options;
    
    header("Content-Type: application/xml;");
    echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 首页
    echo "<url>\n";
    echo "<loc>".$options['site_url']."</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>1.0</priority>\n";
    echo "</url>\n";
    
    // 只添加几个基本页面
    $basic_pages = array(
        '?mod=webdir' => array('priority' => '0.9', 'changefreq' => 'daily'),
        '?mod=article' => array('priority' => '0.8', 'changefreq' => 'daily'),
        '?mod=category' => array('priority' => '0.8', 'changefreq' => 'weekly'),
    );
    
    foreach ($basic_pages as $url => $info) {
        echo "<url>\n";
        echo "<loc>".$options['site_url'].$url."</loc>\n";
        echo "<lastmod>".date('c')."</lastmod>\n";
        echo "<changefreq>".$info['changefreq']."</changefreq>\n";
        echo "<priority>".$info['priority']."</priority>\n";
        echo "</url>\n";
    }
    
    // 尝试添加少量网站数据
    try {
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
        $sql .= " WHERE web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0) ORDER BY web_id DESC LIMIT 5";
        $query = $DB->query($sql);
        
        if ($query) {
            while ($row = $DB->fetch_array($query)) {
                try {
                    $web_link = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
                    echo "<url>\n";
                    echo "<loc>".$web_link."</loc>\n";
                    echo "<lastmod>".date('c', $row['web_ctime'])."</lastmod>\n";
                    echo "<changefreq>weekly</changefreq>\n";
                    echo "<priority>0.6</priority>\n";
                    echo "</url>\n";
                } catch (Exception $e) {
                    // 跳过有问题的网站
                    continue;
                }
            }
            $DB->free_result($query);
        }
    } catch (Exception $e) {
        // 如果网站查询失败，跳过
    }
    
    echo "</urlset>\n";
}

// 如果是直接访问，生成站点地图
if (isset($_GET['generate'])) {
    test_minimal_sitemap();
    exit;
}

// 否则显示测试页面
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>最小化站点地图测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>最小化站点地图测试</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section info">
        <h2>测试说明</h2>
        <p>这是一个简化版本的站点地图测试，用于排查问题。</p>
        <p>点击下面的链接生成和查看最小化站点地图：</p>
        <a href="?generate=1" target="_blank" class="test-link">生成最小化站点地图</a>
        <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link">测试原始webdir站点地图</a>
    </div>
    
    <div class="test-section">
        <h2>数据库状态检查</h2>
        <?php
        try {
            $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
            $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
            $category_count = $DB->get_count($DB->table('categories'), "1=1");
            
            echo "<div class='success'>\n";
            echo "<p>✓ 数据库连接正常</p>\n";
            echo "<p>正常网站数量: $website_count</p>\n";
            echo "<p>正常文章数量: $article_count</p>\n";
            echo "<p>分类数量: $category_count</p>\n";
            echo "</div>\n";
        } catch (Exception $e) {
            echo "<div class='error'>\n";
            echo "<p>✗ 数据库连接失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            echo "</div>\n";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>函数检查</h2>
        <?php
        $functions = ['get_website_url', 'get_article_url', 'get_category_url', 'datediff'];
        $all_ok = true;
        
        foreach ($functions as $func) {
            if (function_exists($func)) {
                echo "<p style='color: green;'>✓ $func 函数存在</p>\n";
            } else {
                echo "<p style='color: red;'>✗ $func 函数不存在</p>\n";
                $all_ok = false;
            }
        }
        
        if ($all_ok) {
            echo "<div class='success'><p>✓ 所有必要函数都存在</p></div>\n";
        } else {
            echo "<div class='error'><p>✗ 部分函数缺失</p></div>\n";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>测试网站URL生成</h2>
        <?php
        try {
            $sql = "SELECT web_id, web_name, web_url FROM ".$DB->table('websites')." WHERE web_status=3 LIMIT 3";
            $query = $DB->query($sql);
            
            if ($query) {
                echo "<div class='success'>\n";
                echo "<p>✓ 网站数据查询成功</p>\n";
                echo "<h4>示例URL:</h4>\n";
                
                while ($row = $DB->fetch_array($query)) {
                    try {
                        $web_link = get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']);
                        echo "<p>ID: {$row['web_id']} - <a href='$web_link' target='_blank'>$web_link</a></p>\n";
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>ID: {$row['web_id']} - URL生成失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
                    }
                }
                echo "</div>\n";
                $DB->free_result($query);
            } else {
                echo "<div class='error'><p>✗ 网站数据查询失败</p></div>\n";
            }
        } catch (Exception $e) {
            echo "<div class='error'><p>✗ 查询异常: " . htmlspecialchars($e->getMessage()) . "</p></div>\n";
        }
        ?>
    </div>
    
    <div class="test-section info">
        <h2>下一步</h2>
        <p>如果最小化站点地图能正常生成，说明基本功能正常，问题可能出现在：</p>
        <ul>
            <li>数据量过大导致超时</li>
            <li>某个特定的数据记录有问题</li>
            <li>某个循环中的函数调用失败</li>
            <li>内存不足</li>
        </ul>
        <p>建议逐步增加数据量，找出具体的问题点。</p>
    </div>
</body>
</html>
