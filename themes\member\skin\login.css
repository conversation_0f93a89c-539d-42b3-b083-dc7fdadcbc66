@charset "utf-8";
@import url("reset.css");

/* 基础样式 */
* {
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

/* custom */
body {background: url(hbg.png) repeat-x top;}
/* clearfix */
.clearfix:after {clear: both; content: "."; display: block; height: 0; visibility: hidden;}
.clearfix {display: block;}
/* hides from IE-mac \*/
.clearfix {height: 1%;}
.clearfix {display: block;}
/* input, button */
.ipt {
    background: url(ipt.png) no-repeat;
    border: solid 1px #c9c9c9;
    font: 14px normal;
    padding: 6px;
    width: 100%;
    max-width: 300px;
}
.btn {
    background: #3c8ece;
    border: solid 1px #2672bc;
    color: #fff;
    padding: 8px 20px;
    cursor: pointer;
    border-radius: 3px;
}
.btn:hover {
    background: #2b7ab8;
}
a {color: #03c; text-decoration: none;}
a:hover {color: #f30; text-decoration: underline;}
#wrapper {margin: 0 auto; width: 1200px; padding: 0 15px;}
#header {height: auto; min-height: 80px; padding: 10px 0;}
.logo {background: url(logo.png) center; display: block; float: left; height: 80px; width: 220px;}
#toplink {color: #ccc; float: right; margin: 30px 0;}
#toplink a {color: #2b7ab8;}
/* loginform */
#loginform {border: solid 1px #e8e8e8; padding: 1px; margin: 20px auto; max-width: 600px;}
#loginform h2 {background: url(tbg.png) repeat-x; color: #fff; font: bold 14px normal; padding: 15px;}
#loginform ul {font: 14px normal; margin: 0 auto; padding: 20px; width: 100%; max-width: 450px;}
#loginform li {padding: 12px 0;}
#loginform li label {display: block; float: left; padding-top: 8px; text-align: right; width: 80px; margin-right: 10px;}
#loginform li p {color: #ccc; font-size: 12px; padding: 8px 0 0 90px;}
.tipinfo {border-left: solid 1px #dadada; padding: 20px;}
a.nowlink {
    background: #f60;
    border: solid 1px #f30;
    color: #fff;
    font-size: 14px;
    padding: 8px 20px;
    display: inline-block;
    border-radius: 3px;
}
a.nowlink:hover {
    background: #f50;
    text-decoration: none;
}
/* combox */
#combox {background: #fff;}
/* regform */
#regform {border: solid 1px #e8e8e8; padding: 1px; margin: 20px auto; max-width: 800px;}
#regform h2 {background: url(tbg.png) repeat-x; color: #fff; font: bold 14px normal; padding: 15px;}
#regform ul {font: 14px normal; margin: 0 auto; padding: 20px; width: 100%; max-width: 600px;}
#regform li {padding: 10px 0;}
#regform li label {display: block; float: left; padding-top: 8px; text-align: right; width: 120px; margin-right: 10px;}
#regform li p {color: #999; font-size: 12px; padding: 6px 0 0 130px;}
#regform li span {color: #666; font: 12px normal; padding-left: 5px;}
.textinfo {
    background: #fdfdf4;
    border: dashed 1px #f60;
    line-height: 1.6;
    margin: 10px;
    padding: 15px;
    border-radius: 3px;
}
/* tipbox */
#tipbox {border: solid 1px #e8e8e8; padding: 1px; margin: 20px auto; max-width: 600px;}
#tipbox h2 {background: url(tbg.png) repeat-x; color: #fff; font: bold 14px normal; padding: 15px;}
#tipbox p {font-size: 14px; padding: 50px 20px; text-align: center;}
/* footer */
#footer {background: url(fbg.gif) no-repeat top center; margin-top: 20px; padding: 20px; text-align: center;}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
    #wrapper {
        width: 95%;
    }
}

@media screen and (max-width: 768px) {
    #header {
        text-align: center;
    }
    
    .logo {
        float: none;
        margin: 0 auto;
    }
    
    #toplink {
        float: none;
        margin: 20px 0;
        text-align: center;
    }
    
    #loginform ul,
    #regform ul {
        width: 90%;
    }
    
    #loginform li label,
    #regform li label {
        float: none;
        text-align: left;
        width: 100%;
        margin-bottom: 5px;
    }
    
    #loginform li p,
    #regform li p {
        padding-left: 0;
    }
    
    .tipinfo {
        border-left: none;
        border-top: solid 1px #dadada;
        margin-top: 20px;
    }
}

@media screen and (max-width: 480px) {
    .ipt {
        max-width: 100%;
    }
    
    #loginform,
    #regform,
    #tipbox {
        margin: 10px;
    }
    
    #loginform h2,
    #regform h2,
    #tipbox h2 {
        padding: 10px;
    }
    
    #loginform ul,
    #regform ul {
        padding: 10px;
    }
    
    #tipbox p {
        padding: 30px 15px;
    }
    
    .btn,
    a.nowlink {
        width: 100%;
        text-align: center;
    }
}