<?php
/*
 * 待审核页面模块
 * 显示正在等待审核的网站，不显示网址和跳转链接，保护隐私
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '待审核';
$pageurl = '?mod=pending';
$tempfile = 'pending.html';
$table = $DB->table('websites');

$pagesize = 20;
$curpage = intval(isset($_GET['page']) ? $_GET['page'] : 1);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}

$cate_id = intval(isset($_GET['cid']) ? $_GET['cid'] : 0);
$cache_id = $cate_id.'-'.$curpage;

if ($cate_id > 0) {
	$pageurl .= '&cid='.$cate_id;
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 默认SEO设置
	$seo_title = $pagename . ' - ' . $options['site_name'];
	$seo_keywords = array('待审核网站', '网站审核', '等待审核', '网站提交', '网站收录');
	$seo_description = '展示正在等待审核的网站列表，这些网站正在经过严格的人工审核流程，审核通过后将正式收录到网站目录中。';

	// 构建查询条件 - 只查询待审核状态的网站 (web_status=2)
	$where = "w.web_status=2";

	// 分类筛选
	if ($cate_id > 0) {
		$cate = $DB->fetch_one("SELECT cate_id, root_id, cate_name, cate_arrchildid, cate_childcount FROM ".$DB->table('categories')." WHERE cate_id=$cate_id LIMIT 1");
		if (!$cate) {
			unset($cate);
			redirect('?mod=pending');
		}

		// 优化分类待审核页面SEO
		$seo_title = $cate['cate_name'] . '待审核网站 - ' . $cate['cate_name'] . '网站审核 - ' . $options['site_name'];

		$seo_keywords = array();
		$seo_keywords[] = $cate['cate_name'] . '待审核';
		$seo_keywords[] = $cate['cate_name'] . '网站审核';
		$seo_keywords[] = $cate['cate_name'] . '网站提交';
		$seo_keywords[] = '待审核网站';
		$seo_keywords[] = '网站审核';
		if (!empty($options['site_keywords'])) {
			$site_keywords = explode(',', $options['site_keywords']);
			$seo_keywords = array_merge($seo_keywords, $site_keywords);
		}

		$seo_description = $cate['cate_name'] . '分类待审核网站列表，展示正在等待审核的' . $cate['cate_name'] . '网站。';

		$smarty->assign('site_path', get_sitepath().' &raquo; <a href="'.$pageurl.'">'.$pagename.'</a> &raquo; '.$cate['cate_name']);

		if ($cate['cate_childcount'] > 0) {
			$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND w.cate_id=$cate_id";
		}
	}

	// 获取分类列表
	$categories = $DB->fetch_all("SELECT cate_id, cate_name, cate_postcount FROM ".$DB->table('categories')." WHERE root_id=0 AND cate_mod='webdir' ORDER BY cate_order ASC, cate_id ASC");
	
	// 获取待审核网站列表 - 使用直接SQL查询确保兼容性
	$sql = "SELECT w.web_id, w.web_name, w.web_intro, w.web_ai_intro, w.web_pic, w.web_tags, w.web_ctime, w.cate_id, c.cate_name
			FROM ".$DB->table('websites')." w
			LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id
			WHERE $where
			ORDER BY w.web_ctime DESC
			LIMIT $start, $pagesize";

	$query = $DB->query($sql);
	$websites = array();
	while ($row = $DB->fetch_array($query)) {
		$websites[] = $row;
	}

	// 处理数据，移除网址信息保护隐私
	foreach ($websites as $key => $row) {
		// 处理简介内容，限制长度
		if (!empty($row['web_intro'])) {
			$websites[$key]['web_intro'] = mb_substr(strip_tags($row['web_intro']), 0, 100, 'utf-8');
			if (mb_strlen($websites[$key]['web_intro'], 'utf-8') >= 100) {
				$websites[$key]['web_intro'] .= '...';
			}
		}

		// 处理AI简介
		if (!empty($row['web_ai_intro'])) {
			$websites[$key]['web_ai_intro'] = mb_substr(strip_tags($row['web_ai_intro']), 0, 100, 'utf-8');
			if (mb_strlen($websites[$key]['web_ai_intro'], 'utf-8') >= 100) {
				$websites[$key]['web_ai_intro'] .= '...';
			}
		}

		// 格式化时间
		$websites[$key]['web_ctime'] = date('Y-m-d', $row['web_ctime']);

		// 处理标签 - 不提供搜索链接，只显示
		if (!empty($row['web_tags'])) {
			$tags = explode(',', $row['web_tags']);
			$websites[$key]['web_tags_array'] = array_map('trim', $tags);
		} else {
			$websites[$key]['web_tags_array'] = array();
		}

		// 分类链接
		if (!empty($row['cate_id'])) {
			$websites[$key]['cate_link'] = '?mod=pending&cid='.$row['cate_id'];
		}
	}
	
	// 获取总数
	$total = $DB->get_count($table.' w', $where);

	// 完善SEO描述
	if ($total > 0) {
		$seo_description .= '目前共有' . $total . '个网站等待审核。';
	}
	$seo_description .= $options['site_name'] . '严格审核每个提交的网站，确保收录质量。';

	// 确保描述长度适中
	if (mb_strlen($seo_description, 'UTF-8') > 160) {
		$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
	}

	// 分页
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);

	// 应用SEO设置
	$smarty->assign('site_title', $seo_title);
	$smarty->assign('site_keywords', implode(',', array_unique($seo_keywords)));
	$smarty->assign('site_description', $seo_description);
	$smarty->assign('site_path', get_sitepath().' &raquo; '.$pagename);
	$smarty->assign('site_rss', get_rssfeed());

	// 分配模板变量
	$smarty->assign('pagename', $pagename);
	$smarty->assign('cate_id', $cate_id);
	$smarty->assign('cate_name', isset($cate['cate_name']) ? '"'.$cate['cate_name'].'"待审核网站' : $pagename);
	$smarty->assign('categories', $categories);
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
	
	unset($websites, $categories);
}

smarty_output($tempfile, $cache_id);
?>
