<?php
/**
 * 友情链接检测定时任务
 * 建议设置为每天执行一次：0 2 * * * /usr/bin/php /path/to/cron_linkcheck.php
 */

// 定义常量
define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace("\\", '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');

// 引入核心文件
require(ROOT_PATH.'config.php');
require(ROOT_PATH.'source/init.php');

// 检查是否启用链接检测
if (!isset($options['is_enabled_linkcheck']) || $options['is_enabled_linkcheck'] != 'yes') {
    echo "链接检测功能未启用\n";
    exit;
}

// 检查检测配置
if (empty($options['check_link_url']) || empty($options['check_link_name'])) {
    echo "链接检测配置不完整\n";
    echo "请在后台设置检测域名和检测名称\n";
    exit;
}

// 引入链接检测函数
require_once(ROOT_PATH.'module/getdata.php');

echo "开始执行友情链接检测任务...\n";
echo "检测配置：\n";
echo "- 检测域名：" . $options['check_link_url'] . "\n";
echo "- 检测名称：" . $options['check_link_name'] . "\n";
echo "- 更新周期：" . $options['data_update_cycle'] . " 天\n\n";

// 获取需要检测的网站
$table = $DB->table('websites');
$batch_size = 50; // 每次检测50个网站
$update_cycle = intval($options['data_update_cycle']) ?: 3;

// 查询需要检测的网站（已审核且非付费的网站）
// 修复：正确关联 webdata 表获取 web_utime 字段
$webdata_table = $DB->table('webdata');
$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_islink, COALESCE(d.web_utime, 0) as web_utime
        FROM $table w
        LEFT JOIN $webdata_table d ON w.web_id = d.web_id
        WHERE w.web_status=3 AND w.web_ispay=0
        AND (COALESCE(d.web_utime, 0)=0 OR COALESCE(d.web_utime, 0) < " . (time() - $update_cycle * 24 * 3600) . ")
        ORDER BY COALESCE(d.web_utime, 0) ASC, w.web_id ASC
        LIMIT $batch_size";

$query = $DB->query($sql);
$websites = array();
while ($row = $DB->fetch_array($query)) {
    $websites[] = $row;
}

if (empty($websites)) {
    echo "没有需要检测的网站\n";

    // 显示当前统计
    $stats = array();
    $stats['total'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0");
    $stats['linked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0 AND web_islink=0");
    $stats['unlinked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0 AND web_islink=1");

    echo "\n当前链接状态统计：\n";
    echo "- 可检测网站总数：" . $stats['total'] . "\n";
    echo "- 已链接网站数：" . $stats['linked'] . "\n";
    echo "- 未链接网站数：" . $stats['unlinked'] . "\n";
    echo "\n任务执行时间：" . date('Y-m-d H:i:s') . "\n";
    exit;
}

echo "找到 " . count($websites) . " 个需要检测的网站\n\n";

$checked_count = 0;
$updated_count = 0;
$error_count = 0;
$linked_count = 0;
$unlinked_count = 0;

foreach ($websites as $website) {
    $checked_count++;
    
    echo "[$checked_count/" . count($websites) . "] 检测网站：" . $website['web_name'] . " (" . $website['web_url'] . ")\n";
    
    try {
        // 执行链接检测
        $result = smart_check_website_link($website['web_url'], $options);
        
        $old_status = $website['web_islink'];
        $new_status = $result['has_link'] ? 0 : 1;
        
        // 更新检测时间到 webdata 表
        $current_time = time();
        // 确保 webdata 表中有对应记录
        $DB->query("INSERT IGNORE INTO $webdata_table (web_id, web_utime) VALUES (" . $website['web_id'] . ", $current_time)");
        $DB->query("UPDATE $webdata_table SET web_utime=$current_time WHERE web_id=" . $website['web_id']);
        
        // 如果状态发生变化，更新链接状态
        if ($old_status != $new_status) {
            $DB->query("UPDATE $table SET web_islink=$new_status WHERE web_id=" . $website['web_id']);
            $updated_count++;
            
            $status_text = $new_status ? '未链接' : '已链接';
            echo "  状态变更：" . ($old_status ? '未链接' : '已链接') . " → $status_text\n";
        } else {
            $status_text = $new_status ? '未链接' : '已链接';
            echo "  状态不变：$status_text\n";
        }

        // 统计链接状态
        if ($new_status == 0) {
            $linked_count++;
        } else {
            $unlinked_count++;
        }

        echo "  检测详情：" . $result['details'] . "\n";
        
        if (!empty($result['patterns_found'])) {
            echo "  匹配模式：" . implode('、', $result['patterns_found']) . "\n";
        }
        
    } catch (Exception $e) {
        $error_count++;
        echo "  检测出错：" . $e->getMessage() . "\n";
        
        // 更新检测时间，避免重复检测出错的网站
        $DB->query("UPDATE $table SET web_utime=" . time() . " WHERE web_id=" . $website['web_id']);
    }
    
    echo "\n";
    
    // 避免请求过于频繁
    usleep(500000); // 0.5秒延迟
}

echo "检测任务完成！\n";
echo "统计信息：\n";
echo "- 检测网站数：$checked_count\n";
echo "- 状态更新数：$updated_count\n";
echo "- 检测错误数：$error_count\n";
echo "- 本次已链接：$linked_count\n";
echo "- 本次未链接：$unlinked_count\n";

// 获取当前总体统计（只统计非付费网站）
$stats = array();
$stats['total'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0");
$stats['linked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0 AND web_islink=0");
$stats['unlinked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0 AND web_islink=1");
$stats['need_check'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=0 AND (web_utime=0 OR web_utime < " . (time() - $update_cycle * 24 * 3600) . ")");

echo "\n当前链接状态统计：\n";
echo "- 可检测网站总数：" . $stats['total'] . "\n";
echo "- 已链接网站数：" . $stats['linked'] . "\n";
echo "- 未链接网站数：" . $stats['unlinked'] . "\n";
echo "- 仍需检测网站数：" . $stats['need_check'] . "\n";

if ($stats['need_check'] > 0) {
    echo "\n提示：还有 " . $stats['need_check'] . " 个网站需要检测，建议明天继续执行或手动批量检测。\n";
}

echo "\n任务执行时间：" . date('Y-m-d H:i:s') . "\n";

// 记录执行日志到数据库（可选）
try {
    $log_data = array(
        'execution_time' => date('Y-m-d H:i:s'),
        'checked_count' => $checked_count,
        'updated_count' => $updated_count,
        'error_count' => $error_count,
        'linked_count' => $linked_count,
        'unlinked_count' => $unlinked_count
    );

    // 如果有日志表，可以记录执行结果
    // $DB->query("INSERT INTO linkcheck_logs SET ...");

} catch (Exception $e) {
    // 日志记录失败不影响主要功能
    echo "日志记录失败：" . $e->getMessage() . "\n";
}

echo "\n友情链接检测定时任务执行完毕！\n";
?>
