变更记录
========================================================

.. contents::
	:depth: 2

ver 4.1.11 (2016-03-31)
-----------------------------------------------------------------
* 新增: 俄语语言包，感谢Valery Votintsev (http://codersclub.org/)。
* 改善: 语言包文件名标准化，zh_CN -> zh-CN, zh_TW -> zh-TW。
* Bugfix: [IE6] 当前页面设置了document.domain，销毁编辑器会报错。
* Bugfix: 行首全角空格被过滤。
* Bugfix: 修复多语言包的一些小错误。
* Bugfix: [IE11] 有些设备报错不能显示，对象不支持attachEvent属性或方法。
* Bugfix: retina屏幕上按钮裂开。
* Bugfix: 编辑图片后点击插入图片，弹出两个dialog。

ver 4.1.10 (2013-11-23)
-----------------------------------------------------------------
* Bugfix: 兼容IE11。
* Bugfix: [IE6-7] 上传按钮界面错乱。
* Bugfix: 引入kindeditor-all.js后开启自动高度插件会报错。
* Bugfix: &reg;来回切换代码模式后变成(R)。
* Bugfix: 字体、文字大小、颜色等操作有toogle效果。
* Bugfix: 非IE设置returnValue和cancelBubble。
* Bugfix: 特定的字符导致浏览器死循环。

ver 4.1.9 (2013-10-08)
-----------------------------------------------------------------
* Bugfix: 选中后无法添加超级链接。
* Bugfix: 自动高度插件无法在多个编辑器上使用。

ver 4.1.8 (2013-10-06)
-----------------------------------------------------------------
* 新增: kindeditor-all.js包含自动高度插件。
* 新增: K.html(expr, val)，K.appendHtml(expr, val)，K.insertHtml(expr, val)接口。
* 改善: IE9+都使用W3C Range。
* 改善: 页面加载完成后也可以触发KindEditor.ready。
* 改善: getAttributeNode已不赞成使用，用getAttribute替代。
* Bugfix: 有些浏览器上点击边缘，可能无法弹出文件选择框。
* Bugfix: embed宽高无法用百分比设置。
* Bugfix: [Firefox] 输入几个文字，切换到源代码模式再切换回来，插入图片报错。
* Bugfix: 自动高度插件高度只增不减，感谢Github用户wyqbailey贡献。
* Bugfix: editor.html(val)的val参数为null或undefined时报错。
* Bugfix: [IE10] 单独调用组件-上传图片弹出框，需要点击两次选择图片按钮才能弹出窗体。
* Bugfix: 代码模式下输入带连续多个空格的标签，有些浏览器无响应。
* Bugfix: [IE] 当两个A标签并排在一起中间没有别的内容，修改后面的链接地址时，前面的链接地址也被改掉。
* Bugfix: 页面同时引入SWFUpload，多图上传会失败。
* Bugfix: 插入分页符，有时候出现复制HTML代码的现象。
* Bugfix: 编辑图片后丢失class、id等属性。
* Bugfix: 在编辑器中输入值，页面提交跳转后，按浏览器的后退按钮，就出现__kindeditor_bookmark_start_0。
* Bugfix: 全屏后和还原后光标没有选中之前光标的位置。
* Bugfix: 特定环境下有时候出现两个弹出框。
* Bugfix: [IE] 编辑表格后焦点跳到顶部。
* Bugfix: [IE] 先选中图片后居中，再左对齐，光标跳到顶部。

ver 4.1.7 (2013-04-21)
-----------------------------------------------------------------
* Bugfix: 取消全屏后没有恢复到原来大小，调整窗口大小后宽高变成全屏宽高。
* Bugfix: [IE] 删除图片、Flash、视频后立即点击图片按钮出错。
* Bugfix: [IE8] 源代码模式下输入<input type="text" />会丢失type属性。
* Bugfix: [IE] 输入几个文字，切换到源代码模式再切换回来，插入图片报错。
* Bugfix: 插入5x5表格，A1向下合并两次，再点到A2，向下合并报错。

ver 4.1.6 (2013-03-24)
-----------------------------------------------------------------
* 新增: 韩国语语言包，感谢Github用户composite贡献。
* 新增: allowImageRemote初始化参数，可隐藏网络图片标签。
* 改善: 插入程序代码添加是否为空的判断。
* Bugfix: [IE9] 在frame里调用编辑器，关闭批量上传dialog时浏览器会崩溃。
* Bugfix: 插入图片后输入文字，文字加粗后取消加粗，图片会被删除。
* Bugfix: [IE] 工具栏被图片选中标记覆盖时有时候无法操作，比如居中对齐后再左对齐。
* Bugfix: 全屏ESC快捷键默认未开启，但图标提示还包含ESC。
* Bugfix: 图片上传后的url包含&时会被转换成&amp;。
* Bugfix: [IE] 移除编辑器后点击页面出现没有权限错误。
* Bugfix: [IE] 输入几个文字，调用editor.html(val)后，插入表情报错。
* Bugfix: 调用editor.resize()，退出全屏后，高度没恢复成原来的大小。

ver 4.1.5 (2013-01-20)
-----------------------------------------------------------------
* 新增: zIndex初始化参数，可指定弹出层的z-index。
* Bugfix: 复制粘贴3.x版本生成的文章时，可能会出现多余的空行。
* Bugfix: 非IE浏览器插入图片或粘贴文本后，可视范围没有自动滚动到光标当前显示的位置。
* Bugfix: [IE] 工具栏被图片选中标记覆盖时不能操作。
* Bugfix: [Firefox] 每次按回车都会显示TypeError。
* Bugfix: [Chrome] 纯文本粘贴1个空行会变成没有空行或者2个空行。
* Bugfix: [IE9] input标签会丢失checked属性。
* Bugfix: [IE8-] 未能隐藏display为none的input/select/button。

ver 4.1.4 (2012-11-11)
-----------------------------------------------------------------
* 改善: 弹出框能够跟随滚动条滚动居中显示。
* Bugfix: 服务器没有配置默认index.html时，百度动态地图无法加载。
* Bugfix: 点击图片属性、超级链接属性时，冒号变成%3A。
* Bugfix: 当页面里面有jQuery的uploadify插件时，无法连续上传。
* Bugfix: URL包含中文时，就会变成乱码。
* Bugfix: [Firefox] 编辑链接后回车换行，在新的段落输入内容带上面那个链接。
* Bugfix: 繁体语言包缺少uploadSuccess属性。
* Bugfix: [Firefox] 编辑3.x版本生成的文章时，可能会出现多余的空行。

ver 4.1.3 (2012-10-14)
-----------------------------------------------------------------
* 新增: 百度地图可插入iframe动态地图。
* 新增: pagebreakHtml初始化参数，可指定分页符HTML。
* 改善: 重复执行K.create时只创建一次。
* Bugfix: [IE] 只显示本地上传Tab时，打开图片弹出框报错。
* Bugfix: 点击全屏后再切换回来，有时候出现JS错误。
* Bugfix: K.addUnit(val, unit)第二个参数不起作用。
* Bugfix: &会转义成&amp;这样超链接就不能访问。
* Bugfix: 表情预览失效。
* Bugfix: [IE9] 多文件上传时不显示上传按钮。
* Bugfix: [Chrome] 创建弹出框时，Console提示没有访问权限。
* Bugfix: URL包含$字符时，生成错误的HTML代码。

ver 4.1.2 (2012-07-21)
-----------------------------------------------------------------
* 新增: K.remove(expr)函数，可移除多个编辑器，expr为选择器或DOM对象。
* 新增: K.sync(expr)函数，可同步多个编辑器，expr为选择器或DOM对象。
* 新增: K.create(expr)、K(expr)等函数可以直接传入jQuery对象。
* 新增: filePostName初始化参数，可指定上传文件form名称。
* 新增: fillDescAfterUploadImage初始化参数，true时图片上传成功后切换到图片编辑标签。
* 新增: afterSelectFile初始化参数，从图片空间选择后执行的回调函数。
* 新增: K.NodeClass,K.RangeClass,K.CmdClass,K.EditClass,K.MenuClass等接口。
* 新增: plugin.imageDialog(options)添加showLocal和showRemote参数，值为false时分别隐藏网络图片和本地上传。
* 新增: afterUpload新增data和name参数，分别为后端返回的JSON数据和插件名称。
* 变更: fullscreenShortcut默认值改成false，默认不启用ESC快捷键全屏。
* 改善: 多图上传时，允许用户post自定义参数到服务器。
* Bugfix: [Firefox] 居中后输入几个文字回车换行，内容被全选。
* Bugfix: 批量上传无法执行afterUpload这个回调，普通上传可以执行。
* Bugfix: 页面中存在其它SWFUpload，批量上传出现冲突。
* Bugfix: IE8怪异模式下先打开弹出框关闭，用滚轮到顶或到底，会出现脚本错误。
* Bugfix: 图片src为图片数据（base64 data）时，无法正常显示。
* Bugfix: 在pre标签里无法粘贴内容。
* Bugfix: KNode.show()和hide()，display都变成block。
* Bugfix: 版权标识&copy;来回切换代码模式后变成(C)。
* Bugfix: 重新创建KNode后，data方法无法取得数据。
* Bugfix: K.create函数未找到目标textarea时报错。
* Bugfix: 右下角拖动，松开鼠标后还可以继续拖动。
* Bugfix: 右键编辑表格，插入行和列时有时候错乱。

ver 4.1.1 (2012-06-10)
-----------------------------------------------------------------
* 新增: extraFileUploadParams初始化参数，文件上传时，支持添加别的参数一并传到服务器。
* 变更: filterMode默认值改成true，根据htmlTags配置过滤HTML代码。
* Bugfix: [Chrome] 粘贴内容代码中出现white-space:nowrap导致不换行。
* Bugfix: [IE6] 本地图片上传按钮错位。
* Bugfix: 开启过滤模式后，预览内容显示KindEditor。

ver 4.1 (2012-05-12)
-----------------------------------------------------------------
* 新增: 批量图片上传功能（multiimage）。
* 新增：地图默认用百度地图（baidumap）。
* 新增: QQ邮箱风格（贡献者：https://github.com/fisker）。
* 新增: formatUploadUrl初始化参数，false时不会自动格式化上传后的URL。
* 新增: fullscreenShortcut初始化参数，false时禁用ESC全屏快捷键。
* 改善: uploadbutton新增form、target参数，上传图片时可提交其它控件。
* 改善: K().children()直接返回KNode对象，原来是返回Array<KNode>。
* 改善: K.create()支持多个textarea，新增KindEditor.instances。
* 改善: Opera 最新版本支持BR换行。
* 改善: 当前页面的语言方向为rtl时，编辑区域也自动设置rtl。
* 改善: PHP写入临时文件失败，提示详细错误。
* Bugfix: [IE9] 上传图片的弹出窗口，最下方的“确定”“取消”会错位，跑到跟“图片说明”文本框的后面。
* Bugfix: FF、Chrome、Opera等行首全角空格被过滤，只有IE没问题。
* Bugfix: 图片正在上传时，连续点击确定按钮，会重复提交表单。
* Bugfix: [WEBKIT] 在BR换行模式下，需要两次回车才能换行。
* Bugfix: [IE9] 在BR换行模式下，在编辑器中回车之后，光标仍然还在本行，不会移动到下一行。
* Bugfix: noscript里的HTML代码会被转移字符。
* Bugfix: [ASP] 文件管理对大小写敏感，大写的文件扩展名会识别不出图片。
* Bugfix: 浏览文件窗口里的目录和文件图标被拉伸，看起来比较模糊。
* Bugfix: 带有超链接的图片删除以后，源代码里面还有A标签。
* Bugfix: 通过文件管理器插入本地附件时，URL可能出现连续两个斜线。

ver 4.0.6 (2012-03-18)
-----------------------------------------------------------------
* 新增: imageTabIndex初始化参数，可设置插入图片弹出层的默认显示标签。
* 新增: allowFileUpload初始化参数，可设置是否显示插入文件弹出层里的上传按钮。
* 新增: KNode类增加eq方法。
* 改善: 改进弹出框样式。
* 改善: 上传图片不选择文件提交时，在浏览器端验证并提示。
* 改善: 优化自动排版，块元素的第一个子节点是图片时不加缩进。
* 改善: 编辑表格时，点击文档会关闭取色器。
* Bugfix: [IE] 先选中图片，编辑图片后关闭Dialog，有时候会出现脚本错误。
* Bugfix: 修改plugins目录名，无法显示plugins目录下的图片。
* Bugfix: [IE] 上传图片后，进度条一直处于加载状态。
* Bugfix: [IE] 上传文件失败后，进度条一直处于加载状态。
* Bugfix: form添加onsubmit="return false;"，提交表单，编辑器转到代码模式就出错。
* Bugfix: [FF] 按下全屏按钮，恢复到原来大小后没有滚动条。
* Bugfix: 自动获取图片尺寸时，有时候得到的尺寸不准确。
* Bugfix: [IE] 在跨域的frame里调用编辑器，会出现权限错误。
* Bugfix: 全屏后form失去自动提交，reset功能也失效。
* Bugfix: 已经引入的default.css带时间戳时会重复加载CSS。

ver 4.0.5 (2012-01-15)
-----------------------------------------------------------------
* Bugfix: 页面添加 content="IE=EmulateIE7" 后，修改颜色、行距之类的操作全部失效。
* Bugfix: 后退（Ctrl+Z）时光标错乱。
* Bugfix: 通过粘贴纯文本框粘贴时，所有空格都变成&nbsp;。
* Bugfix: pasteType参数为1时，粘贴内容，多个空格变成一个空格。
* Bugfix: [FF] 上传图片后，总是出现正在加载的样式。
* Bugfix: [WEBKIT] event.layerX and event.layerY are broken and deprecated in WebKit.
* Bugfix: pasteType为1（纯文本粘贴模式）时，粘贴的内容会换行。
* Bugfix: 在iOS5上无法使用编辑器。
* Bugfix: 单独调用dialog时默认不显示阴影。
* Bugfix: 初始化编辑器时，在afterChange回调函数里无法得到this.edit对象。

ver 4.0.4 (2011-12-11)
-----------------------------------------------------------------
* 新增: 阿拉伯语语言包。
* 改善: 上传文件时显示上传中提示。
* 改善: JSON解析失败时，通过弹出层显示服务器返回的HTML页面。
* 改善: [IE] 弹出框支持阴影效果。
* Bugfix: 浏览器使用有些插件时，上传文件提示不正确。
* Bugfix: 单独调用图片功能时，点击重置大小图标报错。
* Bugfix: 设置了参数filterMode:true，分页符就会丢失样式。
* Bugfix: [FF] 撤销全屏后页面会滚动到顶部。
* Bugfix: [ASP] demo.asp没有指定编码，导致提交后HTML出现乱码。
* Bugfix: 单独调用上传按钮时，无法与旁边输入框对齐。
* Bugfix: [WEBKIT] 在图片、视频、flash等前一个光标处右键，在不选中节点的状态下也能弹出修改属性。
* Bugfix: [IE] 编辑器无内容，加粗，切换到代码模式，再回到可视化模式，加粗，JS报错。
* Bugfix: [IE] 插入<input value="abc&quot;def"/>，会自动变为 <input value="abc"def"/>。
* Bugfix: [WEBKIT] 点击粗体后丢失光标。
* Bugfix: [OPERA] 切换到代码模式后不显示部分工具栏图标。
* Bugfix: del标签被定义在块级元素里，导致格式化HTML时自动换行。
* Bugfix: 开启过滤模式，获取HTML时删除线被过滤。
* Bugfix: [IE] 两张相邻图片添加超级链接，修改其中一个链接，另外一个链接也会被修改。
* Bugfix: 内嵌脚本的小于号会被转义导致脚本错误。
* Bugfix: 分页符在不同浏览器下生成的HTML代码不一致。
* Bugfix: [IE6-7] 插入URL里有大写字符的图片，右键点击选择图片属性，更改图片属性后图片不能显示。

ver 4.0.3 (2011-11-04)
-----------------------------------------------------------------
* Bugfix: [IE] 残留range.dump()调试代码，导致粘贴时报错。
* Bugfix: [IE] 存在menu全局变量，可能发生冲突。
* Bugfix: [IE] 单元格里没有内容时显示不正常。
* Bugfix: 连续按粗体按钮时会生成很多strong。
* Bugfix: 初始化编辑器后，按下粗体按钮，焦点不在<p>标签里。
* Bugfix: [WEBKIT] 设定图片右对齐后，无法选取图片节点。
* Bugfix: [IE] 回车，按下tab键，光标在下一行显示。
* Bugfix: [IE] textarea的高度小于工具栏高度时JS报错。

ver 4.0.2 (2011-10-30)
-----------------------------------------------------------------
* 新增: 上传按钮新增afterError回调函数，可定制JSON错误。
* Bugfix: [FF] 在页面上设置iframe {overflow:hidden;} ，编辑区域不出现滚动条。
* Bugfix: 浏览服务器插件，文件名很长的时候会换行。
* Bugfix: [IE6-7] 在form里引入js的时候出现JS错误。
* Bugfix: [IE] 当编辑器为空时，输入任意字符，然后点击表单的重置按钮，再点击页面空白处，出现JS错误。
* Bugfix: [IE8] 设置X-UA-Compatible=IE7，有时候无法加载编辑器。
* Bugfix: a标签同时有name和href属性时，丢失name以外的属性。
* Bugfix: 连续调用多个ready函数时，第4个ready无法执行。
* Bugfix: 插入多媒体后，右键点击不会弹出菜单。
* Bugfix: 启用纯文本黏贴后，段落首尾都会出现>符号。
* Bugfix: [IE] 点击标题、字体、文字大小，编辑区域失去选中状态。
* Bugfix: [FF,WEBKIT] 连续换行几次，切换到源代码，再切换到可视化模式，没有换行效果。
* Bugfix: [WEBKIT] 选择几个文字，点击上标或下标功能，上下标格式不会被应用。
* Bugfix: 加载编辑器后残留多余的div标签。
* Bugfix: 页面上包含跨域iframe的时候JS报错。
* Bugfix: 页面刷新后，与第一次访问加载的编译器高度不一致。
* Bugfix: [IE6] 弹出层无法遮住selectbox。
* Bugfix: [FF] 提交后退后，编辑器数据不保存。
* Bugfix: 选择粗体，取消粗体再应用粗体（即点两下粗体），则发现粗体、倾斜、下划线功能失效，无法选择。
* Bugfix: [WEBKIT] 置入Issue 269中的HTML，全选，点击删除格式，又出现一个图片，图片变为两个。
* Bugfix: 与MooTools类库有冲突。
* Bugfix: [IE] 选中粘贴过来的文本，进行格式操作时位置出现偏移。
* Bugfix: [IE] 后退前进时有时候报错。

ver 4.0.1 (2011-10-07)
-----------------------------------------------------------------
* 改善: image插件，通过editor.plugin.imageDialog()可以单独调用图片弹出框。
* 改善: filemanager插件，Ajax请求时显示Loading效果。
* 改善: 工具栏图标改成png8格式。
* Bugfix: 不能用style的width和height设置编辑器大小。
* Bugfix: 从MS WORD里面拷贝过来的表格，表格的颜色会丢掉。
* Bugfix: [IE] 关闭弹出层后光标自动跳转到顶部。
* Bugfix: 添加链接时有时候出现__kindeditor_temp_url__。
* Bugfix: [IE] 点击工具栏后，编辑区域失去选中状态。
* Bugfix: 网速比较慢的时候，连续点击一个图标，弹出多个弹出框。
* Bugfix: 删除格式时不能删除段落缩进属性。
* Bugfix: 拖拉改变Flash大小，点击源代码再点回来，Flash长宽自动恢复成预设值。

ver 4.0 (2011-09-26)
-----------------------------------------------------------------
* 新增: 锚点功能。
* 新增: 增加loadStyleMode属性，默认情况下自动加载CSS文件。
* 新增: 编辑器对象增加isDirty方法，判断编辑器内容是否有修改。
* 改善: 粘贴MS Word时自动清理Word专用格式代码，生成干净的HTML代码。
* 改善: 弹出框(dialog)里的输入框添加了基本验证。
* 改善: 超级链接不允许包含HTML代码。
* 改善: uploadJson URL支持GET参数。
* 优化: 后退撤销，粘贴性能。
* Bugfix: 修复了allowImageUpload为false时，无法插入网络图片的问题。
* Bugfix: [WEBKIT] 修复了粘贴内容时顺序相反的问题。
* Bugfix: 修复了进行修改操作，再选择一段带有样式的文字，再进行撤销操作，首先撤销的是选取操作，然后才会撤销修改的问题。
* Bugfix: 修复了设置basePath参数后，themesPath、langPath、pluginsPath参数不起作用的问题。
* Bugfix: 修复了图片和超级连接URL输入双引号时，HTML代码出现错乱的问题。
* Bugfix: [IE] 修复了反复执行后退和前进时有时候出现脚本错误的问题。
* Bugfix: [IE] 修复了连续选择相同文件上传时，第二次开始无法上传的问题。
* Bugfix: [IE] 修复了textarea在p标签里时，无法创建编辑器的问题。
* Bugfix: 修复了filterMode为true时，没有过滤script和style内容的问题。
* Bugfix: [WEBKIT] 修复了粘贴内容后光标消失的问题。
* Bugfix: [IE7] 修复了上传按钮出现偏移的问题。
* Bugfix: [IE] 修复了innerHTML有时候抛出异常的问题。
* Bugfix: 修复了清除HTML代码时过滤rowspan和colspan，导致表格被破坏的问题。
* Bugfix: 修复了在框架(frameset)下面，点击编辑器的源代码按钮后，再点击其它连接变弹出显示的问题。
* Bugfix: 修复了在非IE浏览器上，插入表格后，鼠标无法移到表格下面输入文字的问题。
* Bugfix: [WEBKIT] 修复了回车换行后标题属性丢失的问题。
* Bugfix: [WEBKIT] 修复了粘贴到单元格时有时候粘贴错位的问题。
* Bugfix: 修复了删除格式时有时候丢失文字的问题。
* Bugfix: [IE] 修复了在HTML里有inline-block时有时候_getStartEnd报错的问题。
* Bugfix: 修复了打开地图后立即关闭窗口时，无法关闭的问题。
* Bugfix: 修复了insertHtml插入注释开头的HTML时，注释被过滤的问题。

ver 4.0 beta (2011-08-17)
-----------------------------------------------------------------
* Bugfix: 修复了域名包含端口时在IE上发生错误的问题。
* Bugfix: 修复了在IE上通过工具栏的undo/redo按钮进行undo/redo时无法后退的问题。
* Bugfix: 修复了在FF和IE上加载编辑器后生成一个history的问题。
* Bugfix: 修复了在IE上点击右键自动插入&nbsp;的问题。
* Bugfix: 修复了在IE上文本在table标签前时，原生range转换成标准range后出现偏移的问题。
* Bugfix: 修复了在WEBKIT系列浏览器上在全屏模式下，弹出的插入图片、超链接等对话框，输入框内无法粘贴内容的问题。
* Bugfix: 修复了在pre标签里回车加入空行无效的问题。
* Bugfix: 修复了切换到代码模式后，按全屏报错的问题。
* Bugfix: 修复了点击工具栏时有时候弹出来几个相同dialog的问题。
* Bugfix: 修复了在IE上项目编号无论选在到哪里都是第一行加编号的问题。
* Bugfix: 修复了焦点处于被合并的单元格，右键菜单，向上添加一行，表格错乱的问题。
* Bugfix: 修复了焦点处于被合并的单元格的上一个单元格，右键菜单，向下合并单元格，表格错乱的问题。
* Bugfix: 修复了在IE上点击编辑区域时内存一直增加的问题。

ver 4.0 alpha (2011-08-16)
-----------------------------------------------------------------
* 初期版本，重新编写所有代码。
* 新增: 插入程序代码、预览、插入地图、调整行距、一键排版、清理HTML代码、插入分页符、插入附件、插入模板功能。
* 新增: Flash、多媒体编辑功能，Flash、多媒体上传功能。
* 新增: 表格单元格的合并和拆分功能。
* 新增: ESC键切换全屏模式。
* 改善: 后退/前进(undo/redo)时保持选中状态。
* 改善: 大部分组件实现模块化，可以单独调用。
* 改善: 改进了HTML格式化功能。
* 改善: 粘贴纯文本时按照换行设置（newlineTag）换行。
* 改善: 滚动页面时dialog自动居中。
* 改善: 在移动设备上只能使用代码模式。
* 改善: 修改图片尺寸时自动保持比例。
* Bugfix: 修复了在页面上设置document.domain时发生错误的问题。
* Bugfix: 修复了跨域调用编辑器时无法使用dialog的问题。
* Bugfix: 修复了range的collapsed为true时删除格式不起作用的问题。
* Bugfix: [WEBKIT] 修复了range的collapsed为true时字体、颜色等无效的问题。
* Bugfix: 修复了在不同浏览器上加粗、斜体、下划线、删除线生成出来的HTML代码不一致的问题。
* Bugfix: 修复了全选后有时候不能清除格式的问题。
* Bugfix: 修复了工具栏经常受全局CSS影响的问题。(改用DIV布局)
* Bugfix: 修复了直接拷贝页面自动执行js代码的问题。
* Bugfix: 修复了页面底部显示右键菜单被挡住的问题。
* Bugfix: 修复了在HTML里存在不规则属性("="")时过滤不掉其它属性的问题。
* Bugfix: 修复了处理被合并过的单元格时发生错误的问题。

ver 3.5.6 (2011-10-04)
-----------------------------------------------------------------
* 增加: 新增afterDrag属性(回调函数)，拖动改变编辑器大小后执行。
* 增加: 新增afterUpload属性(回调函数)，上传成功后执行。
* Bugfix: 修复了工具栏受全局a:hover的影响的问题。
* Bugfix: 修复了在全屏模式下编辑器可以被拖动的问题。
* Bugfix: [ASP]不改变文件名并上传中文名文件时文件名出现乱码。
* Bugfix: [IE9]删除格式功能有时候不起作用。
* Bugfix: [IE9]添加样式时有时候报错。

ver 3.5.5 (2011-05-22)
-----------------------------------------------------------------
* 增加: 新增单元格编辑功能。
* 改善: 改进输入框和按钮的外观。
* 改善: 打开dialog后自动选中第一个输入框。
* 改善: 用CSS实现dialog的阴影。
* 改善: 插入图片时不设置border="0"属性。
* Bugfix: 修改了在IE9上上传图片后原来的内容全部消失的问题。
* Bugfix: 修改了在FF4上有时候无法插入图片的问题。
* Bugfix: 修改了在IE6上插入图片后，在图片前出现一个空格的问题。
* Bugfix: 修改了在IE上使用清除格式功能来删除一段加粗的文字时发生JS错误的问题。(只有压缩后的min有这个问题)

ver 3.5.4 (2011-05-01)
-----------------------------------------------------------------
* 改善: 直接兼容IE9。
* Bugfix: 修改了在源代码模式下输入JS代码后切换到可视化模式时会执行JS代码的问题。
* Bugfix: 修改了在IE上编辑区域里的选中select控件时出现JS错误的问题。
* Bugfix: 修改了在IE上通过KE.insertHtml函数输入<mp3>URL</mp3>时丢失标签的问题。
* Bugfix: 修改了在一个页面调用多个编辑器时重复加载相同CSS的问题。
* Bugfix: 修改了在一个页面包含多个kindeditor.js时无法打开dialog的问题。
* Bugfix: 移除了工具栏里的两对多余的tr标签。

ver 3.5.3 (2011-04-09)
-----------------------------------------------------------------
* 增加: 新增useContextmenu属性，值为true时使用自定义右键菜单，false时屏蔽自定义右键菜单，默认值为true。
* 增加: 新增syncType属性，值为"auto"时每次修改时都会同步，"form"时提交form时同步，""时不会自动同步，默认值为"form"。
* 增加: 新增tabIndex属性，可设置编辑器的tabindex。
* 增加: 新增afterChange属性(回调函数)，编辑器内容发生变化后执行的函数。
* 增加: 新增afterTab属性(回调函数)，按下TAB键后执行的函数，默认情况下插入4个空格。
* 增加: 新增afterFocus属性(回调函数)，编辑器获得焦点(onfocus)时执行的函数。
* 增加: 新增afterBlur属性(回调函数)，编辑器失去焦点(onblur)时执行的函数。
* 增加: 新增KE.sync函数，将编辑器数据设回到原来的textarea里，与KE.util.setData函数功能相同。
* 增加: 新增KE.blur函数，让编辑器失去焦点。
* 改变: 将autoSetDataMode的默认值改成false，默认情况下自动寻找所属form，并将KE.sync绑定到该form的submit事件里。
* 改善: fileManagerJson支持GET参数。
* 改善: 动态设置上传图片保存URL(save_url)，在不同深度的页面调用编辑器不会出错。
* 改善: 当编辑器属性newlineTag为p时，粘贴纯文本换行使用p标签。
* 改善: 编辑器id支持[a-z0-9\_]以外的特殊字符。
* 改善: 上传图片按日期目录保存。
* 改善: 在IE6和IE7上浏览器原生菜单包含复制粘贴选项。
* Bugfix: 在IE上通过showModalDialog显示编辑器时无法输入内容。
* Bugfix: 修改了删除列时单元格错位的问题。
* Bugfix: 修改了在Firefox下点击dialog的按钮后没有按下去的效果的问题。
* Bugfix: 有些浏览器无法解析[\w-:]，需对“-”进行转义[\w\-:]。
* Bugfix: 执行KE.html后有时候全选整个编辑区域。
* Bugfix: 在Mac OS X的Firefox上无法显示右键菜单。
* Bugfix: script标签内的JavaScript代码字符串里包含HTML代码时，该字符串也被格式化。
* Bugfix: 修改了ASP浏览图片程序无法进入子目录的问题。
* Bugfix: 修改了通过TAB键移动焦点时焦点移动到工具栏图标上的问题。

ver 3.5.2 (2010-12-02)
-----------------------------------------------------------------
* Bugfix: 修改了在IE下拖动调整大小不够顺畅的问题。
* Bugfix: 修改了在IE下JS的src为"kindeditor.js"时无法加载CSS文件的问题。
* Bugfix: 提高上传图片JSON格式兼容性，防止某些时候因服务器输出额外的数据而导致JSON解析失败的问题。
* Bugfix: 修改了在IE上某些情况下添加样式偏移的问题。
* Bugfix: 修改了在IE下焦点在图片后面时按下TAB键JS报错的问题。
* Bugfix: 修改了KE.util.setOpacity的opacity为2和20时结果相同的问题。
* Bugfix: 修改了在IE6下高度小于0时出现脚本错误的问题。

ver 3.5.1 (2010-07-18)
-----------------------------------------------------------------
* Bugfix: 修改了表格左侧插入列时单元格移位的问题。
* Bugfix: 修改了在Firefox上设置全局CSS后高度计算不正确的问题。
* Bugfix: 修改了ASP上传程序无法上传大写扩展名文件的问题。
* Bugfix: 修改了在Firefox上调用KE.html函数在某些情况下JS报错的问题。
* Bugfix: 修改了在IE6、IE7上只读模式下不显示内容的问题。
* Bugfix: 修改了JSP演示程序提交中文数据后出现乱码的问题。
* Bugfix: 修改了通过insertHtml插入HTML时URL自动变成绝对域名的问题。
* Bugfix: 修改了在IE上用BR换行时回车换行自动选中下面内容的问题。
* Bugfix: 修改了设置表格背景颜色后不能取消颜色的问题。

ver 3.5 (2010-06-20)
-----------------------------------------------------------------
* 增加: 增加了表格编辑功能。
* 增加: 引入了多国语言机制。
* 增加: 标题、字体、文字大小、颜色可以反映当前状态。
* 增加: 右键菜单支持图标和分割线。
* 增加: 表情功能增加分页和预览。
* 增加: 增加了弹出框阴影效果。
* 增加: 增加了新接口。(KE.html,KE.text,KE.selectedHtml,KE.insertHtml,KE.appendHtml,KE.isEmpty等)
* 改善: 编辑器底部显示向下拖动指示图标。
* 改善: 点击编辑器外的页面其它部位时关闭菜单。
* 改善: 移除编辑器时将编辑器内容设置到原来的textarea。
* 改善: 从外部粘贴内容时自动将font转换成span标签。
* 改善: ASP.NET程序改成ashx，使用时不需要编译。
* Bugfix: 改善了文章内容比较多时速度比较慢的问题。
* Bugfix: 修改了在IE上选中图片或表格后无法用backspace键删除的问题。
* Bugfix: 修改了在Firefox上全屏后浏览器一直处于加载状态的问题。
* Bugfix: 修改了在非IE上DOMContentLoaded事件不起作用的问题。
* Bugfix: 修改了删除编辑器时没有销毁事件的问题。
* Bugfix: 修改了设置成无颜色时其它样式也被删除的问题。
* Bugfix: 修改了拖动时拖到浏览器外面放开鼠标后会粘住的问题。
* Bugfix: 修改了在Firefox上pre标签自动生成br标签的问题。
* Bugfix: 修改了在IE6上用KE.cmd.wrap方法设置class属性后没有效果的问题。
* Bugfix: 修改了在P标签内没选中内容时无法插入超级链接的问题。
* Bugfix: 修改了使用快捷键加粗体、斜体、下划线时没有同步的问题。

ver 3.4.4 (2010-06-01)
-----------------------------------------------------------------
* Bugfix: 修改了在IE上焦点自动移动到编辑区域的问题。
* Bugfix: 修改了在IE上打开类型无法修改成当前窗口的问题。
* Bugfix: 修改了全选后无法取消超级链接的问题。
* Bugfix: 修改了切换代码模式时编辑器轻微抖动的问题。
* Bugfix: 修改了在IE上切换代码模式时有时候不出现滚动条的问题。
* Bugfix: 修改了在Chrome 5.0上反复切换代码模式有时候出现崩溃页面的问题。
* 改善: 显示菜单后再点将关闭此菜单。

ver 3.4.3 (2010-05-26)
-----------------------------------------------------------------
* Bugfix: 修改了重复编辑超级链接时每次都添加&amp;的问题。
* Bugfix: 修改了在IE上右键菜单没有复制、剪切项目的问题。
* Bugfix: 修改了在IE上没有格式化<font color=#000>代码的问题。
* Bugfix: 修改了PHP上传程序日期格式不正确的问题。
* Bugfix: 修改了在IE上代码模式下全屏本地URL自动变成绝对URL的问题。
* Bugfix: 修改了在代码模式下KE.util.setFullHtml函数不显示HTML内容的问题。
* Bugfix: 修改了在MARQUEE元素里回车换行出现JS错误的问题。
* Bugfix: 修改了通过菜单剪切、粘贴时不触发KE.event.input事件的问题。
* Bugfix: 修改了在IE上焦点离开编辑区域后没有记住最后的range位置的问题。
* Bugfix: 修改了在源代码模式下undo/redo能看到临时HTML代码的问题。
* Bugfix: 修改了在IE上输入的HTML开头是<script>时该代码被删掉的问题。
* Bugfix: 修改了在IE上将<img>替换<hr>时有时候报错的问题。
* Bugfix: 修改了在IE上编辑marquee元素里的图片和超级链接时报错的问题。
* Bugfix: 修改了右键点击图片右边时有时候会弹出图片编辑菜单的问题。
* Bugfix: 修改了script和style代码无法保留换行符的问题。
* Bugfix: 修改了在非IE浏览器上换行使用p的时候最后一个p结尾还是有一个br的问题。
* Bugfix: 修改了Webkit系列浏览器的textarea可拖动调整大小，聚焦时边框变成黄色的问题。
* Bugfix: 修改了在IE上代码模式下有时候不会自动换行的问题。
* Bugfix: 修改了在IE上new Function和iframe引起内存泄漏的问题。
* 改变: 默认换行方式改成p换行。
* 改善: 弹出框未指定任何按钮(yesButton, noButton, previewButton)时，不显示底部DIV。
* 改善: 确定alert框后将焦点设置到输入错误的输入框。
* 改善: 上传图片时如果返回的JSON格式有错误，提示友好信息。
* 改善: 从Word粘贴功能严格过滤垃圾代码。
* 改善: 编辑时同步更新原textarea里的HTML内容，不需要在提交前设置KE.util.setData。
* 改善: 根据resizeMode配置显示不同的鼠标状态和小图标。
* 改善: 按TAB键时插入4个&nbsp;。
* 增加: 增加了afterDialogCreate属性，设置弹出dialog后执行的回调函数。
* 增加: 增加了ASP.NET、ASP、JSP演示程序。
* 增加: 增加了工具栏分割符号。
* 删除: 删除了autoOnsubmit属性。

ver 3.4.2 (2010-04-04)
-----------------------------------------------------------------
* 增加: 添加了KE.util.isEmpty函数，用于判断编辑器是否有可见内容。
* 改善: 页面很小时弹出菜单的上下位置不变。
* 改善: 插入超级链接未选中内容时插入URL文本。
* 改善: 插入超级链接的打开类型为当前窗口时删除A标签的target属性。
* Bugfix: 修改了在IE上HTML属性值里输入JS代码时格式出现错误的问题。
* Bugfix: 修改了cssPath属性为空时加载首页的问题。
* Bugfix: 修改了当浏览器出现滚动条并拖动调整大小时控制不住的问题。
* Bugfix: 修改了embed代码丢失自定义属性的问题。
* Bugfix: 修改了在IE上切换到代码模式后点击图标触发onbeforeunload事件的问题。
* Bugfix: 修改了在Firefox上光标在图片旁边时点击鼠标右键，弹出右键菜单的问题。
* Bugfix: 修改了在Firefox上无法修改/删除图片的超级链接的问题。
* Bugfix: 修改了在Webkit上有时候无法添加/修改/删除图片的超级链接的问题。

ver 3.4.1 (2010-02-25)
-----------------------------------------------------------------
* 添加了dialogAlignType属性，指定弹出窗口对齐方式。
* 添加了imageUploadJson属性，可指定上传图片服务器端程序。
* 添加了fileManagerJson属性，可指定浏览服务器文件的服务器端程序。
* 修改了在IE上删除所有可见内容后留下P标记的问题。
* 修改了拖动弹出窗口时可以拖出页面外的问题。
* 修改了拖动弹出窗口时选中内容的问题。
* 修改了在IE8上点击工具栏触发onbeforeunload事件的问题。
* 修改了输入带冒号的标签时HTML格式出现错误的问题。
* 修改了在Firefox上不选中超级连接时不能取消超级连接的问题。
* 修改了当页面比较小时下拉菜单超出页面的问题。
* 修改了在Webkit浏览器上不选中内容添加超级连接时插入__ke_temp_url__的问题。
* beforeCreate、afterCreate等回调函数添加了id参数。
* 改善了URL格式化规则，urlType参数默认为空，当urlType为空时不修改URL。
* 只要KE.plugin里有定义就执行插件的init处理。
* cssPath参数可指定多个CSS文件。
* KE.event.ctrl函数可以直接传入keyCode数字。
* urlType为relative时省略当前路径标识符。
* 图片上传程序返回JSON数据，文件名改成upload_json.php。
* 上传图片过程中显示加载动画。
* 标题格式增加了正文。
* 更换了默认风格。

ver 3.4 (2009-12-19)
-----------------------------------------------------------------
* 添加了图片修改/删除功能。
* 添加了超级连接修改/删除功能。
* 添加了浏览服务器文件的功能（PHP）。
* 添加了URL格式化功能。
* 添加了afterCreate和beforeCreate回调函数。
* 添加了textarea的name属性支持，没指定id时寻找name。
* htmlTags属性指定style时，忽略[.]开头的属性，允许任何样式。
* 改善了弹出框，支持多个窗，根据浏览器窗口居中，加载时显示[加载中]动画。
* 改善了回车换行，通过参数可设置BR或P换行，默认BR换行。
* 改善了插入表情功能，显示表情图片时只加载一次图片。
* 在WEBKIT系列浏览器上点击图片后自动选中。
* 编辑器最大化之后不允许拖动修改大小。
* 编辑器的CSS文件可以手动包含。
* 编辑器所用到的图标全部在CSS文件里定义。
* skins里的文件分别放在不同目录里。
* 下拉菜单根据文字内容自动调整宽度。
* 默认不开启过滤模式。
* 修改了Firefox上Flash和多媒体不显示的问题（用图片表示）。
* 修改了非IE浏览器选中element元素时取得错误range的问题。
* 修改了非IE浏览器无法选中element元素的问题。
* 修改了清除格式后有时候变成一行的问题。
* 修改了IE6怪异模式下切换模式高度有变化的问题。
* 修改了Firefox 2上不能使用的问题。
* 修改了在Webkit上高度比较小的时候底部出现空白的问题。
* 修改了在非IE浏览器上上传失败后重新刷新页面的问题。
* 删除了[插入层]、[日期]、[时间]、[预览]、[插入特殊字符]功能。
* 还有很多代码优化。

ver 3.3.1 (2009-09-20)
-----------------------------------------------------------------
* 修改了删除文本格式后出现垃圾代码的问题。
* 删除了KE.util里的没有用到的函数。
* 修改了在IE上多个编辑器同时显示时，点击全屏另外一个编辑器自动变成最大化的问题。
* 修改了在Firefox上缩进操作后产生的代码默认被过滤的问题。
* 修改了删除编辑器后没有清除container的问题。
* 添加了TAB键缩进功能。
* 上传图片时重命名文件名。
* 拖拽编辑器调整大小时不再隐藏编辑器内容。
* 修改了几个演示程序，优化了细节。

ver 3.3 (2009-09-06)
-----------------------------------------------------------------
* 提高了加载速度。DOM加载完成后立即创建编辑器，以前用了window onload事件。
* 改善了HTML格式化性能。增加KE.format，替代原来的outputHtml和htmlToXhtml。
* 删除了siteDomains属性，link和当前域名相同时自动改成相对域名。
* 修改了在IE上有不规范HTML标签时出现重复内容的问题。
* 修改了在Fifefox粘贴Word文档时头部出现垃圾代码的问题。
* 编辑器宽度设定支持百分比，不设置大小时默认取得textarea的大小。
* 整理了插入表格代码。
* 修改了特殊字符、插入表格等功能受YUI全局CSS影响的问题。
* 修改了在Firefox上按F5刷新时JS报错的问题。
* 修改了在Firefox上有时候不能删除内容的问题。
* 修改了代码模式下输入的内容没有被格式化的问题。

ver 3.2.1 (2009-08-09)
-----------------------------------------------------------------
* 修改了在IE上行尾插入图片后光标无法移动到图片后位置的问题。
* 修改了在IE上内容为空时连续插入非文字元素出现脚本错误的问题。
* 修改了原代码模式下输入<textarea></textarea>后来回切换模式时发生错误的问题。
* 修改了在IE上<br>换行后改变字体时光标移动到上一行的问题。
* 修改了在IE上删除文本格式时选中位置有时候会偏移的问题。
* 修改了range在text range的最后位置时wrap方法不正常的问题。
* getPureData方法过滤&nbsp;。
* 修改了htmlTags的默认值。
* 修改了在WEBKIT系列浏览器上有滚动条时下拉框定位不正确的问题。

ver 3.2 (2009-07-12)
-----------------------------------------------------------------
* 工具栏图标可以反映选中状态。
* 用虚线显示p,div,ol等标记。
* font标记全部改成span，文字大小统一使用px单位。
* htmlTags属性一次可定义多个标记。
* Firefox等浏览器上颜色可以输出统一的十六进制颜色。
* filterMode为false的时候输出XHTML，并支持siteDomain设定。
* 修改了过滤一些代码后HTML代码有偏移的问题。
* 修复了在IE上点击工具栏图标时失去焦点的问题。
* 编辑区域的body里添加了ke-content class。
* 添加了后退/撤销快捷键(Ctrl+Z和Ctrl+Y)。
* 改善了默认风格。
* 包含很多细小的代码优化。
* 增加了宽度和高度属性。

ver 3.1.2 (2009-04-15)
-----------------------------------------------------------------
* 修改了IE上拖动选择图片后添加超级链接发生错误的问题。
* 修改了Flash、多媒体、图片的验证规则，支持GET参数。

ver 3.1.1 (2009-03-09)
-----------------------------------------------------------------
* 修改了设置siteDomains无效的问题。
* 修改了例子当中的一些文字错误。

ver 3.1 (2009-03-01)
-----------------------------------------------------------------
* 合并了javascript文件，删除了build目录，只保留kindeditor.js非压缩格式。
* 修改了HTML过滤功能，通过htmlTags属性可以指定HTML标记和属性。
* 修改了有时候超级连接出现__ke_temp_url__的问题。
* 修改了KE.util.selection()里==符号写成=的问题。
* 修改了连续输入空格变成特殊字符的问题。
* 初期显示编辑器时焦点不再默认移到编辑区域。

ver 3.0.1 (2009-02-10)
-----------------------------------------------------------------
* 修改了包含prototype、mootools等类库时发生冲突的问题。
* 修改了在非IE浏览器下outputHtml()过滤正常代码的问题。
* 改善了超级连接功能(link plugin)。
* 添加了KE.lang['invalidUrl']语言定义。
* 修改了在IE6下重复加载工具栏图标的问题。
* 修改了在Firefox 2.0下发生错误的问题。
* 修改了指定多个siteDomains参数时无效的问题。
* 添加了禁止拖动工具栏图标的处理。

ver 3.0 (2009-01-24)
-----------------------------------------------------------------
* 修改了outputHtml()若干问题。
* 修改了position: relative下无法设置全屏的问题。
* 修改了HTML4.0下非IE浏览器页面变形的问题。
* 修改了全屏下弹出窗口后可以点击编辑区域的问题。

ver 3.0 beta 4 (2009-01-18)
-----------------------------------------------------------------
* 修改了IE无法对齐的问题。
* 调整了IE换行规则。

ver 3.0 beta 3 (2009-01-18)
-----------------------------------------------------------------
* 加强了undo/redo。
* 增加了HTML代码过滤功能，并通过filterMode可以选择是否过滤。
* 修改了粘贴纯文本时解析HTML代码的问题。
* 修改了skinsPath和pluginsPath属性无法自定义的问题。
* 增加了siteDomains属性。
* 删除了plugin-mini.js。

ver 3.0 beta 2 (2009-01-01)
-----------------------------------------------------------------
* 修改了PHP上传图片时标题不正确的问题。
* 属性hideBottomMode改成resizeMode。
* 修改了编辑器外观受YUI CSS影响的问题。
* 修改了IE浏览器上编辑时有时候HTML显示不全的问题。
* 修改了部分浏览器插入link时发生js错误的问题。
* 自定义ICON可以定义其它外部图片。
* 初期显示时不再插入<p><br /></p>。
* 精简了部分代码。
* 增加了几个demo。

ver 3.0 beta (2008-12-09)
-----------------------------------------------------------------
* 修改了Firefox3下第一次选择标题有错误的问题。
* 修改了切换到HTML模式时编辑器会抖动的问题。
* 修改了插入表情以后路径有错误无法显示的问题。
* 修改了TinyMCE风格的时间icon坐标不正确的问题。
* 修改了移动dialog时编辑器文字移动结束后也不显示的问题。
* 修改了在iframe里无法使用的问题。
* 修改了目录名为kindeditor时getScriptPath取路径不正确的问题。
* 修改了增加缩进和减少缩进两个图标的提示文本。
* 修改了IE下没有指定DOCTYPE时显示有问题。
* 代码统一用4个空格缩进。
* 增加了几个demo。
* plugin-all.js里的中文提取到zh-CN.js。

ver 3.0 alpha (2008-11-30)
-----------------------------------------------------------------
* 初期完成。
