<?php
// generate_intro.php
header('Content-Type: application/json');

// 引入系统配置
define('IN_ADMIN', TRUE);
define('ROOT_PATH', str_replace('\\', '/', dirname(dirname(__FILE__))).'/');
define('APP_PATH', ROOT_PATH.'source/');
require(APP_PATH.'init.php');
require(APP_PATH.'module/static.php');

// 获取提交的数据
$url = isset($_POST['url']) ? trim($_POST['url']) : '';
$tags = isset($_POST['tags']) ? trim($_POST['tags']) : '';
$intro = isset($_POST['intro']) ? trim($_POST['intro']) : '';

if (empty($url) || empty($tags) || empty($intro)) {
    echo json_encode(array('status' => 'error', 'message' => '请填写网站域名、TAG标签和网站简介'));
    exit;
}

// 智谱AI API配置（从后台读取）
$api_key = isset($options['ai_api_key']) && !empty($options['ai_api_key']) ? $options['ai_api_key'] : '';
$api_url = isset($options['ai_api_url']) && !empty($options['ai_api_url']) ? $options['ai_api_url'] : 'https://open.bigmodel.cn/api/paas/v4/chat/completions';

// 检查API密钥是否已配置
if (empty($api_key)) {
    echo json_encode(array('status' => 'error', 'message' => 'AI API密钥未配置，请联系管理员在后台设置'));
    exit;
}

// 构建请求的Prompt（从后台读取提示词模板，如果没有配置则使用默认）
$prompt_template = isset($options['ai_prompt_template']) && !empty($options['ai_prompt_template']) ? $options['ai_prompt_template'] :
    "你是一个专业的网站内容撰写助手，请根据以下信息为网站生成一段简洁、有吸引力的AI简介（输出600字左右的HTML内容）：\n" .
    "使用<p><ul><li><strong>等基础标签\n" .
    "包含SEO关键词但保持自然\n" .
    "分3-5个段落，每个段落有明确主题\n" .
    "符合中文阅读习惯\n" .
    "请确保内容突出网站特色，语言流畅自然，适合用作网站AI简介\n" .
    "只要一个文本网址不要链接";

$prompt = $prompt_template . "\n" .
          "网站域名：{$url}\n" .
          "TAG标签：{$tags}\n" .
          "网站简介：{$intro}\n";

// 请求智谱AI API
$request_data = array(
    'model' => isset($options['ai_model_name']) && !empty($options['ai_model_name']) ? $options['ai_model_name'] : 'glm-4', // 从后台读取或使用智谱AI的GLM-4模型（根据实际支持的模型调整）
    'messages' => array(
        array('role' => 'user', 'content' => $prompt)
    ),
    'temperature' => isset($options['ai_temperature']) && !empty($options['ai_temperature']) ? floatval($options['ai_temperature']) : 0.7 // 从后台读取或使用默认值：控制生成内容的创造性，0.7为适中
);

$ch = curl_init($api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60); // 60秒超时
curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // 10秒连接超时
curl_setopt($ch, CURLOPT_HTTPHEADER, array(
    'Content-Type: application/json',
    'Authorization: Bearer ' . $api_key
));
curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($request_data));

$response = curl_exec($ch);
$curl_error = curl_error($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($curl_error) {
    if (strpos($curl_error, 'timeout') !== false) {
        echo json_encode(array('status' => 'error', 'message' => '生成超时，请尝试使用更简洁的提示词模板'));
    } else {
        echo json_encode(array('status' => 'error', 'message' => 'API请求失败：' . $curl_error));
    }
    exit;
}

if ($http_code === 524) {
    echo json_encode(array('status' => 'error', 'message' => '生成超时(524)，请尝试使用更简洁的提示词模板'));
    exit;
}

if ($http_code !== 200) {
    echo json_encode(array('status' => 'error', 'message' => 'HTTP错误：' . $http_code));
    exit;
}

// 解析智谱AI API的响应
$response_data = json_decode($response, true);
if (isset($response_data['choices'][0]['message']['content'])) {
    $generated_content = $response_data['choices'][0]['message']['content'];
    echo json_encode(array('status' => 'success', 'message' => '生成成功', 'content' => $generated_content));
} else {
    echo json_encode(array('status' => 'error', 'message' => 'API响应异常：' . json_encode($response_data)));
}

?>
