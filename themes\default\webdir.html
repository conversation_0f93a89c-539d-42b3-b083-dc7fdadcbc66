<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
<meta name="robots" content="index,follow" />
<meta name="format-detection" content="telephone=no" />
<meta name="mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-capable" content="yes" />
<meta name="apple-mobile-web-app-status-bar-style" content="default" />
{#if $cate_id > 0#}
<meta name="apple-mobile-web-app-title" content="{#$cate_name#}网站目录 - {#$options.site_name#}" />
<link rel="canonical" href="{#$site_url#}?mod=webdir&cid={#$cate_id#}" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=webdir&cid={#$cate_id#}" />
{#else#}
<meta name="apple-mobile-web-app-title" content="网站目录 - {#$options.site_name#}" />
<link rel="canonical" href="{#$site_url#}?mod=webdir" />
<link rel="alternate" media="only screen and (max-width: 640px)" href="{#$site_url#}?mod=webdir" />
{#/if#}
<meta name="theme-color" content="#6f42c1" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />

<!-- SEO优化 - 结构化数据：网站分类 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "{#if $cate_id > 0#}{#$cate_name#}{#else#}网站目录{#/if#}",
    "description": "{#$site_description#}",
    "url": "{#$site_url#}?mod=webdir{#if $cate_id > 0#}&cid={#$cate_id#}{#/if#}",
    "mainEntity": {
        "@type": "ItemList",
        "name": "{#if $cate_id > 0#}{#$cate_name#}网站列表{#else#}网站目录列表{#/if#}",
        "numberOfItems": "{#$total#}",
        "itemListElement": [
            {#foreach from=$websites item=web name=weblist#}
            {
                "@type": "ListItem",
                "position": {#$smarty.foreach.weblist.iteration#},
                "item": {
                    "@type": "WebSite",
                    "name": "{#$web.web_name|escape:'javascript'#}",
                    "url": "http://{#$web.web_url#}",
                    "description": "{#$web.web_intro|escape:'javascript'#}"
                }
            }{#if !$smarty.foreach.weblist.last#},{#/if#}
            {#/foreach#}
        ]
    }
}
</script>

<!-- SEO优化 - 结构化数据：面包屑导航 -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "首页",
            "item": "{#$site_url#}"
        },
        {
            "@type": "ListItem",
            "position": 2,
            "name": "网站目录",
            "item": "{#$site_url#}?mod=webdir"
        }
        {#if $cate_id > 0#}
        ,{
            "@type": "ListItem",
            "position": 3,
            "name": "{#$cate_name#}",
            "item": "{#$site_url#}?mod=webdir&cid={#$cate_id#}"
        }
        {#/if#}
    ]
}
</script>

<script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
        	<div id="subcate" class="clearfix">
            	<h3>{#$cate_name#}</h3>
                <ul class="scatelist">
                	{#foreach from=$categories item=sub#}
                    {#if $sub.cate_mod != 'article'#}
                    <li><a href="{#$sub.cate_link#}">{#$sub.cate_name#}</a><em>({#$sub.cate_postcount#})</em></li>
                    {#/if#}
                    {#/foreach#}
                </ul>
            </div>
            <div class="blank10"></div>
            <div id="listbox" class="clearfix">
            	<h2>{#$cate_name#}</h2>
            	<ul class="sitelist">
					{#foreach from=$websites item=w name=list#}
                	<li><a href="{#$w.web_link#}"><img src="{#$w.web_pic#}" width="100" height="80" alt="{#$w.web_name#}网站logo - {#$cate_name#}优质网站推荐" title="{#$w.web_name#}网站截图预览" class="thumb" loading="lazy" /></a><div class="info"><h3><a href="{#$w.web_link#}" title="{#$w.web_name#}">{#$w.web_name#}</a> {#if $w.is_today#}<span class="new-icon">new</span>{#/if#} {#if $w.web_ispay == 1#}<img src="{#$site_root#}public/images/attr/audit.gif" alt="VIP认证网站" title="VIP认证网站" border="0">{#/if#} {#if $w.web_istop == 1#}<img src="{#$site_root#}public/images/attr/top.gif" alt="置顶推荐网站" title="置顶推荐网站" border="0">{#/if#} {#if $w.web_isbest == 1#}<img src="{#$site_root#}public/images/attr/best.gif" alt="精品推荐网站" title="精品推荐网站" border="0">{#/if#}</h3><p>{#$w.web_intro#}</p><address>
  <!-- 仅占位，无 <a> -->
  <span id="placeholder-{#$w.web_id#}"
      class="link-placeholder"
      data-furl="{#$w.web_furl#}"     <!-- 跳转地址 -->
      data-text="{#$w.web_url#}"      <!-- 显示文字：一定要有 -->
      style="color:#666;">
      链接检测中…
</span>

  - {#$w.web_ctime#} -
  <a href="javascript:;" class="addfav"
     onClick="addfav({#$w.web_id#})" title="点击收藏">收藏</a>
</address></div></li>
                	{#foreachelse#}
                	<li>该目录下无任何内容！</li>
                	{#/foreach#}
				</ul>
            	<div class="showpage">{#$showpage#}</div>
            </div>
        </div>
        <div id="mainbox-right">
            
            <div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, true) item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}网站logo - VIP优质网站" title="{#$quick.web_name#}网站截图预览" loading="lazy" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a> {#if $quick.is_today#}<span class="new-icon">new</span>{#/if#}</strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
            
            <div class="blank10"></div>
            
            <div id="bestweb" class="mag">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}网站logo - 推荐优质网站" title="{#$best.web_name#}网站截图预览" loading="lazy" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a> {#if $best.is_today#}<span class="new-icon">new</span>{#/if#}</strong><p>{#$best.web_intro#}</p><address><a href="{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        	<!--<div class="ad250x250">{#get_adcode(7)#}</div>-->
            <div class="blank10"></div>
            <div id="bestart">
            	<h3>推荐资讯</h3>
                <ul class="artlist_b">
                	{#foreach from=get_articles(0, 14) item=art#}
                	<li><a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>
<script>
/* =========================================
   站点链接批量检测脚本
   ========================================= */

document.addEventListener('DOMContentLoaded', () => {

  /* ---- 配置区 ---- */
  const STATUS_TEXT = {
    200: '正常200',
    301: '跳转301',
    302: '跳转302',
    403: '禁止访问403',
    404: '未找到404',
    500: '服务器错误500',
    503: '服务不可用503'
  };
  const BLOCK_CODES     = [403, 404, 500, 503]; // 这些状态隐藏链接
  const CHECK_INTERVAL  = 300000;               // 5 分钟重检一次
  const CHECK_ENDPOINT  = '/module/status_check.php?url=';

  /* ---- 工具函数 ---- */
function makeLink(url, rawText, wid = '') {
  /* ① 显示文字：先去协议，再去尾部 “/” */
  const displayText = (rawText || url)
                        .replace(/^https?:\/\//i, '') // 去掉 http:// 或 https://
                        .replace(/\/$/, '');          // 如果最后还有 / 就去掉

  /* ② 生成链接 */
  const a = document.createElement('a');
  a.href        = url;          // href 仍保留协议
  a.target      = '_blank';
  a.className   = 'visit';
  a.textContent = displayText;  // 用户看到的文本
  if (wid && typeof clickout === 'function') {
    a.addEventListener('click', () => clickout(wid));
  }
  return a;
}

  async function fetchStatus(url) {
    try {
      const res = await fetch(CHECK_ENDPOINT + encodeURIComponent(url));

      if (!res.ok) {
        throw new Error(`HTTP ${res.status}: ${res.statusText}`);
      }

      const data = await res.json();

      if (data.error) {
        console.error('状态检测API错误:', data.error, data.debug || '');
        throw new Error(data.error);
      }

      const code = Number(data.status);
      if (!code) {
        console.error('API返回数据异常:', data);
        throw new Error('接口返回缺少 status 字段');
      }

      return code;
    } catch (error) {
      console.error('链接状态检测失败:', {
        url: url,
        endpoint: CHECK_ENDPOINT + encodeURIComponent(url),
        error: error.message
      });
      throw error;
    }
  }

  /* ---- 检测：列表页 placeholder ---- */
  async function checkPlaceholder(ph) {
    const url = ph.dataset.furl;
    const txt = ph.dataset.text || url;               // 兜底：无 data-text 时用 URL
    const wid = ph.id.startsWith('placeholder-')
                ? ph.id.slice('placeholder-'.length)
                : '';

    // 初始提示
    ph.textContent = '链接检测中…';
    ph.style.color = '#666';

    try {
      const code = await fetchStatus(url);

      if (BLOCK_CODES.includes(code)) {
        // 403/404/500/503 &rarr; 红字提示，不显示链接
        ph.textContent = STATUS_TEXT[code] || `异常(${code})`;
        ph.style.color = '#f00';
      } else {
        // 正常 &rarr; 用 <a> 替换占位
        ph.replaceWith( makeLink(url, txt, wid) );
      }

    } catch (err) {
      // 网络 / JSON / CORS 等错误
      console.error('链接检测失败:', err);
      ph.textContent = '链接检测失败';
      ph.style.color = '#f00';
    }
  }

  /* ---- 检测：详情页单链 (#website-link) ---- */
  async function checkSingleLink() {
    const linkEl = document.getElementById('website-link');
    const phEl   = document.getElementById('website-link-placeholder');
    if (!linkEl || !phEl) return;   // 页面没有这对元素就跳过

    const url = linkEl.href;
    const txt = linkEl.textContent.trim();
    const wid = linkEl.id.startsWith('link-')
                ? linkEl.id.slice('link-'.length)
                : '';

    // 初始：隐藏链接、灰字提示
    linkEl.style.display    = 'none';
    phEl.textContent        = '链接检测中…';
    phEl.style.color        = '#666';
    phEl.style.display      = 'inline';

    try {
      const code = await fetchStatus(url);

      if (BLOCK_CODES.includes(code)) {
        phEl.textContent = STATUS_TEXT[code] || `异常(${code})`;
        phEl.style.color = '#f00';
      } else {
        // 正常 &rarr; 显示链接、隐藏占位
        linkEl.style.display = '';
        phEl.style.display   = 'none';
      }

    } catch (err) {
      console.error('链接检测失败:', err);
      phEl.textContent = '链接检测失败';
      phEl.style.color = '#f00';
    }
  }

  /* ---- 批量执行 ---- */
  function runBatch() {
    document.querySelectorAll('.link-placeholder').forEach(checkPlaceholder);
    checkSingleLink(); // 若有详情页单链同时处理
  }

  /* ---- 启动 ---- */
  runBatch();                       // 页面加载立即检测
  setInterval(runBatch, CHECK_INTERVAL);  // 定时重检
});
</script>

<!-- 建议的简单 CSS（放到你的公共样式里） -->
<style>
a.visit { color: #008000; }      /* 正常链接绿色 */
</style>


</body>
</html>