/**
 * 收录量显示优化脚本
 * 解决前端收录量显示和更新问题
 */

class IndexDisplayManager {
    constructor(webId) {
        this.webId = webId;
        this.updateInProgress = false;
        this.retryCount = 0;
        this.maxRetries = 3;
        
        this.init();
    }
    
    init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.startUpdate());
        } else {
            this.startUpdate();
        }
    }
    
    startUpdate() {
        console.log('开始更新收录量数据...');
        
        // 显示加载状态
        this.showLoadingState();
        
        // 并行获取所有收录量数据
        Promise.all([
            this.fetchIndexData('grank', '百度收录量'),
            this.fetchIndexData('brank', '必应收录量'),
            this.fetchIndexData('srank', '360收录量'),
            this.fetchIndexData('arank', '搜狗收录量')
        ]).then(results => {
            console.log('所有收录量数据更新完成', results);
            this.hideLoadingState();
        }).catch(error => {
            console.error('收录量数据更新失败', error);
            this.hideLoadingState();
            this.showErrorState();
        });
    }
    
    async fetchIndexData(type, name) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = `?mod=getdata&type=${type}&wid=${this.webId}&t=${Date.now()}`;
            
            script.onload = () => {
                console.log(`${name}数据加载完成`);
                document.head.removeChild(script);
                resolve({ type, name, success: true });
            };
            
            script.onerror = () => {
                console.error(`${name}数据加载失败`);
                document.head.removeChild(script);
                
                // 重试机制
                if (this.retryCount < this.maxRetries) {
                    this.retryCount++;
                    setTimeout(() => {
                        this.fetchIndexData(type, name).then(resolve).catch(reject);
                    }, 2000 * this.retryCount);
                } else {
                    reject({ type, name, error: 'Load failed' });
                }
            };
            
            document.head.appendChild(script);
        });
    }
    
    showLoadingState() {
        const indexElements = this.getIndexElements();
        indexElements.forEach(element => {
            if (element.em) {
                element.em.innerHTML = '<span style="color: #666;">获取中...</span>';
            }
        });
    }
    
    hideLoadingState() {
        // 加载状态会被实际数据覆盖，这里不需要特殊处理
    }
    
    showErrorState() {
        const indexElements = this.getIndexElements();
        indexElements.forEach(element => {
            if (element.em && element.em.textContent.includes('获取中')) {
                element.em.innerHTML = '<span style="color: #999;">暂无数据</span>';
            }
        });
    }
    
    getIndexElements() {
        const elements = [];
        const lines = document.querySelectorAll('.wdata li.line');
        
        lines.forEach(line => {
            const text = line.textContent;
            const em = line.querySelector('em');
            
            if (text.includes('百度收录量')) {
                elements.push({ type: 'baidu', em, line });
            } else if (text.includes('必应收录量')) {
                elements.push({ type: 'bing', em, line });
            } else if (text.includes('360收录量')) {
                elements.push({ type: '360', em, line });
            } else if (text.includes('搜狗收录量')) {
                elements.push({ type: 'sogou', em, line });
            }
        });
        
        return elements;
    }
    
    // 手动刷新收录量
    refresh() {
        if (this.updateInProgress) {
            console.log('更新正在进行中，请稍候...');
            return;
        }
        
        this.updateInProgress = true;
        this.retryCount = 0;
        this.startUpdate();
        
        setTimeout(() => {
            this.updateInProgress = false;
        }, 30000); // 30秒后重置状态
    }
    
    // 格式化数字显示
    static formatNumber(num) {
        if (num === 0) return '0';
        if (num < 1000) return num.toString();
        if (num < 1000000) return (num / 1000).toFixed(1) + 'K';
        return (num / 1000000).toFixed(1) + 'M';
    }
}

// 全局函数，供外部调用
window.IndexDisplayManager = IndexDisplayManager;

// 自动初始化（如果页面中有web_id变量）
document.addEventListener('DOMContentLoaded', function() {
    // 尝试从页面中获取web_id
    const webIdMatch = document.body.innerHTML.match(/web_id['"]\s*:\s*['"]?(\d+)['"]?/);
    if (webIdMatch) {
        const webId = webIdMatch[1];
        window.indexManager = new IndexDisplayManager(webId);
        
        // 添加刷新按钮（可选）
        const refreshBtn = document.createElement('button');
        refreshBtn.textContent = '刷新收录量';
        refreshBtn.style.cssText = 'position: fixed; top: 10px; right: 10px; z-index: 1000; padding: 5px 10px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;';
        refreshBtn.onclick = () => window.indexManager.refresh();
        
        // 只在开发环境显示刷新按钮
        if (location.hostname === 'localhost' || location.hostname.includes('test')) {
            document.body.appendChild(refreshBtn);
        }
    }
});

// 兼容旧的更新方式
window.updateIndexDisplay = function(type, value) {
    const lines = document.querySelectorAll('.wdata li.line');
    const typeMap = {
        'grank': '百度收录量',
        'brank': '必应收录量', 
        'srank': '360收录量',
        'arank': '搜狗收录量'
    };
    
    const targetText = typeMap[type];
    if (!targetText) return;
    
    lines.forEach(line => {
        if (line.textContent.includes(targetText)) {
            const em = line.querySelector('em');
            if (em) {
                em.textContent = IndexDisplayManager.formatNumber(value);
                em.style.color = value > 0 ? '#083' : '#999';
            }
        }
    });
};
