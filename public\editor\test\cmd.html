<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>KindEditor Unittest</title>
		<link rel="stylesheet" href="../lib/qunit/qunit.css" />
		<!-- <script src="../lib/firebug-lite/build/firebug-lite.js#startOpened"></script> -->
		<script src="../lib/qunit/qunit.js"></script>
		<!-- include src files -->
		<script src="../src/core.js"></script>
		<script src="../src/config.js"></script>
		<script src="../src/event.js"></script>
		<script src="../src/html.js"></script>
		<script src="../src/selector.js"></script>
		<script src="../src/node.js"></script>
		<script src="../src/range.js"></script>
		<script src="../src/cmd.js"></script>
	</head>
	<body>
		<h1 id="qunit-header">KindEditor Unittest</h1>
		<h2 id="qunit-userAgent"></h2>
		<ol id="qunit-tests"></ol>

		<!-- test data 01 -->
		<div id="test-data-01" class="test-class" style="display:none;">
			<p>abcd<strong>efg</strong>hijk<img src="./data/logo_180_30.gif" border="0" />xyz<br />
01<span style="color:red;font-size:14px;">23456</span>789<br /></p>
			<div>div area</div>
			<div id="escaped-id:.">div area 2</div>
			<input type="text"  name="escaped-name:." value="" />
		</div>

		<!-- include test files -->
		<script src="cmd.js"></script>
	</body>
</html>
