<?php
/**
 * 价格查询API接口
 */

header('Content-Type: application/json; charset=utf-8');

require('common.php');
require(APP_PATH.'module/payment_price.php');

$service_type = intval($_GET['type']);
$months = intval($_GET['months']);

$response = array('success' => false, 'amount' => 0, 'message' => '');

try {
    if (!$service_type || !$months) {
        throw new Exception('参数错误');
    }
    
    $calculation = calculate_payment($service_type, $months, 0);
    if (!$calculation) {
        throw new Exception('未找到价格配置');
    }
    
    $response['success'] = true;
    $response['amount'] = $calculation['amount'];
    $response['price_config'] = $calculation['price_config'];
    
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response);
?>
