# 首页、分类浏览、数据归档、最近更新、排行榜、意见反馈
if ($uri ~ "^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(\.html|/?)") {
    rewrite ^/(index|webdir|weblink|article|category|update|archives|top|feedback|link|rssfeed|sitemap)(\.html|/?) /index.php?mod=$1 last;
}

# 最近更新和数据归档
if ($uri ~ "^/(update|archives)(-|/)(\d+)(-|/)(\d+)(\.html|/?)") {
    rewrite ^/(update|archives)(-|/)(\d+)(-|/)(\d+)(\.html|/?) /index.php?mod=$1&date=$3&page=$5 last;
}

# 站内搜索
if ($uri ~ "^/search(-|/)(name|url|tags|baidu|intro|br|pr|sr)(-|/)(.*)(-|/)?(\d+)?(\.html|/?)") {
    rewrite ^/search(-|/)(name|url|tags|baidu|intro|br|pr|sr)(-|/)(.*)(-|/)?(\d+)?(\.html|/?) /index.php?mod=search&type=$2&query=$4&page=$6 last;
}

# 站点详细 - SEO友好URL（包含网站名称和域名）
if ($uri ~ "^/siteinfo(-|/)(\d+)(-|/)([^-]+)(-|/)([^.]+)(\.html|/?)") {
    rewrite ^/siteinfo(-|/)(\d+)(-|/)([^-]+)(-|/)([^.]+)(\.html|/?) /index.php?mod=siteinfo&wid=$2 last;
}
# 站点详细、文章详细、链接详细、单页 - 兼容原有格式
if ($uri ~ "^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?)") {
    rewrite ^/(siteinfo|artinfo|linkinfo|diypage)(-|/)(\d+)(\.html|/?) /index.php?mod=$1&wid=$3 last;
}

# RSS
if ($uri ~ "^/rssfeed(-|/)(.+)(-|/)?(\d+)?(\.html|/?)") {
    rewrite ^/rssfeed(-|/)(.+)(-|/)?(\d+)?(\.html|/?) /index.php?mod=rssfeed&type=$2&cid=$4 last;
}

# SiteMap
if ($uri ~ "^/sitemap(-|/)(.+)(\.html|/?)") {
    rewrite ^/sitemap(-|/)(.+)(\.html|/?) /index.php?mod=sitemap&cid=$2 last;
}

# 分类目录
if ($uri ~ "^/(webdir|article)(-|/)(.+)(-|/)(\d+)(-|/)(\d+)(\.html|/?)") {
    rewrite ^/(webdir|article)(-|/)(.+)(-|/)(\d+)(-|/)(\d+)(\.html|/?) /index.php?mod=$1&cid=$5&page=$7 last;
}

# PHP 处理
if (!-e $request_filename) {
    rewrite ^ /index.php last;
}