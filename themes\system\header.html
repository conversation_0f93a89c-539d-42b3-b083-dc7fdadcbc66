<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>{#$pagetitle#}</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/page.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
<script type="text/javascript" src="../public/scripts/admin.js"></script>
<!--<script type="text/javascript" src="../public/editor123/kindeditor-min.js"></script>
<script type="text/javascript" src="../public/editor123/lang/zh_CN.js"></script>-->
<script type="text/javascript" src="../public/editor/kindeditor-all-min.js?v=4.1.12"></script>
<script type="text/javascript" src="../public/editor/lang/zh_CN.js?v=4.1.12"></script>
<script type="text/javascript">
$(document).ready(function(){
	$('.listbox tr').mouseover(function() {
		$(this).addClass('over');
	});
	
	$('.listbox tr').mouseout(function() {
		$(this).removeClass('over');
	});
});
</script>
</head>

<body>
<div id="wrapper">