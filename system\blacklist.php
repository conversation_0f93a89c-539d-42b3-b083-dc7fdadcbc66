<?php
/*
 * 黑名单管理页面
 * 显示黑名单网站列表，支持分类筛选和详情查看
 */

require('common.php');

$action = trim($_GET['act']);
$fileurl = 'blacklist.php';
$pagetitle = '黑名单管理';
$tempfile = 'blacklist.html';

// 获取黑名单分类
function get_blacklist_categories() {
    global $DB;
    
    $categories = array();
    $categories[0] = '其他';
    $categories[1] = '违法违规';
    $categories[2] = '色情内容';
    $categories[3] = '赌博博彩';
    $categories[4] = '诈骗欺诈';
    $categories[5] = '恶意软件';
    $categories[6] = '垃圾信息';
    $categories[7] = '版权侵权';
    $categories[8] = '政治敏感';
    
    return $categories;
}

// 获取黑名单网站列表 - 后台管理版本
function get_blacklist_websites_admin($category = '', $page = 1, $pagesize = 20) {
    global $DB;

    $where = "web_status = 1";
    if ($category !== '' && $category >= 0) {
        $where .= " AND web_blacklist_category = " . intval($category);
    }

    $start = ($page - 1) * $pagesize;

    $sql = "SELECT web_id, web_name, web_url, web_blacklist_reason, web_blacklist_category,
                   web_blacklist_time, web_blacklist_operator, web_ctime
            FROM " . $DB->table('websites') . "
            WHERE $where
            ORDER BY web_blacklist_time DESC
            LIMIT $start, $pagesize";

    $query = $DB->query($sql);
    $websites = array();

    while ($row = $DB->fetch_array($query)) {
        // 保存原始时间戳用于判断是否为当天
        $original_ctime = $row['web_ctime'];

        // 判断是否为当天发表的网站
        $row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

        $websites[] = $row;
    }

    return $websites;
}

// 获取黑名单网站总数
function get_blacklist_count($category = '') {
    global $DB;
    
    $where = "web_status = 1";
    if ($category !== '' && $category >= 0) {
        $where .= " AND web_blacklist_category = " . intval($category);
    }
    
    return $DB->get_count($DB->table('websites'), $where);
}

// 处理列表显示
if ($action == '' || $action == 'list') {
    $category = isset($_GET['category']) ? intval($_GET['category']) : '';
    $page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
    $pagesize = 20;
    
    $categories = get_blacklist_categories();
    $websites = get_blacklist_websites_admin($category, $page, $pagesize);
    $total = get_blacklist_count($category);
    
    // 处理网站数据
    foreach ($websites as &$website) {
        $website['category_name'] = $categories[$website['web_blacklist_category']];
        $website['blacklist_time_formatted'] = $website['web_blacklist_time'] ? date('Y-m-d H:i:s', $website['web_blacklist_time']) : '';
        $website['ctime_formatted'] = date('Y-m-d', $website['web_ctime']);
        $website['reason_short'] = mb_strlen($website['web_blacklist_reason']) > 50 ? 
                                   mb_substr($website['web_blacklist_reason'], 0, 50) . '...' : 
                                   $website['web_blacklist_reason'];
    }
    
    // 分页
    $showpage = '';
    if ($total > $pagesize) {
        $pages = ceil($total / $pagesize);
        $showpage = '<div class="pagination">';
        
        if ($page > 1) {
            $prev_page = $page - 1;
            $showpage .= '<a href="' . $fileurl . '?act=list&category=' . $category . '&page=' . $prev_page . '">上一页</a> ';
        }
        
        for ($i = 1; $i <= $pages; $i++) {
            if ($i == $page) {
                $showpage .= '<strong>' . $i . '</strong> ';
            } else {
                $showpage .= '<a href="' . $fileurl . '?act=list&category=' . $category . '&page=' . $i . '">' . $i . '</a> ';
            }
        }
        
        if ($page < $pages) {
            $next_page = $page + 1;
            $showpage .= '<a href="' . $fileurl . '?act=list&category=' . $category . '&page=' . $next_page . '">下一页</a>';
        }
        
        $showpage .= '</div>';
    }
    
    $smarty->assign('websites', $websites);
    $smarty->assign('categories', $categories);
    $smarty->assign('current_category', $category);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
}

// 处理详情显示
if ($action == 'detail') {
    $web_id = intval($_GET['id']);
    
    if ($web_id <= 0) {
        msgbox('参数错误！');
    }
    
    $sql = "SELECT w.*, c.cate_name 
            FROM " . $DB->table('websites') . " w 
            LEFT JOIN " . $DB->table('categories') . " c ON w.cate_id = c.cate_id 
            WHERE w.web_id = $web_id AND w.web_status = 1";
    
    $website = $DB->fetch_one($sql);
    
    if (!$website) {
        msgbox('指定的黑名单网站不存在！');
    }
    
    $categories = get_blacklist_categories();
    $website['category_name'] = $categories[$website['web_blacklist_category']];
    $website['blacklist_time_formatted'] = $website['web_blacklist_time'] ? date('Y-m-d H:i:s', $website['web_blacklist_time']) : '';
    $website['ctime_formatted'] = date('Y-m-d', $website['web_ctime']);
    
    $smarty->assign('website', $website);
    $tempfile = 'blacklist_detail.html';
}

// 处理恢复操作
if ($action == 'restore') {
    $web_id = intval($_GET['id']);

    if ($web_id <= 0) {
        msgbox('参数错误！');
    }

    // 将网站状态改为待审核，清空黑名单信息
    $update_data = array(
        'web_status' => 2,
        'web_blacklist_reason' => '',
        'web_blacklist_category' => 0,
        'web_blacklist_time' => 0,
        'web_blacklist_operator' => ''
    );

    $where = array('web_id' => $web_id);
    $result = $DB->update($DB->table('websites'), $update_data, $where);

    if ($result) {
        msgbox('网站已恢复为待审核状态！', $fileurl);
    } else {
        msgbox('操作失败，请重试！');
    }
}

// 处理批量恢复操作
if ($action == 'batch_restore') {
    $web_ids = isset($_POST['web_id']) ? $_POST['web_id'] : array();

    if (empty($web_ids)) {
        msgbox('请选择要恢复的网站！');
    }

    $success_count = 0;
    $update_data = array(
        'web_status' => 2,
        'web_blacklist_reason' => '',
        'web_blacklist_category' => 0,
        'web_blacklist_time' => 0,
        'web_blacklist_operator' => ''
    );

    foreach ($web_ids as $web_id) {
        $web_id = intval($web_id);
        if ($web_id > 0) {
            $where = array('web_id' => $web_id);
            if ($DB->update($DB->table('websites'), $update_data, $where)) {
                $success_count++;
            }
        }
    }

    msgbox("成功恢复 {$success_count} 个网站为待审核状态！", $fileurl);
}

// 处理批量删除操作
if ($action == 'batch_delete') {
    $web_ids = isset($_POST['web_id']) ? $_POST['web_id'] : array();

    if (empty($web_ids)) {
        msgbox('请选择要删除的网站！');
    }

    $success_count = 0;
    foreach ($web_ids as $web_id) {
        $web_id = intval($web_id);
        if ($web_id > 0) {
            $where = array('web_id' => $web_id);
            if ($DB->delete($DB->table('websites'), $where)) {
                $success_count++;
            }
        }
    }

    msgbox("成功删除 {$success_count} 个黑名单网站！", $fileurl);
}

smarty_output($tempfile);
?>
