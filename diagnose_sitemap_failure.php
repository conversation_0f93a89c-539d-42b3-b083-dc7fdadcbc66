<?php
/**
 * 诊断站点地图失败的具体原因
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('max_execution_time', 60); // 设置60秒超时

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

// 创建一个模拟原始函数但带有详细日志的版本
function diagnose_website_sitemap($cate_id = 0) {
    global $DB, $options;
    
    $log = array();
    $log[] = "开始生成站点地图 - " . date('H:i:s');
    
    try {
        header("Content-Type: application/xml;");
        echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
        echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
        $log[] = "XML头部生成成功";
        
        // 首页
        echo "<url>\n";
        echo "<loc>".$options['site_url']."</loc>\n";
        echo "<lastmod>".date('c')."</lastmod>\n";
        echo "<changefreq>daily</changefreq>\n";
        echo "<priority>1.0</priority>\n";
        echo "</url>\n";
        $log[] = "首页URL生成成功";
        
        // 网站数据查询
        $log[] = "开始查询网站数据 - " . date('H:i:s');
        $where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
        $sql .= " WHERE $where ORDER BY web_id DESC LIMIT 20"; // 先测试20个
        $query = $DB->query($sql);
        $log[] = "网站数据查询完成，开始处理结果";
        
        $count = 0;
        if ($query) {
            while ($row = $DB->fetch_array($query)) {
                try {
                    $web_link = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
                    echo "<url>\n";
                    echo "<loc>".$web_link."</loc>\n";
                    echo "<lastmod>".date('c', $row['web_ctime'])."</lastmod>\n";
                    echo "<changefreq>weekly</changefreq>\n";
                    echo "<priority>0.6</priority>\n";
                    echo "</url>\n";
                    $count++;
                } catch (Exception $e) {
                    $log[] = "网站ID {$row['web_id']} 处理失败: " . $e->getMessage();
                    continue;
                }
            }
            $DB->free_result($query);
        }
        $log[] = "网站数据处理完成，共处理 $count 个网站";
        
        // 分类数据
        $log[] = "开始处理分类数据 - " . date('H:i:s');
        try {
            $categories = get_all_category();
            $cate_count = 0;
            foreach ($categories as $cate) {
                if ($cate['cate_mod'] == 'webdir' && $cate_count < 5) { // 只处理5个分类
                    try {
                        $cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('webdir', $cate['cate_id']));
                        echo "<url>\n";
                        echo "<loc>".$cate_url."</loc>\n";
                        echo "<lastmod>".date('c')."</lastmod>\n";
                        echo "<changefreq>weekly</changefreq>\n";
                        echo "<priority>0.7</priority>\n";
                        echo "</url>\n";
                        $cate_count++;
                    } catch (Exception $e) {
                        $log[] = "分类ID {$cate['cate_id']} 处理失败: " . $e->getMessage();
                        continue;
                    }
                }
            }
            $log[] = "分类数据处理完成，共处理 $cate_count 个分类";
        } catch (Exception $e) {
            $log[] = "分类数据查询失败: " . $e->getMessage();
        }
        
        // 文章数据（简化版）
        $log[] = "开始处理文章数据 - " . date('H:i:s');
        try {
            $sql = "SELECT art_id, art_ctime FROM ".$DB->table('articles');
            $sql .= " WHERE art_status=3 ORDER BY art_id DESC LIMIT 5"; // 只处理5个文章
            $query = $DB->query($sql);
            $art_count = 0;
            if ($query) {
                while ($row = $DB->fetch_array($query)) {
                    try {
                        $art_link = str_replace('&', '&amp;', get_article_url($row['art_id'], true));
                        echo "<url>\n";
                        echo "<loc>".$art_link."</loc>\n";
                        echo "<lastmod>".date('c', $row['art_ctime'])."</lastmod>\n";
                        echo "<changefreq>weekly</changefreq>\n";
                        echo "<priority>0.6</priority>\n";
                        echo "</url>\n";
                        $art_count++;
                    } catch (Exception $e) {
                        $log[] = "文章ID {$row['art_id']} 处理失败: " . $e->getMessage();
                        continue;
                    }
                }
                $DB->free_result($query);
            }
            $log[] = "文章数据处理完成，共处理 $art_count 个文章";
        } catch (Exception $e) {
            $log[] = "文章数据查询失败: " . $e->getMessage();
        }
        
        // 友情链接数据（简化版）
        $log[] = "开始处理友情链接数据 - " . date('H:i:s');
        try {
            $sql = "SELECT link_id, link_time FROM ".$DB->table('weblinks');
            $sql .= " WHERE link_hide=0 ORDER BY link_id DESC LIMIT 3"; // 只处理3个链接
            $query = $DB->query($sql);
            $link_count = 0;
            if ($query) {
                while ($row = $DB->fetch_array($query)) {
                    try {
                        $link_url = str_replace('&', '&amp;', get_weblink_url($row['link_id'], true));
                        echo "<url>\n";
                        echo "<loc>".$link_url."</loc>\n";
                        echo "<lastmod>".date('c', $row['link_time'])."</lastmod>\n";
                        echo "<changefreq>monthly</changefreq>\n";
                        echo "<priority>0.3</priority>\n";
                        echo "</url>\n";
                        $link_count++;
                    } catch (Exception $e) {
                        $log[] = "友情链接ID {$row['link_id']} 处理失败: " . $e->getMessage();
                        continue;
                    }
                }
                $DB->free_result($query);
            }
            $log[] = "友情链接数据处理完成，共处理 $link_count 个链接";
        } catch (Exception $e) {
            $log[] = "友情链接数据查询失败: " . $e->getMessage();
        }
        
        echo "</urlset>\n";
        $log[] = "XML结束标签生成成功 - " . date('H:i:s');
        
        // 在XML注释中输出日志
        echo "<!-- 诊断日志:\n";
        foreach ($log as $entry) {
            echo $entry . "\n";
        }
        echo "-->\n";
        
    } catch (Exception $e) {
        $log[] = "致命错误: " . $e->getMessage() . " - " . date('H:i:s');
        echo "<!-- 错误日志:\n";
        foreach ($log as $entry) {
            echo $entry . "\n";
        }
        echo "致命错误: " . $e->getMessage() . "\n";
        echo "-->\n";
        echo "</urlset>\n";
    }
}

// 如果是诊断请求
if (isset($_GET['diagnose'])) {
    diagnose_website_sitemap(0);
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>诊断站点地图失败原因</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>诊断站点地图失败原因</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section info">
        <h2>诊断说明</h2>
        <p>这个诊断工具会模拟原始站点地图函数的执行过程，但会：</p>
        <ul>
            <li>限制每个部分的数据量（网站20个，分类5个，文章5个，链接3个）</li>
            <li>记录每个步骤的执行时间和状态</li>
            <li>捕获并记录所有错误</li>
            <li>在XML注释中输出详细的执行日志</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>开始诊断</h2>
        <p>点击下面的链接开始诊断：</p>
        <a href="?diagnose=1" target="_blank" class="test-link">开始诊断站点地图</a>
    </div>
    
    <div class="test-section">
        <h2>如何查看诊断结果</h2>
        <ol>
            <li>点击"开始诊断站点地图"链接</li>
            <li>查看生成的XML是否完整（有结束标签）</li>
            <li>查看XML底部的注释部分，里面包含详细的执行日志</li>
            <li>如果某个步骤失败，日志会显示具体的错误信息</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>预期结果分析</h2>
        <p><strong>如果诊断版本成功：</strong></p>
        <ul>
            <li>说明问题是数据量过大导致的</li>
            <li>需要进一步减少原始函数中的数据量</li>
            <li>或者优化查询和处理逻辑</li>
        </ul>
        
        <p><strong>如果诊断版本失败：</strong></p>
        <ul>
            <li>查看日志中最后成功的步骤</li>
            <li>问题出现在该步骤之后的部分</li>
            <li>可能是特定的数据记录或函数调用有问题</li>
        </ul>
    </div>
</body>
</html>
