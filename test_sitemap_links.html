<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>站点地图链接测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-link { display: block; margin: 10px 0; padding: 15px; border: 1px solid #ddd; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background-color: #f8f9fa; }
        .status { font-weight: bold; margin-top: 5px; }
        .success { border-color: #28a745; background-color: #d4edda; }
        .error { border-color: #dc3545; background-color: #f8d7da; }
        .loading { border-color: #ffc107; background-color: #fff3cd; }
        .info { color: #666; font-size: 0.9em; }
    </style>
</head>
<body>
    <h1>站点地图链接测试</h1>
    <p>测试时间: <span id="test-time"></span></p>
    
    <div id="test-results">
        <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link" data-type="webdir">
            <strong>网站目录站点地图 (webdir)</strong><br>
            <span class="info">https://www.95dir.com/?mod=sitemap&type=webdir&format=xml</span>
            <div class="status" id="status-webdir">等待测试...</div>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=weblink&format=xml" target="_blank" class="test-link" data-type="weblink">
            <strong>友情链接站点地图 (weblink)</strong><br>
            <span class="info">https://www.95dir.com/?mod=sitemap&type=weblink&format=xml</span>
            <div class="status" id="status-weblink">等待测试...</div>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=article&format=xml" target="_blank" class="test-link" data-type="article">
            <strong>文章站点地图 (article)</strong><br>
            <span class="info">https://www.95dir.com/?mod=sitemap&type=article&format=xml</span>
            <div class="status" id="status-article">等待测试...</div>
        </a>
        
        <a href="https://www.95dir.com/?mod=sitemap&type=all&format=xml" target="_blank" class="test-link" data-type="all">
            <strong>综合站点地图 (all)</strong><br>
            <span class="info">https://www.95dir.com/?mod=sitemap&type=all&format=xml</span>
            <div class="status" id="status-all">等待测试...</div>
        </a>
    </div>
    
    <div style="margin-top: 30px; padding: 15px; background-color: #f8f9fa; border-radius: 5px;">
        <h3>测试说明</h3>
        <ul>
            <li>点击链接可以在新窗口中查看站点地图内容</li>
            <li>绿色表示测试通过，红色表示测试失败</li>
            <li>测试会检查XML格式和内容完整性</li>
            <li>如果显示"✓ 成功"，说明站点地图已修复</li>
        </ul>
    </div>
    
    <script>
        document.getElementById('test-time').textContent = new Date().toLocaleString();
        
        async function testSitemap(type) {
            const link = document.querySelector(`[data-type="${type}"]`);
            const status = document.getElementById(`status-${type}`);
            const url = link.href;
            
            try {
                status.textContent = '测试中...';
                link.classList.add('loading');
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/xml,text/xml,*/*'
                    }
                });
                
                const text = await response.text();
                
                link.classList.remove('loading');
                
                if (response.ok) {
                    const hasXmlStart = text.trim().startsWith('<?xml');
                    const hasUrlset = text.includes('</urlset>');
                    const hasSitemapIndex = text.includes('</sitemapindex>');
                    const hasNamespace = text.includes('xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"');
                    
                    if (hasXmlStart && (hasUrlset || hasSitemapIndex) && hasNamespace) {
                        const urlCount = (text.match(/<url>/g) || []).length;
                        const sitemapCount = (text.match(/<sitemap>/g) || []).length;
                        const totalCount = urlCount + sitemapCount;
                        
                        link.classList.add('success');
                        status.innerHTML = `✓ 成功 - ${totalCount} 个链接 (${text.length} 字节)`;
                    } else {
                        link.classList.add('error');
                        status.innerHTML = `✗ XML格式错误<br>开始: ${hasXmlStart ? '✓' : '✗'} | 结束: ${(hasUrlset || hasSitemapIndex) ? '✓' : '✗'} | 命名空间: ${hasNamespace ? '✓' : '✗'}`;
                    }
                } else {
                    link.classList.add('error');
                    status.textContent = `✗ HTTP错误 ${response.status}`;
                }
            } catch (error) {
                link.classList.remove('loading');
                link.classList.add('error');
                status.textContent = `✗ 网络错误: ${error.message}`;
            }
        }
        
        async function runAllTests() {
            const types = ['webdir', 'weblink', 'article', 'all'];
            
            for (const type of types) {
                await testSitemap(type);
                // 等待1秒再测试下一个
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
        
        // 页面加载后自动开始测试
        window.addEventListener('load', () => {
            setTimeout(runAllTests, 1000);
        });
    </script>
</body>
</html>
