<?php
/**
 * 站点地图检查脚本
 * 检查所有站点地图链接的可访问性
 */

// 站点地图URL列表
$sitemap_urls = array(
    'all' => '/?mod=sitemap&type=all&format=xml',
    'webdir' => '/?mod=sitemap&type=webdir&format=xml',
    'weblink' => '/?mod=sitemap&type=weblink&format=xml',
    'article' => '/?mod=sitemap&type=article&format=xml',
    'article_detail' => '/?mod=sitemap&type=article_detail&format=xml',
    'category' => '/?mod=sitemap&type=category&format=xml',
    'vip' => '/?mod=sitemap&type=vip&format=xml',
    'tags' => '/?mod=sitemap&type=tags&format=xml',
    'pending' => '/?mod=sitemap&type=pending&format=xml',
    'rejected' => '/?mod=sitemap&type=rejected&format=xml',
    'blacklist' => '/?mod=sitemap&type=blacklist&format=xml',
    'search' => '/?mod=sitemap&type=search&format=xml',
    'member' => '/?mod=sitemap&type=member&format=xml',
    'other' => '/?mod=sitemap&type=other&format=xml'
);

$base_url = 'https://www.95dir.com';

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>站点地图链接检查</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".check-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }\n";
echo ".success { background-color: #d4edda; border-color: #c3e6cb; }\n";
echo ".error { background-color: #f8d7da; border-color: #f5c6cb; }\n";
echo ".url { font-family: monospace; color: #007bff; }\n";
echo ".summary { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border: 1px solid #dee2e6; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>站点地图链接检查</h1>\n";
echo "<p>检查时间: " . date('Y-m-d H:i:s') . "</p>\n";
echo "<p>基础URL: $base_url</p>\n";

$success_count = 0;
$total_count = count($sitemap_urls);

foreach ($sitemap_urls as $type => $url) {
    echo "<div class='check-item'>\n";
    echo "<h3>$type 站点地图</h3>\n";
    
    $full_url = $base_url . $url;
    echo "<p class='url'>URL: <a href='$full_url' target='_blank'>$full_url</a></p>\n";
    
    // 使用curl检查URL
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $full_url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "<p style='color: red;'>✗ 连接错误: $error</p>\n";
        echo "</div>\n";
        continue;
    }
    
    if ($http_code == 200) {
        // 检查是否为有效的XML
        $is_valid_xml = false;
        $xml_start = strpos($response, '<?xml') === 0;
        $has_urlset = strpos($response, '</urlset>') !== false;
        $has_sitemapindex = strpos($response, '</sitemapindex>') !== false;
        $has_xmlns = strpos($response, 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"') !== false;

        if ($xml_start && ($has_urlset || $has_sitemapindex) && $has_xmlns) {
            $is_valid_xml = true;
        }

        if ($is_valid_xml) {
            echo "<p style='color: green;'>✓ 访问成功 - HTTP $http_code</p>\n";
            echo "<p style='color: green;'>✓ 有效的XML格式</p>\n";

            // 计算URL数量
            $url_count = substr_count($response, '<url>') + substr_count($response, '<sitemap>');
            echo "<p>包含链接数量: $url_count</p>\n";

            // 显示响应大小
            echo "<p>响应大小: " . strlen($response) . " 字节</p>\n";

            // 尝试解析XML以进一步验证
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($response);
            if ($xml !== false) {
                echo "<p style='color: green;'>✓ XML解析成功</p>\n";
            } else {
                echo "<p style='color: orange;'>⚠ XML格式警告，但基本结构正确</p>\n";
                $errors = libxml_get_errors();
                if (!empty($errors)) {
                    echo "<p>XML错误: " . htmlspecialchars($errors[0]->message) . "</p>\n";
                }
            }
            libxml_clear_errors();

            $success_count++;
        } else {
            echo "<p style='color: red;'>✗ 访问成功但格式不正确 - HTTP $http_code</p>\n";
            echo "<p>XML开始: " . ($xml_start ? '✓' : '✗') . "</p>\n";
            echo "<p>有urlset结束: " . ($has_urlset ? '✓' : '✗') . "</p>\n";
            echo "<p>有sitemapindex结束: " . ($has_sitemapindex ? '✓' : '✗') . "</p>\n";
            echo "<p>有正确命名空间: " . ($has_xmlns ? '✓' : '✗') . "</p>\n";
            echo "<p>响应内容: " . htmlspecialchars(substr($response, 0, 300)) . "...</p>\n";
        }
    } else {
        echo "<p style='color: red;'>✗ 访问失败 - HTTP $http_code</p>\n";
        if ($response) {
            echo "<p>响应内容: " . htmlspecialchars(substr($response, 0, 200)) . "...</p>\n";
        }
    }
    
    echo "</div>\n";
}

echo "<div class='summary'>\n";
echo "<h2>检查总结</h2>\n";
echo "<p><strong>总检查项目:</strong> $total_count</p>\n";
echo "<p><strong>成功项目:</strong> $success_count</p>\n";
echo "<p><strong>失败项目:</strong> " . ($total_count - $success_count) . "</p>\n";
echo "<p><strong>成功率:</strong> " . round(($success_count / $total_count) * 100, 2) . "%</p>\n";

if ($success_count == $total_count) {
    echo "<p style='color: green; font-weight: bold;'>🎉 所有站点地图链接检查通过！</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ 部分站点地图链接检查失败，请检查相关配置。</p>\n";
}

echo "</div>\n";

echo "<div class='summary'>\n";
echo "<h2>站点地图功能说明</h2>\n";
echo "<ul>\n";
echo "<li><strong>all:</strong> 包含所有主要页面的综合站点地图</li>\n";
echo "<li><strong>webdir:</strong> 网站目录相关页面</li>\n";
echo "<li><strong>weblink:</strong> 友情链接相关页面</li>\n";
echo "<li><strong>article:</strong> 文章列表和分类页面</li>\n";
echo "<li><strong>article_detail:</strong> 文章详情页面（按浏览量排序）</li>\n";
echo "<li><strong>category:</strong> 所有分类页面</li>\n";
echo "<li><strong>vip:</strong> VIP网站相关页面</li>\n";
echo "<li><strong>tags:</strong> 热门标签页面</li>\n";
echo "<li><strong>pending:</strong> 待审核网站页面</li>\n";
echo "<li><strong>rejected:</strong> 审核不通过网站页面</li>\n";
echo "<li><strong>blacklist:</strong> 黑名单网站页面</li>\n";
echo "<li><strong>search:</strong> 搜索相关页面</li>\n";
echo "<li><strong>member:</strong> 会员中心页面</li>\n";
echo "<li><strong>other:</strong> 其他功能页面</li>\n";
echo "</ul>\n";
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";
?>
