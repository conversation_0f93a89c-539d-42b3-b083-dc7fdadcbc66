<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32">
  <defs>
    <linearGradient id="goGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#00ADD8"/>
      <stop offset="100%" stop-color="#007D9C"/>
    </linearGradient>
    <linearGradient id="reactGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#61DAFB"/>
      <stop offset="100%" stop-color="#21A0C4"/>
    </linearGradient>
    <linearGradient id="centerGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#FF6B6B"/>
      <stop offset="100%" stop-color="#EE5A52"/>
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="16" cy="16" r="15" fill="#1a1a1a" stroke="#333" stroke-width="1"/>
  
  <!-- Go语言标识 (左侧) -->
  <path d="M6 12 L12 8 L12 16 Z" fill="url(#goGradient)" opacity="0.9"/>
  <circle cx="8" cy="12" r="1.5" fill="#ffffff"/>
  
  <!-- React标识 (右侧) -->
  <ellipse cx="24" cy="16" rx="4" ry="2" fill="none" stroke="url(#reactGradient)" stroke-width="1.5" opacity="0.8"/>
  <ellipse cx="24" cy="16" rx="4" ry="2" fill="none" stroke="url(#reactGradient)" stroke-width="1.5" opacity="0.8" transform="rotate(60 24 16)"/>
  <ellipse cx="24" cy="16" rx="4" ry="2" fill="none" stroke="url(#reactGradient)" stroke-width="1.5" opacity="0.8" transform="rotate(-60 24 16)"/>
  <circle cx="24" cy="16" r="1.5" fill="url(#reactGradient)"/>
  
  <!-- 中心连接点 -->
  <circle cx="16" cy="16" r="2" fill="url(#centerGradient)"/>
  
  <!-- 连接线 -->
  <line x1="12" y1="12" x2="14" y2="14" stroke="url(#centerGradient)" stroke-width="2" opacity="0.7"/>
  <line x1="18" y1="16" x2="20" y2="16" stroke="url(#centerGradient)" stroke-width="2" opacity="0.7"/>
  
  <!-- 装饰性代码符号 -->
  <text x="16" y="26" font-family="monospace" font-size="8" fill="#666" text-anchor="middle">&lt;/&gt;</text>
</svg>