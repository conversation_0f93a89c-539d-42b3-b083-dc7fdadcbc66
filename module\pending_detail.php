<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '待审核网站详情';
$pageurl = '?mod=pending_detail';
$tempfile = 'pending_detail.html';
$table = $DB->table('webdata');

$web_id = intval($_GET['id']);
$cache_id = $web_id;

if (!$web_id) {
    redirect('?mod=pending');
}

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 只查询待审核状态的网站 (web_status=2)
	$where = "w.web_status=2 AND w.web_id=$web_id";
	$web = get_one_website($where);
	if (!$web) {
		unset($web);
		redirect('?mod=pending');
	}

	// 不增加浏览量，因为是待审核状态
	// $DB->query("UPDATE $table SET web_views=web_views+1 WHERE web_id=".$web['web_id']." LIMIT 1");

	$cate = get_one_category($web['cate_id']);
	$user = get_one_user($web['user_id']);

	$smarty->assign('site_title', $web['web_name'].' - '.$pagename.' - '.$options['site_name']);
	$smarty->assign('site_keywords', !empty($web['web_tags']) ? $web['web_tags'] : $options['site_keywords']);
	$smarty->assign('site_description', !empty($web['web_intro']) ? mb_substr(strip_tags($web['web_intro']), 0, 200, 'utf-8') : $options['site_description']);
	$smarty->assign('site_path', get_sitepath().' &raquo; <a href="?mod=pending">待审核</a> &raquo; <a href="?mod=pending&cid='.$web['cate_id'].'">'.$cate['cate_name'].'</a> &raquo; '.$web['web_name']);
	$smarty->assign('site_rss', get_rssfeed());

	$smarty->assign('cate_id', $cate['cate_id']);
	$smarty->assign('cate_name', $cate['cate_name']);
	$smarty->assign('cate_keywords', $cate['cate_keywords']);
	$smarty->assign('cate_description', $cate['cate_description']);

	// 隐私保护：移除所有网址相关信息
	// $web['web_furl'] = format_url($web['web_url']); // 注释掉
	// unset($web['web_url']); // 移除网址
	// unset($web['web_furl']); // 移除格式化网址

	// 隐私保护：网址信息设为隐藏状态
	$web['web_url'] = '隐私保护';
	$web['web_furl'] = '隐私保护';

	$web['web_pic'] = get_webthumb($web['web_pic']);
	$web['web_ip'] = '隐私保护'; // 隐藏IP信息
	$web['web_ctime'] = date('Y-m-d', $web['web_ctime']);
	$web['web_utime'] = date('Y-m-d', $web['web_utime']);

	// PageRank处理 - 待审核状态显示默认值
	$web['web_prank'] = 0;
	$web['web_grank'] = 0;
	$web['web_brank'] = 0;
	$web['web_srank'] = 0;
	$web['web_arank'] = 0;
	$web['web_views'] = 0; // 待审核状态浏览量为0
	$web['web_instat'] = 0; // 入站次数为0
	$web['web_outstat'] = 0; // 出站次数为0

	/** tags */
	$web_tags = get_format_tags($web['web_tags']);
	$smarty->assign('web_tags', $web_tags);

	// 获取同分类的其他待审核网站
	$related_pending = $DB->fetch_all("SELECT web_id, web_name, web_pic FROM ".$DB->table('websites')."
	                                  WHERE cate_id=".$web['cate_id']." AND web_status=2 AND web_id!=$web_id
	                                  ORDER BY web_ctime DESC LIMIT 8");

	// 处理相关待审核网站的图片
	foreach ($related_pending as &$rel) {
		$rel['web_pic'] = get_webthumb($rel['web_pic']);
	}

    $smarty->assign('web', $web);
	$smarty->assign('user', $user);
	$smarty->assign('related_pending', $related_pending);

	// 获取上一站下一站（同分类的待审核网站）
	$prev_pending = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=2 AND web_id<$web_id
	                               ORDER BY web_id DESC LIMIT 1");
	$next_pending = $DB->fetch_one("SELECT web_id, web_name FROM ".$DB->table('websites')."
	                               WHERE cate_id=".$web['cate_id']." AND web_status=2 AND web_id>$web_id
	                               ORDER BY web_id ASC LIMIT 1");

	// 为上一站下一站添加链接
	if ($prev_pending) {
		$prev_pending['web_link'] = '?mod=pending_detail&id='.$prev_pending['web_id'];
	}
	if ($next_pending) {
		$next_pending['web_link'] = '?mod=pending_detail&id='.$next_pending['web_id'];
	}

	$smarty->assign('prev_website', $prev_pending);
	$smarty->assign('next_website', $next_pending);

	// 相关站点显示同分类的正常网站
	$smarty->assign('related_website', get_websites($web['cate_id'], 10, false, false, 'ctime'));
}

smarty_output($tempfile, $cache_id);
?>
