/* Reset CSS */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
    font-size: 1rem;
    line-height: 1.5;
    color: #333;
    background-color: #fff;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    margin: 0;
    font-weight: 500;
    line-height: 1.2;
}

p {
    margin: 0;
    padding: 0;
}

/* Lists */
ul, ol {
    list-style: none;
    margin: 0;
    padding: 0;
}

/* Links */
a {
    color: inherit;
    text-decoration: none;
    background-color: transparent;
}

a:hover {
    text-decoration: none;
}

/* Images */
img {
    max-width: 100%;
    height: auto;
    vertical-align: middle;
    border-style: none;
}

/* Forms */
button,
input,
optgroup,
select,
textarea {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    border-radius: 0;
    -webkit-appearance: none;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}

textarea {
    overflow: auto;
    resize: vertical;
}

/* Tables */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Helper Classes */
.clearfix::after {
    content: '';
    display: table;
    clear: both;
}

.hidden {
    display: none !important;
}

.visible {
    display: block !important;
}

/* Responsive Images */
picture,
video,
canvas,
svg {
    display: block;
    max-width: 100%;
}

/* Remove tap highlight on iOS */
a,
button,
input,
select,
textarea {
    -webkit-tap-highlight-color: transparent;
}

/* Accessibility */
[hidden] {
    display: none !important;
}

[disabled] {
    cursor: not-allowed;
}

/* Print */
@media print {
    *,
    *::before,
    *::after {
        text-shadow: none !important;
        box-shadow: none !important;
    }
    
    a:not(.btn) {
        text-decoration: underline;
    }
    
    img {
        page-break-inside: avoid;
    }
    
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }
    
    h2,
    h3 {
        page-break-after: avoid;
    }
    
    @page {
        size: a3;
    }
    
    body {
        min-width: 992px !important;
    }
}

