<?php
/**
 * 最终站点地图测试
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

try {
    require(APP_PATH.'init.php');
    require(APP_PATH.'module/prelink.php');
    require(APP_PATH.'module/category.php');
    require(APP_PATH.'module/website.php');
    
    echo "<!DOCTYPE html>\n";
    echo "<html>\n";
    echo "<head>\n";
    echo "<meta charset='utf-8'>\n";
    echo "<title>最终站点地图测试</title>\n";
    echo "<style>\n";
    echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    echo ".test-result { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }\n";
    echo ".success { background-color: #d4edda; border-color: #c3e6cb; }\n";
    echo ".error { background-color: #f8d7da; border-color: #f5c6cb; }\n";
    echo ".info { background-color: #d1ecf1; border-color: #bee5eb; }\n";
    echo "pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 200px; overflow-y: auto; }\n";
    echo "</style>\n";
    echo "</head>\n";
    echo "<body>\n";
    echo "<h1>最终站点地图测试结果</h1>\n";
    echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";
    
    // 测试webdir站点地图
    echo "<div class='test-result'>\n";
    echo "<h2>Webdir 站点地图测试</h2>\n";
    
    try {
        ob_start();

        // 设置错误处理来捕获所有错误
        $errors = array();
        set_error_handler(function($severity, $message, $file, $line) use (&$errors) {
            $errors[] = "错误: $message (文件: $file, 行: $line)";
        });

        get_website_sitemap(0);

        restore_error_handler();
        $output = ob_get_clean();

        // 显示捕获的错误
        if (!empty($errors)) {
            echo "<div class='error'>\n";
            echo "<h4>捕获的错误:</h4>\n";
            foreach ($errors as $error) {
                echo "<p>" . htmlspecialchars($error) . "</p>\n";
            }
            echo "</div>\n";
        }
        
        $length = strlen($output);
        $url_count = substr_count($output, '<url>');
        $has_xml_start = strpos($output, '<?xml') === 0;
        $has_xml_end = strpos($output, '</urlset>') !== false;
        $has_namespace = strpos($output, 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"') !== false;
        
        if ($has_xml_start && $has_xml_end && $has_namespace && $url_count > 0) {
            echo "<div class='success'>\n";
            echo "<h3>✓ 测试通过</h3>\n";
            echo "<p><strong>输出长度:</strong> $length 字符</p>\n";
            echo "<p><strong>URL数量:</strong> $url_count</p>\n";
            echo "<p><strong>XML格式:</strong> 正确</p>\n";
            
            // 尝试XML解析
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($output);
            if ($xml !== false) {
                echo "<p><strong>XML解析:</strong> 成功</p>\n";
            } else {
                echo "<p><strong>XML解析:</strong> 失败</p>\n";
                $errors = libxml_get_errors();
                if (!empty($errors)) {
                    echo "<p><strong>错误:</strong> " . htmlspecialchars($errors[0]->message) . "</p>\n";
                }
            }
            libxml_clear_errors();
            
            echo "</div>\n";
        } else {
            echo "<div class='error'>\n";
            echo "<h3>✗ 测试失败</h3>\n";
            echo "<p><strong>输出长度:</strong> $length 字符</p>\n";
            echo "<p><strong>URL数量:</strong> $url_count</p>\n";
            echo "<p><strong>XML开始:</strong> " . ($has_xml_start ? '✓' : '✗') . "</p>\n";
            echo "<p><strong>XML结束:</strong> " . ($has_xml_end ? '✓' : '✗') . "</p>\n";
            echo "<p><strong>命名空间:</strong> " . ($has_namespace ? '✓' : '✗') . "</p>\n";
            echo "</div>\n";
        }
        
        // 显示输出预览
        echo "<h4>输出预览 (前500字符):</h4>\n";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 500));
        if ($length > 500) {
            echo "\n... (还有 " . ($length - 500) . " 个字符)";
        }
        echo "</pre>\n";
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>\n";
        echo "<h3>✗ 异常错误</h3>\n";
        echo "<p><strong>错误信息:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>文件:</strong> " . htmlspecialchars($e->getFile()) . "</p>\n";
        echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    
    // 提供在线测试链接
    echo "<div class='test-result info'>\n";
    echo "<h2>在线测试链接</h2>\n";
    echo "<p>您可以点击以下链接在浏览器中直接测试站点地图：</p>\n";
    echo "<ul>\n";
    echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=webdir&format=xml' target='_blank'>Webdir 站点地图</a></li>\n";
    echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=weblink&format=xml' target='_blank'>Weblink 站点地图</a></li>\n";
    echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=article&format=xml' target='_blank'>Article 站点地图</a></li>\n";
    echo "<li><a href='https://www.95dir.com/?mod=sitemap&type=all&format=xml' target='_blank'>All 站点地图</a></li>\n";
    echo "<li><a href='https://www.95dir.com/sitemap.xml' target='_blank'>主站点地图索引</a></li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    // 数据库状态检查
    echo "<div class='test-result info'>\n";
    echo "<h2>数据库状态</h2>\n";
    if (isset($DB) && is_object($DB)) {
        $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
        $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
        $category_count = $DB->get_count($DB->table('categories'), "1=1");
        $weblink_count = $DB->get_count($DB->table('weblinks'), "link_hide=0");
        
        echo "<p><strong>正常网站数量:</strong> $website_count</p>\n";
        echo "<p><strong>正常文章数量:</strong> $article_count</p>\n";
        echo "<p><strong>分类数量:</strong> $category_count</p>\n";
        echo "<p><strong>友情链接数量:</strong> $weblink_count</p>\n";
    } else {
        echo "<p style='color: red;'>数据库连接失败</p>\n";
    }
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>\n";
    echo "<h2>初始化错误</h2>\n";
    echo "<p><strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}

echo "</body>\n";
echo "</html>\n";
?>
