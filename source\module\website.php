<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-12 15:22:31
 * @LastEditTime : 2025-02-15 17:16:23
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\source\module\website.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
/** website list */
function get_websites($cate_id = 0, $top_num = 10, $is_pay = false, $is_best = false, $field = 'ctime', $order = 'desc') {
	global $DB;

	// 检查web_violation_status字段是否存在
	$table_name = $DB->table('websites');
	$check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_violation_status'";
	$check_result = $DB->query($check_sql);
	$has_violation_field = $DB->num_rows($check_result) > 0;

	$where = "w.web_status=3 AND c.cate_mod='webdir'";
	if ($has_violation_field) {
		$where .= " AND (w.web_violation_status IS NULL OR w.web_violation_status=0)";
	}
	if (!in_array($field, array('instat', 'outstat', 'views', 'ctime'))) $field = 'ctime';
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) $where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
	}
	if ($is_pay == true) $where .= " AND w.web_ispay=1";
	if ($is_best == true) $where .= " AND w.web_isbest=1";
	switch ($field) {
		case 'instat' :
			$sortby = "d.web_itime";
			break;
		case 'outstat' :
			$sortby = "d.web_otime";
			break;
		case 'views' :
			$sortby = "d.web_views";
			break;
		case 'ctime' :
			$sortby = "w.web_ctime";
			break;
		default :
			$sortby = "w.web_ctime";
			break;
	}
	$order = strtoupper($order);
	
	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, c.cate_id, c.cate_mod, c.cate_name, d.web_grank, d.web_brank, d.web_srank, d.web_arank, d.web_instat, d.web_outstat, d.web_views FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id LEFT JOIN ".$DB->table('webdata')." d ON w.web_id=d.web_id WHERE $where ORDER BY $sortby $order LIMIT $top_num";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_furl'] = format_url($row['web_url']);
		$row['web_pic'] = get_webthumb($row['web_pic']);
		$row['web_tags'] = get_format_tags(isset($row['web_tags']) ? $row['web_tags'] : '');

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['web_ctime'];
		$row['web_ctime'] = date('Y-m-d', $row['web_ctime']);
		$row['web_link'] = get_website_url($row['web_id'], false, $row['web_name'], $row['web_url']);
		$row['cate_link'] = get_category_url($row['cate_mod'], $row['cate_id']);

		// 判断是否为当天发表的网站
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);

	return $results;
}

/** 获取待审核网站列表 */
function get_pending_websites($top_num = 10) {
	global $DB;

	$where = "w.web_status=2";

	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, c.cate_id, c.cate_mod, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id WHERE $where ORDER BY w.web_ctime DESC LIMIT $top_num";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_furl'] = format_url($row['web_url']);
		$row['web_pic'] = get_webthumb($row['web_pic']);
		$row['web_tags'] = get_format_tags(isset($row['web_tags']) ? $row['web_tags'] : '');

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['web_ctime'];
		$row['web_ctime'] = date('Y-m-d', $row['web_ctime']);
		$row['web_link'] = '?mod=pending_detail&id='.$row['web_id']; // 待审核网站链接到详情页
		$row['cate_link'] = get_category_url($row['cate_mod'], $row['cate_id']);

		// 判断是否为当天发表的网站
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	return $results;
}

/** 获取黑名单网站列表 */
function get_blacklist_websites($top_num = 10) {
	global $DB;

	$where = "w.web_status=1";

	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, c.cate_id, c.cate_mod, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id WHERE $where ORDER BY w.web_ctime DESC LIMIT $top_num";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_furl'] = format_url($row['web_url']);
		$row['web_pic'] = get_webthumb($row['web_pic']);
		$row['web_tags'] = get_format_tags(isset($row['web_tags']) ? $row['web_tags'] : '');

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['web_ctime'];
		$row['web_ctime'] = date('Y-m-d', $row['web_ctime']);
		$row['web_link'] = '?mod=blacklist_detail&id='.$row['web_id']; // 黑名单网站链接到详情页
		$row['cate_link'] = get_category_url($row['cate_mod'], $row['cate_id']);

		// 判断是否为当天发表的网站
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	return $results;
}

/** 获取审核不通过网站列表 */
function get_rejected_websites($top_num = 10) {
	global $DB;

	$where = "w.web_status=4";

	// 检查web_reject_reason字段是否存在
	$table_name = $DB->table('websites');
	$check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_reject_reason'";
	$check_result = $DB->query($check_sql);
	$has_reject_reason_field = $DB->num_rows($check_result) > 0;

	// 根据字段是否存在构建不同的SQL
	if ($has_reject_reason_field) {
		$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, w.web_reject_reason, c.cate_id, c.cate_mod, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id WHERE $where ORDER BY w.web_ctime DESC LIMIT $top_num";
	} else {
		$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro, w.web_ai_intro, w.web_tags, w.web_ctime, c.cate_id, c.cate_mod, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id WHERE $where ORDER BY w.web_ctime DESC LIMIT $top_num";
	}

	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_furl'] = format_url($row['web_url']);
		$row['web_pic'] = get_webthumb($row['web_pic']);
		$row['web_tags'] = get_format_tags(isset($row['web_tags']) ? $row['web_tags'] : '');

		// 如果字段不存在，设置默认值
		if (!$has_reject_reason_field) {
			$row['web_reject_reason'] = '';
		}

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['web_ctime'];
		$row['web_ctime'] = date('Y-m-d', $row['web_ctime']);
		$row['web_link'] = '?mod=rejected_detail&id='.$row['web_id']; // 审核不通过网站链接到专门的详情页
		$row['cate_link'] = get_category_url($row['cate_mod'], $row['cate_id']);

		// 判断是否为当天发表的网站
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	return $results;
}

/** website list */
function get_website_list($where = 1, $field = 'ctime', $order = 'DESC', $stweb = 0, $pagesize = 0) {
	global $DB;
	
	if (!in_array($field, array('instat', 'outstat', 'views', 'ctime'))) $field = 'ctime';
	switch ($field) {
		case 'instat' :
			$sortby = "d.web_instat";
			break;
		case 'outstat' :
			$sortby = "d.web_outstat";
			break;
		case 'views' :
			$sortby = "d.web_views";
			break;
		case 'ctime' :
			$sortby = "w.web_ctime";
			break;
		default :
			$sortby = "w.web_ctime";
			break;
	}
	$order = strtoupper($order);
	$sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_pic, w.web_intro,w.web_ai_intro, w.web_ispay, w.web_istop, w.web_isbest, w.web_status, w.web_ctime, c.cate_name, d.web_ip, d.web_grank, d.web_brank, d.web_srank, d.web_arank, d.web_instat, d.web_outstat, d.web_views, d.web_utime FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id LEFT JOIN ".$DB->table('webdata')." d ON w.web_id=d.web_id WHERE $where ORDER BY w.web_istop DESC, $sortby $order LIMIT $stweb, $pagesize";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		switch ($row['web_status']) {
			case 1 :
				$status = '黑名单';
				break;
			case 2 :
				$status = '待审核';
				break;
			case 3 :
				$status = '已审核';
				break;
			case 4 :
				$status = '审核不通过';
				break;
			default :
				$status = '未知状态';
				break;
		}
		$row['web_furl'] = format_url($row['web_url']);
		$row['web_link'] = get_website_url($row['web_id'], false, $row['web_name'], $row['web_url']);
		$row['web_pic'] = get_webthumb($row['web_pic']);
		$row['web_status'] = $status;

		// 保存原始时间戳用于判断是否为当天
		$original_ctime = $row['web_ctime'];
		$original_utime = $row['web_utime'];
		$row['web_ctime'] = date('Y-m-d', $row['web_ctime']);
		$row['web_utime'] = date('Y-m-d', $original_utime);

		// PageRank处理
		$row['web_prank'] = $row['web_grank'];

		// 判断是否为当天发表的网站
		$row['is_today'] = (date('Y-m-d', $original_ctime) == date('Y-m-d'));

		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
		
	return $results;
}
	
/** one website */
function get_one_website($where = 1) {
	global $DB;

	// 检查web_reject_reason字段是否存在
	$table_name = $DB->table('websites');
	$check_sql = "SHOW COLUMNS FROM `{$table_name}` LIKE 'web_reject_reason'";
	$check_result = $DB->query($check_sql);
	$has_reject_reason_field = $DB->num_rows($check_result) > 0;

	// 根据字段是否存在构建不同的SQL
	if ($has_reject_reason_field) {
		$row = $DB->fetch_one("SELECT w.user_id, w.cate_id, w.web_id, w.web_name, w.web_url, w.web_tags, w.web_pic, w.web_intro,w.web_ai_intro, w.web_ispay, w.web_istop, w.web_isbest, w.web_status, w.web_ctime, w.web_reject_reason, d.web_ip, d.web_grank, d.web_brank, d.web_srank, d.web_arank, d.web_instat, d.web_outstat, d.web_views, d.web_utime FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table("webdata")." d ON w.web_id=d.web_id WHERE $where LIMIT 1");
	} else {
		$row = $DB->fetch_one("SELECT w.user_id, w.cate_id, w.web_id, w.web_name, w.web_url, w.web_tags, w.web_pic, w.web_intro,w.web_ai_intro, w.web_ispay, w.web_istop, w.web_isbest, w.web_status, w.web_ctime, d.web_ip, d.web_grank, d.web_brank, d.web_srank, d.web_arank, d.web_instat, d.web_outstat, d.web_views, d.web_utime FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table("webdata")." d ON w.web_id=d.web_id WHERE $where LIMIT 1");
	}

	// PageRank处理
	if ($row) {
		$row['web_prank'] = $row['web_grank'];

		// 如果字段不存在，设置默认值
		if (!$has_reject_reason_field) {
			$row['web_reject_reason'] = '';
		}
	}

	return $row;
}

/** prev website */
function get_prev_website($wid = 0) {
	global $DB;

	$row = $DB->fetch_one("SELECT web_id, web_name, web_url FROM ".$DB->table('websites')." WHERE web_status=3 AND web_id < $wid ORDER BY web_id DESC LIMIT 1");
	if (!empty($row)) {
		$row['web_link'] = get_website_url($row['web_id'], false, $row['web_name'], $row['web_url']);
		$row['web_furl'] = format_url($row['web_url']);
	}

	return $row;
}

/** next website */
function get_next_website($wid = 0) {
	global $DB;

	$row = $DB->fetch_one("SELECT web_id, web_name, web_url FROM ".$DB->table('websites')." WHERE web_status=3 AND web_id > $wid ORDER BY web_id ASC LIMIT 1");
	if (!empty($row)) {
		$row['web_link'] = get_website_url($row['web_id'], false, $row['web_name'], $row['web_url']);
		$row['web_furl'] = format_url($row['web_url']);
	}

	return $row;
}
	
/** rssfeed */
function get_website_rssfeed($cate_id = 0) {
	global $DB, $options;
		
	$where = "w.web_status=3 AND c.cate_mod='webdir'";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND w.cate_id=$cate_id";
		}
	}

	$sql = "SELECT w.web_id, w.cate_id, w.web_name, w.web_url, w.web_intro,w.web_ai_intro, w.web_ctime, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id";
	$sql .= " WHERE $where ORDER BY w.web_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_intro'] = htmlspecialchars(strip_tags($row['web_intro']));
		$row['web_ai_intro'] = htmlspecialchars(strip_tags($row['web_ai_intro']));
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
		
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<rss version=\"2.0\">\n";
	echo "<channel>\n";
	echo "<title>".$options['site_name']."</title>\n";
	echo "<link>".$options['site_url']."</link>\n";
	echo "<description>".$options['site_description']."</description>\n";
	echo "<language>zh-cn</language>\n";
	echo "<copyright><!--CDATA[".$options['site_copyright']."]--></copyright>\n";
	echo "<webmaster>".$options['site_name']."</webmaster>\n";
	echo "<generator>".$options['site_name']."</generator>\n";
	echo "<image>\n";
	echo "<title>".$options['site_name']."</title>\n";
	echo "<url>".$options['site_url']."logo.gif</url>\n";
	echo "<link>".$options['site_url']."</link>\n";
	echo "<description>".$options['site_description']."</description>\n";
	echo "</image>\n";
	
	foreach ($results as $row) {
		echo "<item>\n";
		echo "<link>".$row['web_link']."</link>\n";
		echo "<title>".$row['web_name']."</title>\n";
		echo "<author>".$options['site_name']."</author>\n";
		echo "<category>".$row['cate_name']."</category>\n";
		echo "<pubDate>".$row['web_ctime']."</pubDate>\n";
		echo "<guid>".$row['web_link']."</guid>\n";
		echo "<description>".$row['web_intro']."</description>\n";
		echo "</item>\n";
	}
	echo "</channel>\n";
	echo "</rss>";
	
	unset($options, $results);
}
	
/** sitemap */
function get_website_sitemap($cate_id = 0) {
	global $DB, $options;

	$where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND cate_id=$cate_id";
		}
	}

	$sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
	$sql .= " WHERE $where ORDER BY web_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 添加主要页面
	$main_pages = array(
		'webdir' => array('priority' => '0.9', 'changefreq' => 'daily'),
		'article' => array('priority' => '0.8', 'changefreq' => 'daily'),
		'weblink' => array('priority' => '0.7', 'changefreq' => 'weekly'),
		'top' => array('priority' => '0.6', 'changefreq' => 'daily'),
		'search' => array('priority' => '0.5', 'changefreq' => 'monthly')
	);

	foreach ($main_pages as $mod => $info) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=".$mod."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>".$info['changefreq']."</changefreq>\n";
		echo "<priority>".$info['priority']."</priority>\n";
		echo "</url>\n";
	}

	// 添加分类页面
	$categories = get_all_category();
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'webdir' && $cate['cate_postcount'] > 0) {
			$cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('webdir', $cate['cate_id']));
			echo "<url>\n";
			echo "<loc>".$cate_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.7</priority>\n";
			echo "</url>\n";
		}
		if ($cate['cate_mod'] == 'article' && $cate['cate_postcount'] > 0) {
			$cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('article', $cate['cate_id']));
			echo "<url>\n";
			echo "<loc>".$cate_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.6</priority>\n";
			echo "</url>\n";
		}
	}
	
	$now = time();
	foreach ($results as $row) {
		$prior = 0.5;
		
		if (datediff('h', $row['web_ctime']) < 24) {
			$freq = "hourly";
			$prior = 0.8;
		} elseif (datediff('d', $row['web_ctime']) < 7) {
			$freq = "daily";
			$prior = 0.7;
		} elseif (datediff('w', $row['web_ctime']) < 4) {
			$freq = "weekly";
		} elseif (datediff('m', $row['web_ctime']) < 12) {
			$freq = "monthly";
		} else {
			$freq = "yearly";
		}
		
		echo "<url>\n";
		echo "<loc>".$row['web_link']."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['web_ctime']))."</lastmod>\n";
		echo "<changefreq>".$freq."</changefreq>\n";
		if ($prior != 0.5) {
			echo "<priority>".$prior."</priority>\n";
		}
		echo "</url>\n";
	}

	// 添加文章页面
	$sql = "SELECT art_id, art_ctime FROM ".$DB->table('articles');
	$sql .= " WHERE art_status=3 ORDER BY art_id DESC LIMIT 50";
	$query = $DB->query($sql);
	while ($row = $DB->fetch_array($query)) {
		$row['art_ctime'] = date('Y-m-d H:i:s', $row['art_ctime']);
		$art_link = str_replace('&', '&amp;', get_article_url($row['art_id'], true));

		// 计算更新频率和优先级
		if (datediff('d', $row['art_ctime']) < 7) {
			$freq = "weekly";
			$prior = 0.6;
		} elseif (datediff('m', $row['art_ctime']) < 3) {
			$freq = "monthly";
			$prior = 0.5;
		} else {
			$freq = "yearly";
			$prior = 0.4;
		}

		echo "<url>\n";
		echo "<loc>".$art_link."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['art_ctime']))."</lastmod>\n";
		echo "<changefreq>".$freq."</changefreq>\n";
		echo "<priority>".$prior."</priority>\n";
		echo "</url>\n";
	}
	$DB->free_result($query);

	// 添加webdir分类页面的分页
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'webdir' && $cate['cate_postcount'] > 10) {
			$pages = ceil($cate['cate_postcount'] / 10); // 每页10个
			for ($page = 2; $page <= min($pages, 10); $page++) { // 最多包含10页
				$page_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('webdir', $cate['cate_id']) . '-' . $page . '.html');
				echo "<url>\n";
				echo "<loc>".$page_url."</loc>\n";
				echo "<lastmod>".date('c')."</lastmod>\n";
				echo "<changefreq>weekly</changefreq>\n";
				echo "<priority>0.5</priority>\n";
				echo "</url>\n";
			}
		}
		if ($cate['cate_mod'] == 'article' && $cate['cate_postcount'] > 10) {
			$pages = ceil($cate['cate_postcount'] / 10); // 每页10个
			for ($page = 2; $page <= min($pages, 10); $page++) { // 最多包含10页
				$page_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('article', $cate['cate_id']) . '-' . $page . '.html');
				echo "<url>\n";
				echo "<loc>".$page_url."</loc>\n";
				echo "<lastmod>".date('c')."</lastmod>\n";
				echo "<changefreq>weekly</changefreq>\n";
				echo "<priority>0.4</priority>\n";
				echo "</url>\n";
			}
		}
	}

	// 添加友情链接详情页
	$sql = "SELECT link_id, link_time FROM ".$DB->table('weblinks');
	$sql .= " WHERE link_hide=0 ORDER BY link_id DESC LIMIT 30";
	$query = $DB->query($sql);
	while ($row = $DB->fetch_array($query)) {
		$link_url = str_replace('&', '&amp;', get_weblink_url($row['link_id'], true));
		echo "<url>\n";
		echo "<loc>".$link_url."</loc>\n";
		echo "<lastmod>".date('c', $row['link_time'])."</lastmod>\n";
		echo "<changefreq>monthly</changefreq>\n";
		echo "<priority>0.3</priority>\n";
		echo "</url>\n";
	}
	$DB->free_result($query);

	// 添加排行榜页面
	$top_types = array('views', 'grank', 'brank', 'srank', 'arank');
	foreach ($top_types as $type) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=top&type=".$type."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>daily</changefreq>\n";
		echo "<priority>0.4</priority>\n";
		echo "</url>\n";
	}

	// 添加最近更新页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=update</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>0.5</priority>\n";
	echo "</url>\n";

	// 添加数据归档页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=archives</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>monthly</changefreq>\n";
	echo "<priority>0.4</priority>\n";
	echo "</url>\n";

	// 添加热门网站标签页面
	$sql = "SELECT web_tags FROM ".$DB->table('websites')." WHERE web_status=3 AND web_tags!='' ORDER BY web_id DESC LIMIT 200";
	$query = $DB->query($sql);
	$all_tags = array();
	while ($row = $DB->fetch_array($query)) {
		$tags = explode(',', $row['web_tags']);
		foreach ($tags as $tag) {
			$tag = trim($tag);
			if (!empty($tag) && strlen($tag) > 1) {
				if (isset($all_tags[$tag])) {
					$all_tags[$tag]++;
				} else {
					$all_tags[$tag] = 1;
				}
			}
		}
	}
	$DB->free_result($query);

	// 按使用频率排序，取前30个热门标签
	arsort($all_tags);
	$hot_tags = array_slice($all_tags, 0, 30, true);

	foreach ($hot_tags as $tag => $count) {
		if ($count >= 2) { // 至少被使用2次的标签才加入sitemap
			$tag_url = str_replace('&', '&amp;', $options['site_url'] . '?mod=search&type=tags&query=' . urlencode($tag));
			echo "<url>\n";
			echo "<loc>".$tag_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.4</priority>\n";
			echo "</url>\n";
		}
	}

	// 添加热门文章标签页面
	$sql = "SELECT art_tags FROM ".$DB->table('articles')." WHERE art_status=3 AND art_tags!='' ORDER BY art_id DESC LIMIT 100";
	$query = $DB->query($sql);
	$all_art_tags = array();
	while ($row = $DB->fetch_array($query)) {
		$tags = explode(',', $row['art_tags']);
		foreach ($tags as $tag) {
			$tag = trim($tag);
			if (!empty($tag) && strlen($tag) > 1) {
				if (isset($all_art_tags[$tag])) {
					$all_art_tags[$tag]++;
				} else {
					$all_art_tags[$tag] = 1;
				}
			}
		}
	}
	$DB->free_result($query);

	// 按使用频率排序，取前20个热门文章标签
	arsort($all_art_tags);
	$hot_art_tags = array_slice($all_art_tags, 0, 20, true);

	foreach ($hot_art_tags as $tag => $count) {
		if ($count >= 2) { // 至少被使用2次的标签才加入sitemap
			$tag_url = str_replace('&', '&amp;', $options['site_url'] . '?mod=search&type=article&query=' . urlencode($tag));
			echo "<url>\n";
			echo "<loc>".$tag_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.3</priority>\n";
			echo "</url>\n";
		}
	}

	// 添加更多功能页面
	$additional_pages = array(
		'category' => array('priority' => '0.7', 'changefreq' => 'weekly'),
		'feedback' => array('priority' => '0.4', 'changefreq' => 'monthly')
	);

	foreach ($additional_pages as $mod => $info) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=".$mod."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>".$info['changefreq']."</changefreq>\n";
		echo "<priority>".$info['priority']."</priority>\n";
		echo "</url>\n";
	}

	// 添加最近更新页面（按天数）
	$update_days = array(1, 3, 7, 15, 30);
	foreach ($update_days as $days) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=update&amp;days=".$days."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>daily</changefreq>\n";
		echo "<priority>0.5</priority>\n";
		echo "</url>\n";
	}

	// 添加数据归档页面（按年月）
	$sql = "SELECT DISTINCT DATE_FORMAT(FROM_UNIXTIME(web_ctime), '%Y%m') as date_key FROM ".$DB->table('websites')." WHERE web_status=3 ORDER BY date_key DESC LIMIT 24";
	$query = $DB->query($sql);
	while ($row = $DB->fetch_array($query)) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=archives&amp;date=".$row['date_key']."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>monthly</changefreq>\n";
		echo "<priority>0.4</priority>\n";
		echo "</url>\n";
	}
	$DB->free_result($query);

	// 添加自定义页面（diypage）
	$sql = "SELECT page_id FROM ".$DB->table('diypage')." WHERE page_status=1 ORDER BY page_id ASC";
	$query = $DB->query($sql);
	while ($row = $DB->fetch_array($query)) {
		echo "<url>\n";
		echo "<loc>".$options['site_url']."?mod=diypage&amp;pid=".$row['page_id']."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>monthly</changefreq>\n";
		echo "<priority>0.5</priority>\n";
		echo "</url>\n";
	}
	$DB->free_result($query);

	// 添加RSS订阅页面
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'webdir' && $cate['cate_postcount'] > 5) {
			echo "<url>\n";
			echo "<loc>".$options['site_url']."?mod=rssfeed&amp;cid=".$cate['cate_id']."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>daily</changefreq>\n";
			echo "<priority>0.3</priority>\n";
			echo "</url>\n";
		}
		if ($cate['cate_mod'] == 'article' && $cate['cate_postcount'] > 5) {
			echo "<url>\n";
			echo "<loc>".$options['site_url']."?mod=rssfeed&amp;type=article&amp;cid=".$cate['cate_id']."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>daily</changefreq>\n";
			echo "<priority>0.3</priority>\n";
			echo "</url>\n";
		}
	}

	echo "</urlset>";

	unset($options, $results);
}
/** sodir api */
function get_website_api($cate_id = 0, $stweb = 0, $pagesize = 0) {
	global $DB, $options;

	$where = "w.web_status=3 AND c.cate_mod='webdir' AND (w.web_violation_status IS NULL OR w.web_violation_status=0)";
	$cate = get_one_category($cate_id);
	if (!empty($cate)) {
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
		} else {
			$where .= " AND w.cate_id=$cate_id";
		}
	}

	$sql = "SELECT w.web_id, w.cate_id, w.web_name, w.web_url, w.web_tags, w.web_intro,w.web_ai_intro, w.web_ctime, c.cate_name FROM ".$DB->table('websites')." w LEFT JOIN ".$DB->table('categories')." c ON w.cate_id=c.cate_id";
	$sql .= " WHERE $where ORDER BY w.web_id DESC LIMIT $stweb, $pagesize";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['web_intro'] = htmlspecialchars(strip_tags($row['web_intro']));
		$row['web_ai_intro'] = htmlspecialchars(strip_tags($row['web_ai_intro']));
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	$total = $DB->get_count($DB->table('websites').' w', $where);
	
	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sodir.org/sitemap/\">\n";
	echo "<total>".$total."</total>";
	
	foreach ($results as $row) {
		echo "<url>\n";
		echo "<name>".$row['web_name']."</name>\n";
		echo "<link>".$row['web_link']."</link>\n";
		echo "<tags>".$row['web_tags']."</tags>\n";
		echo "<desc>".$row['web_intro']."</desc>\n";
		echo "<cate>".$row['cate_name']."</cate>\n";
		echo "<time>".$row['web_ctime']."</time>\n";		
		echo "</url>\n";
	}
	echo "</urlset>\n";
	
	unset($options, $results);
}

/** archives */
function get_archives() {
	global $DB;
	
	$archives = array();
	if (load_cache('archives')) {
		$archives = load_cache('archives');
	} else {
		$time = array();
		$sql = "SELECT web_ctime FROM ".$DB->table('websites')." WHERE web_status=3 ORDER BY web_ctime DESC";
		$query = $DB->query($sql);
		while ($row = $DB->fetch_array($query)) {
			$time[] = date('Y-m', $row['web_ctime']);
		}
		unset($row);
		$DB->free_result($query);
		
		$count = array_count_values($time);
		unset($time);
		
		foreach ($count as $key => $val) {
			list($year, $month) = explode('-', $key);
			$archives[$year][$month] = $val;
		}
	}
		
	$newarr = array();
	foreach ($archives as $year => $arr) {
		foreach ($arr as $month => $count) {
			$newarr[$year][$month]['site_count'] = $count;
			$newarr[$year][$month]['arc_link'] = get_archives_url($year.$month);
		}
	}
	unset($archives);
	
	return $newarr;
}

/** hot tags */
function get_hot_tags($top_num = 30) {
	global $DB;

	$hot_tags = array();
	$cache_key = 'hot_tags_' . $top_num;
	if (load_cache($cache_key)) {
		$hot_tags = load_cache($cache_key);
	} else {
		$sql = "SELECT web_tags FROM ".$DB->table('websites')." WHERE web_status=3 AND web_tags!='' ORDER BY web_id DESC LIMIT 200"; 
		$query = $DB->query($sql);
		$all_tags = array();
		while ($row = $DB->fetch_array($query)) {
			$tags = explode(',', $row['web_tags']);
			foreach ($tags as $tag) {
				$tag = trim($tag);
				if (!empty($tag) && strlen($tag) > 1) {
					if (isset($all_tags[$tag])) {
						$all_tags[$tag]++;
					} else {
						$all_tags[$tag] = 1;
					}
				}
			}
		}
		$DB->free_result($query);

		// 按使用频率排序，取前N个热门标签
		arsort($all_tags);
		$hot_tags_temp = array_slice($all_tags, 0, $top_num, true);

		// 格式化标签数据
		foreach ($hot_tags_temp as $tag => $count) {
			if ($count >= 2) { // 至少被使用2次的标签才显示
				$hot_tags[] = array(
					'tag_name' => $tag,
					'tag_count' => $count,
					'tag_link' => get_search_url('tags', $tag)
				);
			}
		}

		// 缓存热门标签数据
		$cache_data = "\$static_data = " . var_export($hot_tags, true) . ";";
		write_cache($cache_key, $cache_data);
	}

	return $hot_tags;
}

/** category sitemap */
function get_category_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 分类浏览页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=category</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>weekly</changefreq>\n";
	echo "<priority>0.8</priority>\n";
	echo "</url>\n";

	// 添加所有分类页面
	$categories = get_all_category();
	foreach ($categories as $cate) {
		if ($cate['cate_postcount'] > 0) {
			$cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url($cate['cate_mod'], $cate['cate_id']));
			echo "<url>\n";
			echo "<loc>".$cate_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			if ($cate['cate_mod'] == 'webdir') {
				echo "<priority>0.7</priority>\n";
			} else {
				echo "<priority>0.6</priority>\n";
			}
			echo "</url>\n";
		}
	}

	// 添加分类页面的分页
	foreach ($categories as $cate) {
		if ($cate['cate_postcount'] > 10) {
			$pages = ceil($cate['cate_postcount'] / 10); // 每页10个
			for ($page = 2; $page <= min($pages, 10); $page++) { // 最多包含10页
				$page_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url($cate['cate_mod'], $cate['cate_id'], $page));
				echo "<url>\n";
				echo "<loc>".$page_url."</loc>\n";
				echo "<lastmod>".date('c')."</lastmod>\n";
				echo "<changefreq>weekly</changefreq>\n";
				echo "<priority>0.5</priority>\n";
				echo "</url>\n";
			}
		}
	}

	echo "</urlset>\n";

	unset($options, $categories);
}

/** VIP sitemap */
function get_vip_sitemap($cate_id = 0) {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// VIP列表页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=vip_list</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>0.8</priority>\n";
	echo "</url>\n";

	// VIP网站详情页面
	$where = "w.web_status=3 AND w.web_ispay=1";
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) {
			if ($cate['cate_childcount'] > 0) {
				$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
			} else {
				$where .= " AND w.cate_id=$cate_id";
			}
		}
	}

	$sql = "SELECT w.web_id, w.web_name, w.web_ctime FROM ".$DB->table('websites')." w WHERE $where ORDER BY w.web_istop DESC, w.web_id DESC LIMIT 100";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['vip_link'] = str_replace('&', '&amp;', $options['site_url'].'?mod=vip_detail&id='.$row['web_id']);
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);

	foreach ($results as $row) {
		echo "<url>\n";
		echo "<loc>".$row['vip_link']."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['web_ctime']))."</lastmod>\n";
		echo "<changefreq>weekly</changefreq>\n";
		echo "<priority>0.7</priority>\n";
		echo "</url>\n";
	}

	// VIP分类页面
	$categories = get_all_category();
	foreach ($categories as $cate) {
		if ($cate['cate_mod'] == 'webdir') {
			$vip_cate_url = str_replace('&', '&amp;', $options['site_url'].'?mod=vip_list&cid='.$cate['cate_id']);
			echo "<url>\n";
			echo "<loc>".$vip_cate_url."</loc>\n";
			echo "<lastmod>".date('c')."</lastmod>\n";
			echo "<changefreq>weekly</changefreq>\n";
			echo "<priority>0.6</priority>\n";
			echo "</url>\n";
		}
	}

	echo "</urlset>\n";

	unset($options, $results, $categories);
}

/** Tags sitemap */
function get_tags_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 获取热门标签
	$hot_tags = get_hot_tags(50);
	foreach ($hot_tags as $tag) {
		$tag_url = str_replace('&', '&amp;', $tag['tag_link']);
		echo "<url>\n";
		echo "<loc>".$tag_url."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>weekly</changefreq>\n";
		if ($tag['tag_count'] >= 10) {
			echo "<priority>0.7</priority>\n";
		} elseif ($tag['tag_count'] >= 5) {
			echo "<priority>0.6</priority>\n";
		} else {
			echo "<priority>0.5</priority>\n";
		}
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $hot_tags);
}

/** Pending sitemap */
function get_pending_sitemap($cate_id = 0) {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 待审核列表页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=pending</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>0.7</priority>\n";
	echo "</url>\n";

	// 待审核网站详情页面
	$where = "w.web_status=2"; // 待审核状态
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) {
			if ($cate['cate_childcount'] > 0) {
				$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
			} else {
				$where .= " AND w.cate_id=$cate_id";
			}
		}
	}

	$sql = "SELECT w.web_id, w.web_name, w.web_ctime FROM ".$DB->table('websites')." w WHERE $where ORDER BY w.web_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['pending_link'] = str_replace('&', '&amp;', $options['site_url'].'?mod=pending_detail&id='.$row['web_id']);
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);

	foreach ($results as $row) {
		echo "<url>\n";
		echo "<loc>".$row['pending_link']."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['web_ctime']))."</lastmod>\n";
		echo "<changefreq>daily</changefreq>\n";
		echo "<priority>0.5</priority>\n";
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $results);
}

/** Rejected sitemap */
function get_rejected_sitemap($cate_id = 0) {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 审核不通过列表页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=rejected</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>weekly</changefreq>\n";
	echo "<priority>0.6</priority>\n";
	echo "</url>\n";

	// 审核不通过网站详情页面
	$where = "w.web_status=0"; // 审核不通过状态
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) {
			if ($cate['cate_childcount'] > 0) {
				$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
			} else {
				$where .= " AND w.cate_id=$cate_id";
			}
		}
	}

	$sql = "SELECT w.web_id, w.web_name, w.web_ctime FROM ".$DB->table('websites')." w WHERE $where ORDER BY w.web_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['rejected_link'] = str_replace('&', '&amp;', $options['site_url'].'?mod=rejected_detail&id='.$row['web_id']);
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);

	foreach ($results as $row) {
		echo "<url>\n";
		echo "<loc>".$row['rejected_link']."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['web_ctime']))."</lastmod>\n";
		echo "<changefreq>monthly</changefreq>\n";
		echo "<priority>0.4</priority>\n";
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $results);
}

/** Blacklist sitemap */
function get_blacklist_sitemap($cate_id = 0) {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 黑名单列表页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=blacklist</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>weekly</changefreq>\n";
	echo "<priority>0.6</priority>\n";
	echo "</url>\n";

	// 黑名单网站详情页面
	$where = "w.web_violation_status=1"; // 黑名单状态
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!empty($cate)) {
			if ($cate['cate_childcount'] > 0) {
				$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
			} else {
				$where .= " AND w.cate_id=$cate_id";
			}
		}
	}

	$sql = "SELECT w.web_id, w.web_name, w.web_ctime FROM ".$DB->table('websites')." w WHERE $where ORDER BY w.web_id DESC LIMIT 50";
	$query = $DB->query($sql);
	$results = array();
	while ($row = $DB->fetch_array($query)) {
		$row['blacklist_link'] = str_replace('&', '&amp;', $options['site_url'].'?mod=blacklist_detail&id='.$row['web_id']);
		$row['web_ctime'] = date('Y-m-d H:i:s', $row['web_ctime']);
		$results[] = $row;
	}
	unset($row);
	$DB->free_result($query);

	foreach ($results as $row) {
		echo "<url>\n";
		echo "<loc>".$row['blacklist_link']."</loc>\n";
		echo "<lastmod>".date('c', strtotime($row['web_ctime']))."</lastmod>\n";
		echo "<changefreq>monthly</changefreq>\n";
		echo "<priority>0.3</priority>\n";
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $results);
}

/** Search sitemap */
function get_search_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 搜索页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."?mod=search</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>weekly</changefreq>\n";
	echo "<priority>0.7</priority>\n";
	echo "</url>\n";

	echo "</urlset>\n";

	unset($options);
}

/** Member sitemap */
function get_member_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 会员中心页面
	echo "<url>\n";
	echo "<loc>".$options['site_url']."member/</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>weekly</changefreq>\n";
	echo "<priority>0.6</priority>\n";
	echo "</url>\n";

	echo "</urlset>\n";

	unset($options);
}

/** Other pages sitemap */
function get_other_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 其他页面
	$other_pages = array(
		'update' => array('url' => '?mod=update', 'priority' => '0.7', 'changefreq' => 'daily'),
		'archives' => array('url' => '?mod=archives', 'priority' => '0.6', 'changefreq' => 'weekly'),
		'top' => array('url' => '?mod=top', 'priority' => '0.7', 'changefreq' => 'daily'),
		'feedback' => array('url' => '?mod=feedback', 'priority' => '0.5', 'changefreq' => 'monthly'),
		'addurl' => array('url' => '?mod=addurl', 'priority' => '0.8', 'changefreq' => 'weekly'),
		'quicksubmit' => array('url' => '?mod=quicksubmit', 'priority' => '0.7', 'changefreq' => 'weekly'),
		'datastats' => array('url' => '?mod=datastats', 'priority' => '0.6', 'changefreq' => 'weekly'),
	);

	foreach ($other_pages as $page => $info) {
		echo "<url>\n";
		echo "<loc>".$options['site_url'].$info['url']."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>".$info['changefreq']."</changefreq>\n";
		echo "<priority>".$info['priority']."</priority>\n";
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $other_pages);
}

/** All sitemap */
function get_all_sitemap() {
	global $DB, $options;

	header("Content-Type: application/xml;");
	echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
	echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";

	// 首页
	echo "<url>\n";
	echo "<loc>".$options['site_url']."</loc>\n";
	echo "<lastmod>".date('c')."</lastmod>\n";
	echo "<changefreq>daily</changefreq>\n";
	echo "<priority>1.0</priority>\n";
	echo "</url>\n";

	// 主要页面
	$main_pages = array(
		'webdir' => array('url' => '?mod=webdir', 'priority' => '0.9', 'changefreq' => 'daily'),
		'article' => array('url' => '?mod=article', 'priority' => '0.8', 'changefreq' => 'daily'),
		'weblink' => array('url' => '?mod=weblink', 'priority' => '0.7', 'changefreq' => 'daily'),
		'category' => array('url' => '?mod=category', 'priority' => '0.8', 'changefreq' => 'weekly'),
		'vip_list' => array('url' => '?mod=vip_list', 'priority' => '0.8', 'changefreq' => 'daily'),
		'search' => array('url' => '?mod=search', 'priority' => '0.7', 'changefreq' => 'weekly'),
		'update' => array('url' => '?mod=update', 'priority' => '0.7', 'changefreq' => 'daily'),
		'top' => array('url' => '?mod=top', 'priority' => '0.7', 'changefreq' => 'daily'),
	);

	foreach ($main_pages as $page => $info) {
		echo "<url>\n";
		echo "<loc>".$options['site_url'].$info['url']."</loc>\n";
		echo "<lastmod>".date('c')."</lastmod>\n";
		echo "<changefreq>".$info['changefreq']."</changefreq>\n";
		echo "<priority>".$info['priority']."</priority>\n";
		echo "</url>\n";
	}

	echo "</urlset>\n";

	unset($options, $main_pages);
}

?>