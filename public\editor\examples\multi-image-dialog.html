<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>MultiImageDialog Examples</title>
		<link rel="stylesheet" href="../themes/default/default.css" />
		<script src="../kindeditor.js"></script>
		<script src="../lang/zh_CN.js"></script>
		<script>
			KindEditor.ready(function(K) {
				var editor = K.editor({
					allowFileManager : true
				});
				K('#J_selectImage').click(function() {
					editor.loadPlugin('multiimage', function() {
						editor.plugin.multiImageDialog({
							clickFn : function(urlList) {
								var div = K('#J_imageView');
								div.html('');
								K.each(urlList, function(i, url) {
									div.append('<img src="' + url + '">');
								});
								editor.hideDialog();
							}
						});
					});
				});
			});
		</script>
	</head>
	<body>
		<input type="button" id="J_selectImage" value="批量上传" />
		<div id="J_imageView"></div>
	</body>
</html>
