<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>KindEditor Menu Test</title>
		<link rel="stylesheet" href="../lib/qunit/qunit.css" />
		<script src="../lib/firebug-lite/build/firebug-lite.js#startOpened"></script>
		<script src="../lib/qunit/qunit.js"></script>
		<script src="../lib/jquery.min.js"></script>
		<link rel="stylesheet" href="../themes/default/default.css" />
		<!-- include src files -->
		<script src="../src/core.js"></script>
		<script src="../src/config.js"></script>
		<script src="../src/event.js"></script>
		<script src="../src/html.js"></script>
		<script src="../src/selector.js"></script>
		<script src="../src/node.js"></script>
		<script src="../src/range.js"></script>
		<script src="../src/cmd.js"></script>
		<script src="../src/widget.js"></script>
		<script src="../src/menu.js"></script>
		<script src="../src/colorpicker.js"></script>
		<script src="../src/main.js"></script>
		<script src="../lang/zh-CN.js"></script>
	</head>
	<body>
		<h1>KindEditor Menu Test</h1>
		<input type="button" id="menu" value="Menu" />
		<input type="button" id="contextmenu" value="Contextmenu" />
		<input type="button" id="colorpicker" value="ColorPicker" />
		<script src="menu.js"></script>
	</body>
</html>
