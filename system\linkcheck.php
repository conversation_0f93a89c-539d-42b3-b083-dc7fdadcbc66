<?php
require('common.php');

// 检查管理员权限
if (!$myself['user_id']) {
    exit('Access Denied');
}

$action = trim($_GET['action']);
$fileurl = 'linkcheck.php';
$tempfile = 'linkcheck.html';
$pagetitle = '友情链接检测';

// 检查配置状态
$linkcheck_enabled = isset($options['is_enabled_linkcheck']) && $options['is_enabled_linkcheck'] == 'yes';
$has_check_config = !empty($options['check_link_url']) && !empty($options['check_link_name']);
$config_status = array(
    'enabled' => $linkcheck_enabled,
    'configured' => $has_check_config,
    'ready' => $linkcheck_enabled && $has_check_config
);

/** 单个网站链接检测 */
if ($action == 'check_single') {
    $web_id = intval($_GET['web_id']);
    $url = trim($_GET['url']);
    
    if (empty($url)) {
        echo json_encode(array('success' => false, 'message' => '请输入网站URL'));
        exit;
    }
    
    $result = check_website_link($url);
    echo json_encode($result);
    exit;
}

/** 批量检测 */
if ($action == 'batch_check') {
    $limit = intval($_POST['limit']) ?: 10;
    $status = intval($_POST['status']) ?: 3; // 默认检测已审核的网站
    
    $table = $DB->table('websites');
    $webdata_table = $DB->table('webdata');
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_islink, COALESCE(d.web_utime, 0) as web_utime
            FROM $table w
            LEFT JOIN $webdata_table d ON w.web_id = d.web_id
            WHERE w.web_status=$status AND w.web_ispay=0
            ORDER BY w.web_id DESC
            LIMIT $limit";
    $query = $DB->query($sql);
    
    $results = array();
    $checked_count = 0;
    $updated_count = 0;
    
    while ($web = $DB->fetch_array($query)) {
        $checked_count++;
        $result = check_website_link($web['web_url']);
        
        $old_status = $web['web_islink'];
        $new_status = $result['has_link'] ? 0 : 1;
        
        if ($old_status != $new_status) {
            $DB->query("UPDATE $table SET web_islink=$new_status WHERE web_id=".$web['web_id']);
            $updated_count++;
        }
        
        $results[] = array(
            'web_id' => $web['web_id'],
            'web_name' => $web['web_name'],
            'web_url' => $web['web_url'],
            'old_status' => $old_status ? '未链接' : '已链接',
            'new_status' => $new_status ? '未链接' : '已链接',
            'changed' => $old_status != $new_status,
            'details' => $result['details']
        );
        
        // 避免请求过于频繁
        usleep(500000); // 0.5秒延迟
    }
    
    echo json_encode(array(
        'success' => true,
        'checked_count' => $checked_count,
        'updated_count' => $updated_count,
        'results' => $results
    ));
    exit;
}

/** 检测配置更新 */
if ($action == 'update_config') {
    $check_link_name = trim($_POST['check_link_name']);
    $check_link_url = trim($_POST['check_link_url']);
    
    if (empty($check_link_name) || empty($check_link_url)) {
        msgbox('请填写完整的检测配置！');
    }
    
    $DB->query("UPDATE ".$DB->table('options')." SET option_value='$check_link_name' WHERE option_name='check_link_name'");
    $DB->query("UPDATE ".$DB->table('options')." SET option_value='$check_link_url' WHERE option_name='check_link_url'");
    
    // 更新缓存
    update_cache('options');
    
    msgbox('友情链接检测配置更新成功！', $fileurl);
}

// 友情链接检测函数
function check_website_link($url) {
    global $options;

    // 格式化URL
    if (substr($url, 0, 7) != 'http://' && substr($url, 0, 8) != 'https://') {
        $url = 'https://' . $url;
    }

    // 直接调用智能检测函数（避免引入getdata.php的副作用）
    $result = smart_check_website_link_direct($url, $options);
    $result['success'] = true;

    return $result;
}

/**
 * 直接的智能友情链接检测函数（复制自module/getdata.php）
 */
function smart_check_website_link_direct($url, $options) {
    $result = array(
        'has_link' => false,
        'details' => '',
        'patterns_found' => array()
    );

    try {
        // 获取网站内容
        $content = get_url_content($url);

        if (empty($content)) {
            $result['details'] = '无法获取网站内容';
            return $result;
        }

        $check_domain = $options['check_link_url']; // 95dir.com
        $check_name = $options['check_link_name'];   // 95分类目录

        // 修复后的检测模式 - 更严格的友情链接检测，与 module/getdata.php 保持一致
        $patterns = array(
            'exact_match' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>.*?'.preg_quote($check_name, '/').'.*?<\/a>/i',
                'weight' => 100,
                'desc' => '精确匹配（域名+名称）'
            ),
            'domain_with_text' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>[^<]*'.preg_quote($check_name, '/').'[^<]*<\/a>/i',
                'weight' => 95,
                'desc' => '域名+文本匹配'
            ),
            'friendlink_section_domain' => array(
                'pattern' => '/(?:友情链接|友链|合作伙伴|推荐网站|相关链接|links?).{0,500}<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>/i',
                'weight' => 85,
                'desc' => '友情链接区域的域名'
            ),
            'domain_with_site_text' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>[^<]*(?:网站|站点|目录|导航|分类)[^<]*<\/a>/i',
                'weight' => 80,
                'desc' => '域名+网站相关文本'
            ),
            'domain_only_strict' => array(
                'pattern' => '/<a[^>]*href=["\']?https?:\/\/(www\.)?'.preg_quote($check_domain, '/').'[\/]?["\']?[^>]*>(?!.*(?:广告|AD|推广|赞助))[^<]{1,30}<\/a>/i',
                'weight' => 70,
                'desc' => '严格域名匹配（排除广告）'
            ),
            'text_with_domain_verification' => array(
                'pattern' => '/<a[^>]*href=["\'][^"\']*'.preg_quote($check_domain, '/').'[^"\']*["\'][^>]*>.*?'.preg_quote($check_name, '/').'.*?<\/a>/i',
                'weight' => 90,
                'desc' => '文本+域名验证'
            ),
            'text_with_domain_hint' => array(
                'pattern' => '/<a[^>]*href=["\'][^"\']*["\'][^>]*>.*?'.preg_quote($check_name, '/').'.*?<\/a>/i',
                'weight' => 70,
                'desc' => '文本匹配'
            )
        );

        $max_weight = 0;
        $found_patterns = array();

        foreach ($patterns as $key => $pattern_info) {
            if (preg_match($pattern_info['pattern'], $content, $matches)) {
                $found_patterns[] = $pattern_info['desc'];
                if ($pattern_info['weight'] > $max_weight) {
                    $max_weight = $pattern_info['weight'];
                }
            }
        }

        // 提高判断阈值到85，减少误判，与 module/getdata.php 保持一致
        if ($max_weight >= 85) {
            $result['has_link'] = true;
            $result['details'] = '找到友情链接：' . implode('、', $found_patterns) . "（权重：$max_weight）";
        } else if ($max_weight >= 70) {
            $result['has_link'] = false;
            $result['details'] = '可能的链接：' . implode('、', $found_patterns) . "（权重不足：$max_weight < 85）";
        } else {
            $result['has_link'] = false;
            $result['details'] = '未找到友情链接';
        }

        $result['patterns_found'] = $found_patterns;

    } catch (Exception $e) {
        $result['details'] = '检测出错：' . $e->getMessage();
    }

    return $result;
}

// 获取统计数据
$stats = array();
$table = $DB->table('websites');

// 总网站数
$stats['total'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3");

// 已链接数
$stats['linked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_islink=0");

// 未链接数
$stats['unlinked'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_islink=1");

// 付费网站数（不检测链接）
$stats['paid'] = $DB->fetch_one("SELECT COUNT(*) FROM $table WHERE web_status=3 AND web_ispay=1");

// 最近未链接的网站
$recent_unlinked = $DB->fetch_all("SELECT web_id, web_name, web_url FROM $table WHERE web_status=3 AND web_islink=1 ORDER BY web_id DESC LIMIT 10");

$smarty->assign('pagetitle', $pagetitle);
$smarty->assign('fileurl', $fileurl);
$smarty->assign('stats', $stats);
$smarty->assign('recent_unlinked', $recent_unlinked);
$smarty->assign('check_link_name', $options['check_link_name']);
$smarty->assign('check_link_url', $options['check_link_url']);
$smarty->assign('config_status', $config_status);
$smarty->assign('data_update_cycle', $options['data_update_cycle']);

smarty_output($tempfile);
?>
