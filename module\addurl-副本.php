<?php
if (!defined('ROOT_PATH')) exit('Access Denied');

$pagename = '提交网站';
$tempfile = 'addurl.html';
$pageurl = '?mod=addurl';

// 处理表单提交
if ($_POST['act'] == 'submit') {
	$cate_id = intval($_POST['cate_id']);
	$web_name = trim($_POST['web_name']);
	$web_url = trim($_POST['web_url']);
	$web_tags = strtolower(addslashes(trim($_POST['web_tags'])));
	$web_intro = addslashes(trim($_POST['web_intro']));
	$web_owner = trim($_POST['web_owner']);
	$web_email = trim($_POST['web_email']);
	$check_code = strtolower(trim($_POST['check_code']));
	
	// 验证码检查
	if (empty($check_code) || $check_code != $_SESSION['code']) {
		echo "<script>alert('验证码错误！'); history.back();</script>";
		exit;
	}
	
	// 数据验证
	if ($cate_id <= 0) {
		echo "<script>alert('请选择网站所属分类！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_name)) {
		echo "<script>alert('请输入网站名称！'); history.back();</script>";
		exit;
	}
	
	// 检查网站名称长度（中文算2个字符）
	$name_length = 0;
	for ($i = 0; $i < mb_strlen($web_name, 'UTF-8'); $i++) {
		$char = mb_substr($web_name, $i, 1, 'UTF-8');
		if (ord($char) > 127) {
			$name_length += 2; // 中文字符算2个字符
		} else {
			$name_length += 1; // 英文字符算1个字符
		}
	}
	
	if ($name_length > 12) {
		echo "<script>alert('网站名称过长！最多12个字符（6个汉字）'); history.back();</script>";
		exit;
	}
	
	if (empty($web_url)) {
		echo "<script>alert('请输入网站域名！'); history.back();</script>";
		exit;
	} else {
		// 简单的域名格式检查
		if (!preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $web_url)) {
			echo "<script>alert('请输入正确的网站域名！'); history.back();</script>";
			exit;
		}
	}
	
	if (empty($web_intro)) {
		echo "<script>alert('请输入网站简介！'); history.back();</script>";
		exit;
	}
	
	if (empty($web_email)) {
		echo "<script>alert('请输入电子邮箱！'); history.back();</script>";
		exit;
	} else {
		if (!filter_var($web_email, FILTER_VALIDATE_EMAIL)) {
			echo "<script>alert('请输入正确的电子邮箱！'); history.back();</script>";
			exit;
		}
	}
	
	// 检查网站是否已存在
	$table = $DB->table('websites');
	$query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM $table WHERE web_url='$web_url'");
	if ($DB->num_rows($query)) {
		$existing_web = $DB->fetch_array($query);
		$status_msg = '';
		
		switch($existing_web['web_status']) {
			case 1:
				$status_msg = '该网站已被拉黑，无法重复提交！';
				break;
			case 2:
				$status_msg = '该网站正在审核中，请勿重复提交！';
				break;
			case 3:
				$status_msg = '该网站已收录（收录时间：' . date('Y-m-d', $existing_web['web_ctime']) . '），请勿重复提交！';
				break;
			case 4:
				$status_msg = '该网站审核不通过，请修改后重新提交！';
				break;
			default:
				$status_msg = '该网站已存在，请勿重复提交！';
		}
		
		echo "<script>alert('$status_msg'); history.back();</script>";
		exit;
	}
	
	// 准备数据
	$web_time = time();
	$web_data = array(
		'cate_id' => $cate_id,
		'web_name' => $web_name,
		'web_url' => $web_url,
		'web_tags' => $web_tags,
		'web_intro' => $web_intro,
		'web_owner' => $web_owner,
		'web_email' => $web_email,
		'web_pic' => 'themes/default/skin/wait.png',
		'web_status' => 2, // 待审核状态
		'web_ctime' => $web_time,
		'web_utime' => $web_time,
		'web_ip' => get_client_ip(),
		'web_grank' => 0,
		'web_brank' => 0,
		'web_srank' => 0,
		'web_arank' => 0
	);
	
	// 插入数据
	$DB->insert($table, $web_data);
	$web_id = $DB->insert_id();
	
	// 插入统计数据
	$stat_data = array(
		'web_id' => $web_id,
		'web_ip' => get_client_ip(),
		'web_grank' => 0,
		'web_brank' => 0,
		'web_srank' => 0,
		'web_arank' => 0,
		'web_instat' => 0,
		'web_outstat' => 0,
		'web_views' => 0,
		'web_errors' => 0,
		'web_ctime' => $web_time,
		'web_utime' => $web_time
	);
	$DB->insert($DB->table('webdata'), $stat_data);
	
	// 更新分类统计
	$DB->query("UPDATE ".$DB->table('categories')." SET cate_postcount=cate_postcount+1 WHERE cate_id=$cate_id");
	
	// 更新缓存
	update_cache('archives');
	
	echo "<script>alert('网站提交成功！我们会尽快审核您的网站。'); location.href='?mod=index';</script>";
	exit;
}

// 获取分类选项
$category_option = get_category_option(0, 'website');

// 设置模板变量
$smarty->assign('site_title', $pagename.' - '.$options['site_name']);
$smarty->assign('site_keywords', '提交网站,网站收录,免费收录');
$smarty->assign('site_description', '免费提交网站到分类目录，快速收录您的网站。');
$smarty->assign('site_path', '当前位置：<a href="'.$options['site_url'].'">'.$options['site_name'].'</a> &raquo; '.$pagename);
$smarty->assign('pageurl', $pageurl);
$smarty->assign('category_option', $category_option);

// 提交配置
$cfg = array(
	'is_enabled_submit' => 'yes',
	'submit_close_reason' => '网站提交功能暂时关闭，请稍后再试。'
);
$smarty->assign('cfg', $cfg);

smarty_output($tempfile);
?>
