<?php
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$type = trim($_GET['type']);
$cate_id = intval($_GET['cid']);
$format = trim($_GET['format']) ?: 'xml';
if (empty($type)) $type = 'all';

switch ($type) {
    case 'webdir':
    case 'weblink':
        get_website_sitemap($cate_id);
        break;
    case 'article':
        get_article_sitemap($cate_id);
        break;
    case 'category':
        get_category_sitemap();
        break;
    case 'vip':
        get_vip_sitemap($cate_id);
        break;
    case 'tags':
        get_tags_sitemap();
        break;
    case 'pending':
        get_pending_sitemap($cate_id);
        break;
    case 'rejected':
        get_rejected_sitemap($cate_id);
        break;
    case 'blacklist':
        get_blacklist_sitemap($cate_id);
        break;
    case 'search':
        get_search_sitemap();
        break;
    case 'member':
        get_member_sitemap();
        break;
    case 'other':
        get_other_sitemap();
        break;
    case 'all':
    default:
        get_all_sitemap();
        break;
}

?>