<?php

class TP_yyStackEntry
{
    public $stateno;       /* The state-number */
    public $major;         /* The major token value.  This is the code
                     ** number for the token at this stack level */
    public $minor; /* The user-supplied minor token value.  This
                     ** is the value of the token  */
}

// line 11 "../smarty/lexer/smarty_internal_templateparser.y"

/**
 * Smarty Template Parser Class
 *
 * This is the template parser.
 * It is generated from the smarty_internal_templateparser.y file
 *
 * <AUTHOR> Tews <<EMAIL>>
 */
class Smarty_Internal_Templateparser
{
    // line 23 "../smarty/lexer/smarty_internal_templateparser.y"
    const ERR1                      = 'Security error: Call to private object member not allowed';
    const ERR2                      = 'Security error: Call to dynamic object member not allowed';
    const ERR3                      = 'PHP in template not allowed. Use SmartyBC to enable it';
    const TP_VERT                   = 1;
    const TP_COLON                  = 2;
    const TP_PHP                    = 3;
    const TP_TEXT                   = 4;
    const TP_STRIPON                = 5;
    const TP_STRIPOFF               = 6;
    const TP_LITERALSTART           = 7;
    const TP_LITERALEND             = 8;
    const TP_LITERAL                = 9;
    const TP_SIMPELOUTPUT           = 10;
    const TP_SIMPLETAG              = 11;
    const TP_SMARTYBLOCKCHILDPARENT = 12;
    const TP_LDEL                   = 13;
    const TP_RDEL                   = 14;
    const TP_DOLLARID               = 15;
    const TP_EQUAL                  = 16;
    const TP_ID                     = 17;
    const TP_PTR                    = 18;
    const TP_LDELMAKENOCACHE        = 19;
    const TP_LDELIF                 = 20;
    const TP_LDELFOR                = 21;
    const TP_SEMICOLON              = 22;
    const TP_INCDEC                 = 23;
    const TP_TO                     = 24;
    const TP_STEP                   = 25;
    const TP_LDELFOREACH            = 26;
    const TP_SPACE                  = 27;
    const TP_AS                     = 28;
    const TP_APTR                   = 29;
    const TP_LDELSETFILTER          = 30;
    const TP_CLOSETAG               = 31;
    const TP_LDELSLASH              = 32;
    const TP_ATTR                   = 33;
    const TP_INTEGER                = 34;
    const TP_COMMA                  = 35;
    const TP_OPENP                  = 36;
    const TP_CLOSEP                 = 37;
    const TP_MATH                   = 38;
    const TP_UNIMATH                = 39;
    const TP_ISIN                   = 40;
    const TP_QMARK                  = 41;
    const TP_NOT                    = 42;
    const TP_TYPECAST               = 43;
    const TP_HEX                    = 44;
    const TP_DOT                    = 45;
    const TP_INSTANCEOF             = 46;
    const TP_SINGLEQUOTESTRING      = 47;
    const TP_DOUBLECOLON            = 48;
    const TP_NAMESPACE              = 49;
    const TP_AT                     = 50;
    const TP_HATCH                  = 51;
    const TP_OPENB                  = 52;
    const TP_CLOSEB                 = 53;
    const TP_DOLLAR                 = 54;
    const TP_LOGOP                  = 55;
    const TP_SLOGOP                 = 56;
    const TP_TLOGOP                 = 57;
    const TP_SINGLECOND             = 58;
    const TP_ARRAYOPEN              = 59;
    const TP_QUOTE                  = 60;
    const TP_BACKTICK               = 61;
    const YY_NO_ACTION              = 516;
    const YY_ACCEPT_ACTION          = 515;
    const YY_ERROR_ACTION           = 514;
    const YY_SZ_ACTTAB              = 2071;
    const YY_SHIFT_USE_DFLT         = -31;
    const YY_SHIFT_MAX              = 230;
    const YY_REDUCE_USE_DFLT        = -91;
    const YY_REDUCE_MAX             = 178;
    const YYNOCODE                  = 110;
    const YYSTACKDEPTH              = 500;
    const YYNSTATE                  = 327;
    const YYNRULE                   = 187;
    const YYERRORSYMBOL             = 62;
    const YYERRSYMDT                = 'yy0';
    const YYFALLBACK                = 0;

    public static $yy_action        = array(
        251, 234, 237, 1, 144, 127, 428, 184, 199, 212,
        10, 54, 19, 175, 282, 215, 109, 389, 428, 428,
        224, 321, 223, 303, 203, 389, 13, 389, 281, 43,
        389, 428, 41, 40, 266, 225, 389, 213, 389, 194,
        389, 52, 4, 308, 301, 383, 34, 209, 222, 3,
        50, 153, 251, 234, 237, 1, 199, 131, 383, 198,
        305, 212, 10, 54, 383, 16, 199, 428, 109, 385,
        132, 18, 224, 321, 223, 222, 221, 12, 32, 428,
        116, 43, 385, 262, 41, 40, 266, 225, 385, 233,
        95, 194, 16, 52, 4, 131, 301, 252, 18, 265,
        164, 3, 50, 324, 251, 234, 237, 1, 23, 130,
        229, 198, 150, 212, 10, 54, 326, 11, 170, 284,
        109, 42, 22, 239, 224, 321, 223, 193, 221, 261,
        13, 52, 157, 43, 301, 286, 41, 40, 266, 225,
        205, 233, 5, 194, 96, 52, 4, 263, 301, 301,
        99, 349, 96, 3, 50, 199, 251, 234, 237, 1,
        238, 130, 241, 181, 349, 212, 10, 54, 382, 240,
        349, 36, 109, 185, 104, 256, 224, 321, 223, 132,
        191, 382, 13, 49, 91, 43, 12, 382, 41, 40,
        266, 225, 257, 233, 152, 194, 457, 52, 4, 457,
        301, 301, 228, 457, 282, 3, 50, 285, 251, 234,
        237, 1, 301, 131, 441, 198, 238, 212, 10, 54,
        349, 441, 325, 175, 109, 30, 349, 273, 224, 321,
        223, 20, 221, 295, 32, 211, 457, 39, 166, 49,
        41, 40, 266, 225, 87, 233, 205, 194, 279, 52,
        4, 24, 301, 204, 200, 280, 99, 3, 50, 199,
        251, 234, 237, 1, 31, 130, 96, 198, 205, 212,
        10, 54, 350, 55, 293, 207, 109, 283, 99, 96,
        224, 321, 223, 199, 180, 350, 13, 134, 230, 43,
        222, 350, 41, 40, 266, 225, 104, 233, 316, 194,
        279, 52, 4, 24, 301, 165, 284, 280, 85, 3,
        50, 25, 251, 234, 237, 1, 131, 129, 210, 198,
        14, 212, 10, 54, 269, 270, 301, 116, 109, 295,
        216, 211, 224, 321, 223, 171, 221, 95, 13, 28,
        219, 43, 323, 9, 41, 40, 266, 225, 151, 233,
        324, 194, 52, 52, 4, 301, 301, 30, 282, 302,
        178, 3, 50, 7, 251, 234, 237, 1, 136, 130,
        304, 179, 238, 212, 10, 54, 279, 175, 282, 24,
        109, 238, 429, 280, 224, 321, 223, 177, 221, 270,
        13, 255, 281, 43, 429, 49, 41, 40, 266, 225,
        275, 233, 318, 194, 49, 52, 4, 276, 301, 163,
        26, 199, 8, 3, 50, 119, 251, 234, 237, 1,
        11, 93, 291, 51, 107, 212, 10, 54, 226, 428,
        206, 201, 109, 148, 178, 322, 224, 321, 223, 441,
        221, 428, 13, 282, 9, 43, 441, 115, 41, 40,
        266, 225, 167, 233, 227, 194, 457, 52, 4, 457,
        301, 96, 158, 457, 101, 3, 50, 271, 251, 234,
        237, 1, 282, 130, 235, 186, 135, 212, 10, 54,
        199, 37, 119, 315, 109, 165, 284, 176, 224, 321,
        223, 104, 221, 149, 13, 281, 146, 43, 281, 300,
        41, 40, 266, 225, 30, 233, 289, 194, 21, 52,
        4, 272, 301, 211, 18, 301, 161, 3, 50, 110,
        251, 234, 237, 1, 137, 128, 282, 198, 268, 212,
        10, 54, 222, 169, 515, 92, 109, 172, 284, 31,
        224, 321, 223, 29, 221, 238, 6, 260, 53, 43,
        232, 139, 41, 40, 266, 225, 154, 233, 178, 194,
        168, 52, 4, 214, 301, 145, 99, 33, 49, 3,
        50, 245, 208, 211, 320, 282, 90, 111, 311, 183,
        98, 70, 309, 297, 236, 178, 95, 319, 142, 258,
        247, 267, 249, 264, 250, 195, 231, 199, 246, 324,
        317, 253, 254, 259, 126, 137, 133, 251, 234, 237,
        1, 326, 290, 105, 143, 156, 212, 10, 54, 88,
        84, 83, 484, 109, 322, 282, 37, 224, 321, 223,
        245, 208, 211, 320, 281, 90, 111, 298, 182, 98,
        56, 245, 298, 211, 178, 95, 103, 147, 258, 197,
        102, 75, 141, 250, 195, 231, 95, 246, 324, 258,
        279, 242, 89, 24, 250, 195, 231, 280, 246, 324,
        298, 298, 298, 298, 298, 298, 298, 16, 298, 192,
        277, 298, 298, 18, 294, 44, 45, 38, 298, 298,
        251, 234, 237, 2, 298, 296, 298, 298, 298, 212,
        10, 54, 310, 312, 313, 314, 109, 162, 298, 298,
        224, 321, 223, 298, 298, 298, 294, 282, 298, 42,
        22, 239, 251, 234, 237, 2, 298, 296, 298, 298,
        298, 212, 10, 54, 298, 159, 298, 298, 109, 298,
        298, 17, 224, 321, 223, 282, 298, 42, 22, 239,
        298, 298, 245, 298, 211, 278, 298, 103, 111, 298,
        183, 98, 70, 298, 298, 298, 298, 95, 298, 298,
        258, 298, 292, 17, 298, 250, 195, 231, 279, 246,
        324, 24, 298, 395, 245, 280, 211, 298, 298, 103,
        298, 298, 197, 102, 75, 16, 298, 140, 298, 95,
        298, 18, 258, 298, 298, 298, 298, 250, 195, 231,
        298, 246, 324, 298, 298, 298, 298, 428, 298, 395,
        395, 395, 202, 277, 298, 245, 298, 211, 298, 428,
        103, 298, 298, 197, 120, 69, 395, 395, 395, 395,
        95, 298, 298, 258, 298, 298, 298, 160, 250, 195,
        231, 86, 246, 324, 245, 16, 211, 282, 298, 103,
        196, 18, 197, 120, 69, 298, 44, 45, 38, 95,
        298, 298, 258, 298, 298, 298, 178, 250, 195, 231,
        298, 246, 324, 310, 312, 313, 314, 298, 298, 190,
        245, 298, 211, 298, 298, 103, 298, 298, 197, 102,
        75, 298, 298, 298, 298, 95, 298, 298, 258, 298,
        298, 298, 298, 250, 195, 231, 298, 246, 324, 298,
        298, 298, 245, 298, 211, 298, 199, 100, 298, 288,
        197, 120, 47, 298, 106, 298, 298, 95, 298, 353,
        258, 155, 298, 218, 298, 250, 195, 231, 298, 246,
        324, 282, 16, 42, 22, 239, 298, 245, 18, 211,
        298, 428, 103, 298, 298, 197, 120, 69, 298, 298,
        298, 298, 95, 428, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 100, 189, 298, 197, 120, 59, 245, 207, 211,
        298, 95, 103, 298, 258, 197, 120, 81, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 80, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 67, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 57, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 58, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 82, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 97, 76, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 71, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 187, 120, 61, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 63, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 94, 79, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 59, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 77, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 188, 108, 64, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 65, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 97, 66, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 68, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 62, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 60, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 74, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 72, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 48, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 120, 46, 298, 298,
        298, 298, 95, 298, 298, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 120, 78, 245, 298, 211,
        298, 95, 103, 298, 258, 197, 120, 73, 298, 250,
        195, 231, 95, 246, 324, 258, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 125, 298, 298, 298,
        298, 298, 95, 298, 298, 298, 298, 298, 298, 244,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 114, 298, 245, 298, 211,
        298, 95, 103, 298, 298, 197, 122, 298, 243, 250,
        195, 231, 95, 246, 324, 298, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 117, 298, 298, 298,
        298, 298, 95, 298, 298, 298, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 121, 298, 245, 298, 211,
        298, 95, 103, 298, 298, 197, 124, 298, 298, 250,
        195, 231, 95, 246, 324, 298, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 298, 245, 298, 211,
        298, 298, 103, 298, 298, 197, 118, 298, 298, 298,
        298, 298, 95, 298, 298, 298, 298, 298, 298, 298,
        250, 195, 231, 298, 246, 324, 245, 298, 211, 298,
        298, 103, 298, 298, 197, 123, 298, 245, 298, 211,
        298, 95, 103, 298, 298, 197, 113, 298, 298, 250,
        195, 231, 95, 246, 324, 298, 298, 298, 298, 298,
        250, 195, 231, 220, 246, 324, 298, 27, 298, 16,
        298, 457, 298, 298, 457, 18, 298, 26, 457, 441,
        44, 45, 38, 217, 44, 45, 38, 298, 298, 298,
        298, 298, 298, 298, 298, 298, 298, 310, 312, 313,
        314, 310, 312, 313, 314, 298, 441, 298, 298, 441,
        298, 457, 220, 441, 457, 298, 298, 457, 298, 298,
        457, 457, 441, 457, 298, 298, 220, 457, 441, 298,
        298, 298, 298, 298, 457, 298, 298, 457, 298, 298,
        5, 457, 441, 298, 298, 298, 298, 298, 298, 441,
        298, 298, 441, 298, 457, 441, 441, 298, 441, 298,
        457, 298, 441, 306, 298, 298, 298, 298, 298, 441,
        298, 298, 441, 298, 457, 220, 441, 298, 298, 298,
        298, 298, 298, 457, 298, 298, 457, 298, 298, 15,
        457, 441, 35, 274, 44, 45, 38, 457, 298, 298,
        457, 298, 298, 298, 457, 441, 298, 298, 298, 298,
        298, 310, 312, 313, 314, 298, 298, 298, 441, 298,
        298, 441, 298, 457, 298, 441, 287, 298, 44, 45,
        38, 298, 441, 298, 298, 441, 298, 457, 298, 441,
        248, 298, 298, 298, 298, 310, 312, 313, 314, 298,
        44, 45, 38, 298, 298, 112, 298, 44, 45, 38,
        298, 173, 298, 298, 44, 45, 38, 310, 312, 313,
        314, 44, 45, 38, 310, 312, 313, 314, 298, 298,
        299, 310, 312, 313, 314, 44, 45, 38, 310, 312,
        313, 314, 174, 298, 298, 298, 138, 298, 298, 298,
        298, 298, 310, 312, 313, 314, 44, 45, 38, 298,
        298, 298, 44, 45, 38, 298, 44, 45, 38, 298,
        44, 45, 38, 310, 312, 313, 314, 307, 298, 310,
        312, 313, 314, 310, 312, 313, 314, 310, 312, 313,
        314,
    );

    public static $yy_lookahead     = array(
        10, 11, 12, 13, 74, 15, 36, 17, 1, 19,
        20, 21, 29, 103, 84, 45, 26, 14, 48, 36,
        30, 31, 32, 53, 34, 22, 36, 24, 98, 39,
        27, 48, 42, 43, 44, 45, 33, 47, 35, 49,
        37, 51, 52, 53, 54, 14, 16, 16, 45, 59,
        60, 96, 10, 11, 12, 13, 1, 15, 27, 17,
        53, 19, 20, 21, 33, 27, 1, 36, 26, 14,
        45, 33, 30, 31, 32, 45, 34, 52, 36, 48,
        72, 39, 27, 75, 42, 43, 44, 45, 33, 47,
        82, 49, 27, 51, 52, 15, 54, 17, 33, 91,
        83, 59, 60, 95, 10, 11, 12, 13, 13, 15,
        15, 17, 17, 19, 20, 21, 97, 35, 99, 100,
        26, 86, 87, 88, 30, 31, 32, 66, 34, 49,
        36, 51, 96, 39, 54, 53, 42, 43, 44, 45,
        72, 47, 16, 49, 18, 51, 52, 79, 54, 54,
        82, 14, 18, 59, 60, 1, 10, 11, 12, 13,
        23, 15, 15, 17, 27, 19, 20, 21, 14, 17,
        33, 13, 26, 15, 48, 17, 30, 31, 32, 45,
        34, 27, 36, 46, 83, 39, 52, 33, 42, 43,
        44, 45, 34, 47, 74, 49, 10, 51, 52, 13,
        54, 54, 50, 17, 84, 59, 60, 14, 10, 11,
        12, 13, 54, 15, 45, 17, 23, 19, 20, 21,
        27, 52, 100, 103, 26, 35, 33, 37, 30, 31,
        32, 22, 34, 67, 36, 69, 50, 39, 83, 46,
        42, 43, 44, 45, 35, 47, 72, 49, 10, 51,
        52, 13, 54, 79, 80, 17, 82, 59, 60, 1,
        10, 11, 12, 13, 16, 15, 18, 17, 72, 19,
        20, 21, 14, 107, 108, 79, 26, 71, 82, 18,
        30, 31, 32, 1, 34, 27, 36, 15, 50, 39,
        45, 33, 42, 43, 44, 45, 48, 47, 53, 49,
        10, 51, 52, 13, 54, 99, 100, 17, 36, 59,
        60, 29, 10, 11, 12, 13, 15, 15, 17, 17,
        13, 19, 20, 21, 8, 9, 54, 72, 26, 67,
        75, 69, 30, 31, 32, 78, 34, 82, 36, 24,
        50, 39, 17, 36, 42, 43, 44, 45, 74, 47,
        95, 49, 51, 51, 52, 54, 54, 35, 84, 37,
        103, 59, 60, 36, 10, 11, 12, 13, 74, 15,
        108, 17, 23, 19, 20, 21, 10, 103, 84, 13,
        26, 23, 36, 17, 30, 31, 32, 7, 34, 9,
        36, 17, 98, 39, 48, 46, 42, 43, 44, 45,
        17, 47, 53, 49, 46, 51, 52, 93, 54, 78,
        16, 1, 36, 59, 60, 101, 10, 11, 12, 13,
        35, 15, 37, 17, 48, 19, 20, 21, 18, 36,
        65, 66, 26, 74, 103, 104, 30, 31, 32, 45,
        34, 48, 36, 84, 36, 39, 52, 17, 42, 43,
        44, 45, 15, 47, 17, 49, 10, 51, 52, 13,
        54, 18, 74, 17, 82, 59, 60, 34, 10, 11,
        12, 13, 84, 15, 93, 17, 15, 19, 20, 21,
        1, 2, 101, 101, 26, 99, 100, 17, 30, 31,
        32, 48, 34, 96, 36, 98, 96, 39, 98, 71,
        42, 43, 44, 45, 35, 47, 37, 49, 27, 51,
        52, 67, 54, 69, 33, 54, 74, 59, 60, 17,
        10, 11, 12, 13, 96, 15, 84, 17, 34, 19,
        20, 21, 45, 78, 63, 64, 26, 99, 100, 16,
        30, 31, 32, 16, 34, 23, 36, 17, 17, 39,
        23, 51, 42, 43, 44, 45, 72, 47, 103, 49,
        78, 51, 52, 17, 54, 74, 82, 41, 46, 59,
        60, 67, 68, 69, 70, 84, 72, 73, 53, 75,
        76, 77, 53, 61, 15, 103, 82, 14, 51, 85,
        14, 37, 17, 8, 90, 91, 92, 1, 94, 95,
        3, 4, 5, 6, 7, 96, 82, 10, 11, 12,
        13, 97, 84, 81, 96, 74, 19, 20, 21, 78,
        82, 82, 1, 26, 104, 84, 2, 30, 31, 32,
        67, 68, 69, 70, 98, 72, 73, 109, 75, 76,
        77, 67, 109, 69, 103, 82, 72, 96, 85, 75,
        76, 77, 96, 90, 91, 92, 82, 94, 95, 85,
        10, 14, 96, 13, 90, 91, 92, 17, 94, 95,
        109, 109, 109, 109, 109, 109, 109, 27, 109, 105,
        106, 109, 109, 33, 4, 38, 39, 40, 109, 109,
        10, 11, 12, 13, 109, 15, 109, 109, 109, 19,
        20, 21, 55, 56, 57, 58, 26, 74, 109, 109,
        30, 31, 32, 109, 109, 109, 4, 84, 109, 86,
        87, 88, 10, 11, 12, 13, 109, 15, 109, 109,
        109, 19, 20, 21, 109, 74, 109, 109, 26, 109,
        60, 61, 30, 31, 32, 84, 109, 86, 87, 88,
        109, 109, 67, 109, 69, 70, 109, 72, 73, 109,
        75, 76, 77, 109, 109, 109, 109, 82, 109, 109,
        85, 109, 60, 61, 109, 90, 91, 92, 10, 94,
        95, 13, 109, 2, 67, 17, 69, 109, 109, 72,
        109, 109, 75, 76, 77, 27, 109, 29, 109, 82,
        109, 33, 85, 109, 109, 109, 109, 90, 91, 92,
        109, 94, 95, 109, 109, 109, 109, 36, 109, 38,
        39, 40, 105, 106, 109, 67, 109, 69, 109, 48,
        72, 109, 109, 75, 76, 77, 55, 56, 57, 58,
        82, 109, 109, 85, 109, 109, 109, 74, 90, 91,
        92, 78, 94, 95, 67, 27, 69, 84, 109, 72,
        102, 33, 75, 76, 77, 109, 38, 39, 40, 82,
        109, 109, 85, 109, 109, 109, 103, 90, 91, 92,
        109, 94, 95, 55, 56, 57, 58, 109, 109, 102,
        67, 109, 69, 109, 109, 72, 109, 109, 75, 76,
        77, 109, 109, 109, 109, 82, 109, 109, 85, 109,
        109, 109, 109, 90, 91, 92, 109, 94, 95, 109,
        109, 109, 67, 109, 69, 109, 1, 72, 109, 106,
        75, 76, 77, 109, 79, 109, 109, 82, 109, 14,
        85, 74, 109, 18, 109, 90, 91, 92, 109, 94,
        95, 84, 27, 86, 87, 88, 109, 67, 33, 69,
        109, 36, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 48, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 102, 109, 75, 76, 77, 67, 79, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 77, 109, 109,
        109, 109, 82, 109, 109, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 77, 67, 109, 69,
        109, 82, 72, 109, 85, 75, 76, 77, 109, 90,
        91, 92, 82, 94, 95, 85, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 109, 109, 109,
        109, 109, 82, 109, 109, 109, 109, 109, 109, 89,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 109, 67, 109, 69,
        109, 82, 72, 109, 109, 75, 76, 109, 89, 90,
        91, 92, 82, 94, 95, 109, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 109, 109, 109,
        109, 109, 82, 109, 109, 109, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 109, 67, 109, 69,
        109, 82, 72, 109, 109, 75, 76, 109, 109, 90,
        91, 92, 82, 94, 95, 109, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 109, 67, 109, 69,
        109, 109, 72, 109, 109, 75, 76, 109, 109, 109,
        109, 109, 82, 109, 109, 109, 109, 109, 109, 109,
        90, 91, 92, 109, 94, 95, 67, 109, 69, 109,
        109, 72, 109, 109, 75, 76, 109, 67, 109, 69,
        109, 82, 72, 109, 109, 75, 76, 109, 109, 90,
        91, 92, 82, 94, 95, 109, 109, 109, 109, 109,
        90, 91, 92, 2, 94, 95, 109, 25, 109, 27,
        109, 10, 109, 109, 13, 33, 109, 16, 17, 18,
        38, 39, 40, 37, 38, 39, 40, 109, 109, 109,
        109, 109, 109, 109, 109, 109, 109, 55, 56, 57,
        58, 55, 56, 57, 58, 109, 45, 109, 109, 48,
        109, 50, 2, 52, 10, 109, 109, 13, 109, 109,
        10, 17, 18, 13, 109, 109, 2, 17, 18, 109,
        109, 109, 109, 109, 10, 109, 109, 13, 109, 109,
        16, 17, 18, 109, 109, 109, 109, 109, 109, 45,
        109, 109, 48, 109, 50, 45, 52, 109, 48, 109,
        50, 109, 52, 53, 109, 109, 109, 109, 109, 45,
        109, 109, 48, 109, 50, 2, 52, 109, 109, 109,
        109, 109, 109, 10, 109, 109, 13, 109, 109, 2,
        17, 18, 2, 37, 38, 39, 40, 10, 109, 109,
        13, 109, 109, 109, 17, 18, 109, 109, 109, 109,
        109, 55, 56, 57, 58, 109, 109, 109, 45, 109,
        109, 48, 109, 50, 109, 52, 14, 109, 38, 39,
        40, 109, 45, 109, 109, 48, 109, 50, 109, 52,
        14, 109, 109, 109, 109, 55, 56, 57, 58, 109,
        38, 39, 40, 109, 109, 22, 109, 38, 39, 40,
        109, 14, 109, 109, 38, 39, 40, 55, 56, 57,
        58, 38, 39, 40, 55, 56, 57, 58, 109, 109,
        61, 55, 56, 57, 58, 38, 39, 40, 55, 56,
        57, 58, 14, 109, 109, 109, 28, 109, 109, 109,
        109, 109, 55, 56, 57, 58, 38, 39, 40, 109,
        109, 109, 38, 39, 40, 109, 38, 39, 40, 109,
        38, 39, 40, 55, 56, 57, 58, 53, 109, 55,
        56, 57, 58, 55, 56, 57, 58, 55, 56, 57,
        58,
    );

    public static $yy_shift_ofst    = array(
        -31, 406, 406, 458, 458, 94, 510, 94, 94, 94,
        510, 458, -10, 94, 94, 354, 146, 94, 94, 94,
        94, 146, 94, 94, 94, 94, 250, 94, 94, 94,
        94, 94, 94, 302, 94, 94, 94, 198, 42, 42,
        42, 42, 42, 42, 42, 42, 1772, 828, 828, 80,
        712, 925, 301, 65, 272, 680, 1942, 1920, 1886, 1776,
        647, 1949, 1977, 2008, 2004, 1963, 1998, 1956, 2012, 2012,
        2012, 2012, 2012, 2012, 2012, 2012, 2012, 2012, 2012, 2012,
        2012, 2012, 2012, 768, 650, 272, 65, 272, 65, 134,
        126, 479, 597, 1854, 154, 290, 95, 55, 258, 366,
        248, 366, 282, 443, 437, 38, 38, 437, 7, 481,
        410, 38, 461, 621, 596, 596, 261, 596, 596, 261,
        596, 596, 596, 596, 596, -31, -31, 1840, 1791, 1917,
        1903, 1834, 158, 238, 394, 446, 38, 25, 147, 169,
        147, 25, 169, 25, 38, 38, 25, 25, 38, 25,
        307, 38, 38, 25, 527, 38, 38, 25, 38, 38,
        38, 38, 38, 596, 624, 261, 624, 327, 596, 596,
        261, 596, 261, -31, -31, -31, -31, -31, -31, 781,
        3, 31, 193, 137, -30, 186, -17, 522, 349, 469,
        322, 30, 82, 316, 346, 376, 190, 358, 393, 152,
        209, 380, 385, 245, 315, 523, 585, 554, 576, 575,
        537, 573, 569, 529, 525, 546, 500, 526, 531, 325,
        530, 487, 494, 502, 470, 433, 430, 408, 383, 327,
        374,
    );

    public static $yy_reduce_ofst   = array(
        471, 504, 563, 717, 574, 685, 919, 890, 787, 758,
        855, 823, 1240, 1199, 1140, 1100, 1070, 1129, 1170, 1210,
        1269, 1280, 1310, 1339, 1350, 1380, 1409, 1420, 1450, 1479,
        1490, 1059, 1030, 1000, 930, 960, 989, 1520, 1549, 1700,
        1619, 1689, 1660, 1630, 1590, 1560, 633, 661, 867, 8,
        166, 773, 255, 541, 174, 262, 35, 35, 35, 35,
        35, 35, 35, 35, 35, 35, 35, 35, 35, 35,
        35, 35, 35, 35, 35, 35, 35, 35, 35, 35,
        35, 35, 35, 294, -70, 196, 120, 68, 274, 19,
        206, 331, 444, 428, 257, 400, 382, 257, 257, 400,
        386, 397, 257, 386, 381, 388, 359, 314, 257, 442,
        482, 491, 484, 257, 257, 455, 386, 257, 257, 438,
        257, 257, 257, 257, 257, 257, 365, 509, 509, 509,
        509, 509, 524, 536, 509, 509, 528, 514, 539, 551,
        538, 514, 556, 514, 528, 528, 514, 514, 528, 514,
        518, 528, 528, 514, 532, 528, 528, 514, 528, 528,
        528, 528, 528, -90, 520, 122, 520, 566, -90, -90,
        122, -90, 122, -45, 36, 155, 101, 61, 17,
    );

    public static $yyExpectedTokens = array(
        array(),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 53, 54, 59,
            60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(
            10, 11, 12, 13, 15, 17, 19, 20, 21, 26, 30, 31, 32, 34, 36, 39, 42, 43, 44, 45, 47, 49, 51, 52, 54, 59, 60,
        ),
        array(25, 27, 33, 38, 39, 40, 55, 56, 57, 58,),
        array(27, 33, 38, 39, 40, 55, 56, 57, 58,),
        array(27, 33, 38, 39, 40, 55, 56, 57, 58,),
        array(15, 17, 49, 51, 54,),
        array(4, 10, 11, 12, 13, 15, 19, 20, 21, 26, 30, 31, 32, 60, 61,),
        array(1, 14, 18, 27, 33, 36, 48,),
        array(15, 17, 51, 54,),
        array(1, 27, 33,),
        array(15, 36, 54,),
        array(4, 10, 11, 12, 13, 15, 19, 20, 21, 26, 30, 31, 32, 60, 61,),
        array(14, 38, 39, 40, 55, 56, 57, 58,),
        array(2, 38, 39, 40, 55, 56, 57, 58,),
        array(37, 38, 39, 40, 55, 56, 57, 58,),
        array(37, 38, 39, 40, 55, 56, 57, 58,),
        array(14, 38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58, 61,),
        array(14, 38, 39, 40, 55, 56, 57, 58,),
        array(14, 38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 53, 55, 56, 57, 58,),
        array(22, 38, 39, 40, 55, 56, 57, 58,),
        array(28, 38, 39, 40, 55, 56, 57, 58,),
        array(14, 38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(38, 39, 40, 55, 56, 57, 58,),
        array(10, 13, 17, 27, 29, 33,),
        array(10, 13, 17, 27, 33,),
        array(15, 36, 54,),
        array(1, 27, 33,),
        array(15, 36, 54,),
        array(1, 27, 33,),
        array(18, 45, 52,),
        array(16, 18, 48,),
        array(1, 2,),
        array(3, 4, 5, 6, 7, 10, 11, 12, 13, 19, 20, 21, 26, 30, 31, 32,),
        array(2, 10, 13, 16, 17, 18, 45, 48, 50, 52,),
        array(1, 14, 27, 33,),
        array(10, 13, 17, 50,),
        array(13, 15, 17, 54,),
        array(1, 14, 27, 33,),
        array(1, 14, 27, 33,),
        array(10, 13, 17,),
        array(16, 18, 48,),
        array(10, 13, 17,),
        array(1, 29,),
        array(18, 48,),
        array(15, 17,),
        array(27, 33,),
        array(27, 33,),
        array(15, 17,),
        array(1, 53,),
        array(27, 33,),
        array(1, 18,),
        array(27, 33,),
        array(15, 54,),
        array(1,),
        array(1,),
        array(1,),
        array(18,),
        array(1,),
        array(1,),
        array(18,),
        array(1,),
        array(1,),
        array(1,),
        array(1,),
        array(1,),
        array(),
        array(),
        array(2, 10, 13, 17, 18, 45, 48, 50, 52, 53,),
        array(2, 10, 13, 16, 17, 18, 45, 48, 50, 52,),
        array(2, 10, 13, 17, 18, 45, 48, 50, 52,),
        array(2, 10, 13, 17, 18, 45, 48, 50, 52,),
        array(10, 13, 17, 18, 45, 48, 50, 52,),
        array(13, 15, 17, 34, 54,),
        array(10, 13, 17, 50,),
        array(16, 45, 52,),
        array(10, 13, 17,),
        array(27, 33,),
        array(45, 52,),
        array(15, 54,),
        array(45, 52,),
        array(15, 54,),
        array(45, 52,),
        array(45, 52,),
        array(45, 52,),
        array(27, 33,),
        array(27, 33,),
        array(45, 52,),
        array(45, 52,),
        array(27, 33,),
        array(45, 52,),
        array(13, 36,),
        array(27, 33,),
        array(27, 33,),
        array(45, 52,),
        array(16, 23,),
        array(27, 33,),
        array(27, 33,),
        array(45, 52,),
        array(27, 33,),
        array(27, 33,),
        array(27, 33,),
        array(27, 33,),
        array(27, 33,),
        array(1,),
        array(2,),
        array(18,),
        array(2,),
        array(36,),
        array(1,),
        array(1,),
        array(18,),
        array(1,),
        array(18,),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(2, 36, 38, 39, 40, 48, 55, 56, 57, 58,),
        array(14, 22, 24, 27, 33, 35, 37, 45,),
        array(14, 16, 27, 33, 36, 48,),
        array(14, 23, 27, 33, 46,),
        array(14, 23, 27, 33, 46,),
        array(36, 45, 48, 53,),
        array(10, 13, 17, 50,),
        array(29, 36, 48,),
        array(23, 46, 61,),
        array(23, 46, 53,),
        array(35, 37,),
        array(35, 37,),
        array(16, 45,),
        array(35, 53,),
        array(8, 9,),
        array(36, 48,),
        array(36, 48,),
        array(35, 37,),
        array(23, 46,),
        array(36, 48,),
        array(17, 50,),
        array(22, 35,),
        array(7, 9,),
        array(35, 37,),
        array(45, 53,),
        array(24,),
        array(16,),
        array(8,),
        array(37,),
        array(14,),
        array(17,),
        array(51,),
        array(14,),
        array(15,),
        array(53,),
        array(53,),
        array(17,),
        array(51,),
        array(41,),
        array(17,),
        array(17,),
        array(17,),
        array(45,),
        array(34,),
        array(17,),
        array(17,),
        array(34,),
        array(17,),
        array(36,),
        array(17,),
        array(36,),
        array(17,),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
        array(),
    );

    public static $yy_default       = array(
        338, 514, 514, 499, 499, 514, 514, 476, 476, 476,
        514, 514, 514, 514, 514, 514, 514, 514, 514, 514,
        514, 514, 514, 514, 514, 514, 514, 514, 514, 514,
        514, 514, 514, 514, 514, 514, 514, 514, 514, 514,
        514, 514, 514, 514, 514, 514, 379, 358, 379, 514,
        514, 415, 514, 379, 514, 514, 351, 514, 514, 514,
        514, 514, 514, 514, 514, 514, 384, 514, 399, 475,
        351, 403, 390, 474, 500, 502, 384, 501, 363, 381,
        404, 386, 391, 379, 379, 514, 379, 514, 379, 489,
        431, 370, 327, 430, 393, 441, 514, 393, 393, 441,
        431, 441, 393, 431, 514, 379, 360, 514, 393, 379,
        373, 379, 514, 406, 402, 375, 431, 396, 398, 486,
        393, 408, 397, 407, 406, 483, 336, 430, 430, 430,
        430, 430, 514, 443, 457, 441, 367, 438, 514, 436,
        514, 435, 434, 466, 368, 348, 439, 437, 361, 467,
        441, 356, 354, 468, 514, 366, 355, 469, 362, 359,
        352, 369, 365, 371, 478, 463, 477, 441, 374, 376,
        490, 424, 487, 441, 441, 482, 482, 336, 482, 415,
        411, 415, 405, 405, 415, 442, 415, 405, 405, 514,
        514, 411, 514, 514, 425, 514, 514, 405, 415, 514,
        514, 334, 514, 411, 387, 514, 514, 514, 514, 514,
        514, 514, 514, 420, 514, 514, 514, 417, 514, 514,
        514, 411, 413, 514, 514, 514, 514, 488, 514, 457,
        514, 421, 364, 420, 340, 422, 357, 341, 409, 400,
        480, 457, 462, 401, 485, 423, 426, 342, 447, 380,
        416, 339, 428, 329, 330, 444, 445, 446, 394, 331,
        395, 429, 419, 388, 332, 418, 410, 392, 412, 333,
        335, 414, 337, 472, 417, 479, 427, 497, 347, 461,
        460, 459, 378, 346, 464, 510, 495, 511, 498, 473,
        377, 496, 503, 506, 513, 512, 509, 507, 504, 508,
        345, 458, 471, 448, 505, 454, 452, 455, 456, 450,
        491, 449, 492, 493, 494, 470, 451, 328, 453, 343,
        344, 372, 481, 432, 433, 465, 440,
    );

    public static $yyFallback       = array();

    public static $yyRuleName       = array(
        'start ::= template',
        'template ::= template PHP',
        'template ::= template TEXT',
        'template ::= template STRIPON',
        'template ::= template STRIPOFF',
        'template ::= template LITERALSTART literal_e2 LITERALEND',
        'literal_e2 ::= literal_e1 LITERALSTART literal_e1 LITERALEND',
        'literal_e2 ::= literal_e1',
        'literal_e1 ::= literal_e1 LITERAL',
        'literal_e1 ::=',
        'template ::= template smartytag',
        'template ::=',
        'smartytag ::= SIMPELOUTPUT',
        'smartytag ::= SIMPLETAG',
        'smartytag ::= SMARTYBLOCKCHILDPARENT',
        'smartytag ::= LDEL tagbody RDEL',
        'smartytag ::= tag RDEL',
        'tagbody ::= outattr',
        'tagbody ::= DOLLARID eqoutattr',
        'tagbody ::= varindexed eqoutattr',
        'eqoutattr ::= EQUAL outattr',
        'outattr ::= output attributes',
        'output ::= variable',
        'output ::= value',
        'output ::= expr',
        'tag ::= LDEL ID attributes',
        'tag ::= LDEL ID',
        'tag ::= LDEL ID modifierlist attributes',
        'tag ::= LDEL ID PTR ID attributes',
        'tag ::= LDEL ID PTR ID modifierlist attributes',
        'tag ::= LDELMAKENOCACHE DOLLARID',
        'tag ::= LDELIF expr',
        'tag ::= LDELIF expr attributes',
        'tag ::= LDELIF statement',
        'tag ::= LDELIF statement attributes',
        'tag ::= LDELFOR statements SEMICOLON expr SEMICOLON varindexed foraction attributes',
        'foraction ::= EQUAL expr',
        'foraction ::= INCDEC',
        'tag ::= LDELFOR statement TO expr attributes',
        'tag ::= LDELFOR statement TO expr STEP expr attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar attributes',
        'tag ::= LDELFOREACH SPACE expr AS varvar APTR varvar attributes',
        'tag ::= LDELFOREACH attributes',
        'tag ::= LDELSETFILTER ID modparameters',
        'tag ::= LDELSETFILTER ID modparameters modifierlist',
        'smartytag ::= CLOSETAG',
        'tag ::= LDELSLASH ID',
        'tag ::= LDELSLASH ID modifierlist',
        'tag ::= LDELSLASH ID PTR ID',
        'tag ::= LDELSLASH ID PTR ID modifierlist',
        'attributes ::= attributes attribute',
        'attributes ::= attribute',
        'attributes ::=',
        'attribute ::= SPACE ID EQUAL ID',
        'attribute ::= ATTR expr',
        'attribute ::= ATTR value',
        'attribute ::= SPACE ID',
        'attribute ::= SPACE expr',
        'attribute ::= SPACE value',
        'attribute ::= SPACE INTEGER EQUAL expr',
        'statements ::= statement',
        'statements ::= statements COMMA statement',
        'statement ::= DOLLARID EQUAL INTEGER',
        'statement ::= DOLLARID EQUAL expr',
        'statement ::= varindexed EQUAL expr',
        'statement ::= OPENP statement CLOSEP',
        'expr ::= value',
        'expr ::= ternary',
        'expr ::= DOLLARID COLON ID',
        'expr ::= expr MATH value',
        'expr ::= expr UNIMATH value',
        'expr ::= expr tlop value',
        'expr ::= expr lop expr',
        'expr ::= expr scond',
        'expr ::= expr ISIN array',
        'expr ::= expr ISIN value',
        'ternary ::= OPENP expr CLOSEP QMARK DOLLARID COLON expr',
        'ternary ::= OPENP expr CLOSEP QMARK expr COLON expr',
        'value ::= variable',
        'value ::= UNIMATH value',
        'value ::= NOT value',
        'value ::= TYPECAST value',
        'value ::= variable INCDEC',
        'value ::= HEX',
        'value ::= INTEGER',
        'value ::= INTEGER DOT INTEGER',
        'value ::= INTEGER DOT',
        'value ::= DOT INTEGER',
        'value ::= ID',
        'value ::= function',
        'value ::= OPENP expr CLOSEP',
        'value ::= variable INSTANCEOF ns1',
        'value ::= variable INSTANCEOF variable',
        'value ::= SINGLEQUOTESTRING',
        'value ::= doublequoted_with_quotes',
        'value ::= varindexed DOUBLECOLON static_class_access',
        'value ::= smartytag',
        'value ::= value modifierlist',
        'value ::= NAMESPACE',
        'value ::= arraydef',
        'value ::= ns1 DOUBLECOLON static_class_access',
        'ns1 ::= ID',
        'ns1 ::= NAMESPACE',
        'variable ::= DOLLARID',
        'variable ::= varindexed',
        'variable ::= varvar AT ID',
        'variable ::= object',
        'variable ::= HATCH ID HATCH',
        'variable ::= HATCH ID HATCH arrayindex',
        'variable ::= HATCH variable HATCH',
        'variable ::= HATCH variable HATCH arrayindex',
        'varindexed ::= DOLLARID arrayindex',
        'varindexed ::= varvar arrayindex',
        'arrayindex ::= arrayindex indexdef',
        'arrayindex ::=',
        'indexdef ::= DOT DOLLARID',
        'indexdef ::= DOT varvar',
        'indexdef ::= DOT varvar AT ID',
        'indexdef ::= DOT ID',
        'indexdef ::= DOT INTEGER',
        'indexdef ::= DOT LDEL expr RDEL',
        'indexdef ::= OPENB ID CLOSEB',
        'indexdef ::= OPENB ID DOT ID CLOSEB',
        'indexdef ::= OPENB SINGLEQUOTESTRING CLOSEB',
        'indexdef ::= OPENB INTEGER CLOSEB',
        'indexdef ::= OPENB DOLLARID CLOSEB',
        'indexdef ::= OPENB variable CLOSEB',
        'indexdef ::= OPENB value CLOSEB',
        'indexdef ::= OPENB expr CLOSEB',
        'indexdef ::= OPENB CLOSEB',
        'varvar ::= DOLLARID',
        'varvar ::= DOLLAR',
        'varvar ::= varvar varvarele',
        'varvarele ::= ID',
        'varvarele ::= SIMPELOUTPUT',
        'varvarele ::= LDEL expr RDEL',
        'object ::= varindexed objectchain',
        'objectchain ::= objectelement',
        'objectchain ::= objectchain objectelement',
        'objectelement ::= PTR ID arrayindex',
        'objectelement ::= PTR varvar arrayindex',
        'objectelement ::= PTR LDEL expr RDEL arrayindex',
        'objectelement ::= PTR ID LDEL expr RDEL arrayindex',
        'objectelement ::= PTR method',
        'function ::= ns1 OPENP params CLOSEP',
        'method ::= ID OPENP params CLOSEP',
        'method ::= DOLLARID OPENP params CLOSEP',
        'params ::= params COMMA expr',
        'params ::= expr',
        'params ::=',
        'modifierlist ::= modifierlist modifier modparameters',
        'modifierlist ::= modifier modparameters',
        'modifier ::= VERT AT ID',
        'modifier ::= VERT ID',
        'modparameters ::= modparameters modparameter',
        'modparameters ::=',
        'modparameter ::= COLON value',
        'modparameter ::= COLON UNIMATH value',
        'modparameter ::= COLON array',
        'static_class_access ::= method',
        'static_class_access ::= method objectchain',
        'static_class_access ::= ID',
        'static_class_access ::= DOLLARID arrayindex',
        'static_class_access ::= DOLLARID arrayindex objectchain',
        'lop ::= LOGOP',
        'lop ::= SLOGOP',
        'tlop ::= TLOGOP',
        'scond ::= SINGLECOND',
        'arraydef ::= OPENB arrayelements CLOSEB',
        'arraydef ::= ARRAYOPEN arrayelements CLOSEP',
        'arrayelements ::= arrayelement',
        'arrayelements ::= arrayelements COMMA arrayelement',
        'arrayelements ::=',
        'arrayelement ::= value APTR expr',
        'arrayelement ::= ID APTR expr',
        'arrayelement ::= expr',
        'doublequoted_with_quotes ::= QUOTE QUOTE',
        'doublequoted_with_quotes ::= QUOTE doublequoted QUOTE',
        'doublequoted ::= doublequoted doublequotedcontent',
        'doublequoted ::= doublequotedcontent',
        'doublequotedcontent ::= BACKTICK variable BACKTICK',
        'doublequotedcontent ::= BACKTICK expr BACKTICK',
        'doublequotedcontent ::= DOLLARID',
        'doublequotedcontent ::= LDEL variable RDEL',
        'doublequotedcontent ::= LDEL expr RDEL',
        'doublequotedcontent ::= smartytag',
        'doublequotedcontent ::= TEXT',
    );

    public static $yyRuleInfo       = array(
        array(0 => 63, 1 => 1),
        array(0 => 64, 1 => 2),
        array(0 => 64, 1 => 2),
        array(0 => 64, 1 => 2),
        array(0 => 64, 1 => 2),
        array(0 => 64, 1 => 4),
        array(0 => 65, 1 => 4),
        array(0 => 65, 1 => 1),
        array(0 => 66, 1 => 2),
        array(0 => 66, 1 => 0),
        array(0 => 64, 1 => 2),
        array(0 => 64, 1 => 0),
        array(0 => 67, 1 => 1),
        array(0 => 67, 1 => 1),
        array(0 => 67, 1 => 1),
        array(0 => 67, 1 => 3),
        array(0 => 67, 1 => 2),
        array(0 => 68, 1 => 1),
        array(0 => 68, 1 => 2),
        array(0 => 68, 1 => 2),
        array(0 => 71, 1 => 2),
        array(0 => 70, 1 => 2),
        array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 1),
        array(0 => 73, 1 => 1),
        array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 4),
        array(0 => 69, 1 => 5),
        array(0 => 69, 1 => 6),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 8),
        array(0 => 81, 1 => 2),
        array(0 => 81, 1 => 1),
        array(0 => 69, 1 => 5),
        array(0 => 69, 1 => 7),
        array(0 => 69, 1 => 6),
        array(0 => 69, 1 => 8),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 4),
        array(0 => 67, 1 => 1),
        array(0 => 69, 1 => 2),
        array(0 => 69, 1 => 3),
        array(0 => 69, 1 => 4),
        array(0 => 69, 1 => 5),
        array(0 => 74, 1 => 2),
        array(0 => 74, 1 => 1),
        array(0 => 74, 1 => 0),
        array(0 => 84, 1 => 4),
        array(0 => 84, 1 => 2),
        array(0 => 84, 1 => 2),
        array(0 => 84, 1 => 2),
        array(0 => 84, 1 => 2),
        array(0 => 84, 1 => 2),
        array(0 => 84, 1 => 4),
        array(0 => 80, 1 => 1),
        array(0 => 80, 1 => 3),
        array(0 => 79, 1 => 3),
        array(0 => 79, 1 => 3),
        array(0 => 79, 1 => 3),
        array(0 => 79, 1 => 3),
        array(0 => 77, 1 => 1),
        array(0 => 77, 1 => 1),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 2),
        array(0 => 77, 1 => 3),
        array(0 => 77, 1 => 3),
        array(0 => 85, 1 => 7),
        array(0 => 85, 1 => 7),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 3),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 2),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 1),
        array(0 => 76, 1 => 3),
        array(0 => 91, 1 => 1),
        array(0 => 91, 1 => 1),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 1),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 4),
        array(0 => 75, 1 => 3),
        array(0 => 75, 1 => 4),
        array(0 => 72, 1 => 2),
        array(0 => 72, 1 => 2),
        array(0 => 96, 1 => 2),
        array(0 => 96, 1 => 0),
        array(0 => 97, 1 => 2),
        array(0 => 97, 1 => 2),
        array(0 => 97, 1 => 4),
        array(0 => 97, 1 => 2),
        array(0 => 97, 1 => 2),
        array(0 => 97, 1 => 4),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 5),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 3),
        array(0 => 97, 1 => 2),
        array(0 => 82, 1 => 1),
        array(0 => 82, 1 => 1),
        array(0 => 82, 1 => 2),
        array(0 => 98, 1 => 1),
        array(0 => 98, 1 => 1),
        array(0 => 98, 1 => 3),
        array(0 => 95, 1 => 2),
        array(0 => 99, 1 => 1),
        array(0 => 99, 1 => 2),
        array(0 => 100, 1 => 3),
        array(0 => 100, 1 => 3),
        array(0 => 100, 1 => 5),
        array(0 => 100, 1 => 6),
        array(0 => 100, 1 => 2),
        array(0 => 90, 1 => 4),
        array(0 => 101, 1 => 4),
        array(0 => 101, 1 => 4),
        array(0 => 102, 1 => 3),
        array(0 => 102, 1 => 1),
        array(0 => 102, 1 => 0),
        array(0 => 78, 1 => 3),
        array(0 => 78, 1 => 2),
        array(0 => 103, 1 => 3),
        array(0 => 103, 1 => 2),
        array(0 => 83, 1 => 2),
        array(0 => 83, 1 => 0),
        array(0 => 104, 1 => 2),
        array(0 => 104, 1 => 3),
        array(0 => 104, 1 => 2),
        array(0 => 93, 1 => 1),
        array(0 => 93, 1 => 2),
        array(0 => 93, 1 => 1),
        array(0 => 93, 1 => 2),
        array(0 => 93, 1 => 3),
        array(0 => 87, 1 => 1),
        array(0 => 87, 1 => 1),
        array(0 => 86, 1 => 1),
        array(0 => 88, 1 => 1),
        array(0 => 94, 1 => 3),
        array(0 => 94, 1 => 3),
        array(0 => 105, 1 => 1),
        array(0 => 105, 1 => 3),
        array(0 => 105, 1 => 0),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 3),
        array(0 => 106, 1 => 1),
        array(0 => 92, 1 => 2),
        array(0 => 92, 1 => 3),
        array(0 => 107, 1 => 2),
        array(0 => 107, 1 => 1),
        array(0 => 108, 1 => 3),
        array(0 => 108, 1 => 3),
        array(0 => 108, 1 => 1),
        array(0 => 108, 1 => 3),
        array(0 => 108, 1 => 3),
        array(0 => 108, 1 => 1),
        array(0 => 108, 1 => 1),
    );

    public static $yyReduceMap      = array(
        0   => 0,
        1   => 1,
        2   => 2,
        3   => 3,
        4   => 4,
        5   => 5,
        6   => 6,
        7   => 7,
        22  => 7,
        23  => 7,
        24  => 7,
        37  => 7,
        57  => 7,
        58  => 7,
        66  => 7,
        67  => 7,
        78  => 7,
        83  => 7,
        84  => 7,
        89  => 7,
        93  => 7,
        94  => 7,
        98  => 7,
        99  => 7,
        101 => 7,
        106 => 7,
        170 => 7,
        175 => 7,
        8   => 8,
        9   => 9,
        10  => 10,
        12  => 12,
        13  => 13,
        14  => 14,
        15  => 15,
        16  => 16,
        17  => 17,
        18  => 18,
        19  => 19,
        20  => 20,
        21  => 21,
        25  => 25,
        26  => 26,
        27  => 27,
        28  => 28,
        29  => 29,
        30  => 30,
        31  => 31,
        32  => 32,
        34  => 32,
        33  => 33,
        35  => 35,
        36  => 36,
        38  => 38,
        39  => 39,
        40  => 40,
        41  => 41,
        42  => 42,
        43  => 43,
        44  => 44,
        45  => 45,
        46  => 46,
        47  => 47,
        48  => 48,
        49  => 49,
        50  => 50,
        51  => 51,
        60  => 51,
        148 => 51,
        152 => 51,
        156 => 51,
        158 => 51,
        52  => 52,
        149 => 52,
        155 => 52,
        53  => 53,
        54  => 54,
        55  => 54,
        56  => 56,
        133 => 56,
        59  => 59,
        61  => 61,
        62  => 62,
        63  => 62,
        64  => 64,
        65  => 65,
        68  => 68,
        69  => 69,
        70  => 69,
        71  => 71,
        72  => 72,
        73  => 73,
        74  => 74,
        75  => 75,
        76  => 76,
        77  => 77,
        79  => 79,
        81  => 79,
        82  => 79,
        113 => 79,
        80  => 80,
        85  => 85,
        86  => 86,
        87  => 87,
        88  => 88,
        90  => 90,
        91  => 91,
        92  => 91,
        95  => 95,
        96  => 96,
        97  => 97,
        100 => 100,
        102 => 102,
        103 => 103,
        104 => 104,
        105 => 105,
        107 => 107,
        108 => 108,
        109 => 109,
        110 => 110,
        111 => 111,
        112 => 112,
        114 => 114,
        172 => 114,
        115 => 115,
        116 => 116,
        117 => 117,
        118 => 118,
        119 => 119,
        120 => 120,
        128 => 120,
        121 => 121,
        122 => 122,
        123 => 123,
        124 => 123,
        126 => 123,
        127 => 123,
        125 => 125,
        129 => 129,
        130 => 130,
        131 => 131,
        176 => 131,
        132 => 132,
        134 => 134,
        135 => 135,
        136 => 136,
        137 => 137,
        138 => 138,
        139 => 139,
        140 => 140,
        141 => 141,
        142 => 142,
        143 => 143,
        144 => 144,
        145 => 145,
        146 => 146,
        147 => 147,
        150 => 150,
        151 => 151,
        153 => 153,
        154 => 154,
        157 => 157,
        159 => 159,
        160 => 160,
        161 => 161,
        162 => 162,
        163 => 163,
        164 => 164,
        165 => 165,
        166 => 166,
        167 => 167,
        168 => 168,
        169 => 168,
        171 => 171,
        173 => 173,
        174 => 174,
        177 => 177,
        178 => 178,
        179 => 179,
        180 => 180,
        183 => 180,
        181 => 181,
        184 => 181,
        182 => 182,
        185 => 185,
        186 => 186,
    );

    /**
     * result status
     *
     * @var bool
     */
    public $successful = true;

    /**
     * return value
     *
     * @var mixed
     */
    public $retvalue = 0;

    /**
     * @var
     */
    public $yymajor;

    /**
     * last index of array variable
     *
     * @var mixed
     */
    public $last_index;

    /**
     * last variable name
     *
     * @var string
     */
    public $last_variable;

    /**
     * root parse tree buffer
     *
     * @var Smarty_Internal_ParseTree_Template
     */
    public $root_buffer;

    /**
     * current parse tree object
     *
     * @var Smarty_Internal_ParseTree
     */
    public $current_buffer;

    /**
     * lexer object
     *
     * @var Smarty_Internal_Templatelexer
     */
    public $lex;

    /**
     * {strip} status
     *
     * @var bool
     */
    public $strip = false;

    /**
     * compiler object
     *
     * @var Smarty_Internal_TemplateCompilerBase
     */
    public $compiler = null;

    /**
     * smarty object
     *
     * @var Smarty
     */
    public $smarty = null;

    /**
     * template object
     *
     * @var Smarty_Internal_Template
     */
    public $template = null;

    /**
     * block nesting level
     *
     * @var int
     */
    public $block_nesting_level = 0;

    /**
     * security object
     *
     * @var Smarty_Security
     */
    public $security = null;

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_prefix = array();

    /**
     * template prefix array
     *
     * @var \Smarty_Internal_ParseTree[]
     */
    public $template_postfix = array();

    public $yyTraceFILE;

    public $yyTracePrompt;

    public $yyidx;

    public $yyerrcnt;

    public $yystack          = array();

    public $yyTokenName      = array(
        '$', 'VERT', 'COLON', 'PHP',
        'TEXT', 'STRIPON', 'STRIPOFF', 'LITERALSTART',
        'LITERALEND', 'LITERAL', 'SIMPELOUTPUT', 'SIMPLETAG',
        'SMARTYBLOCKCHILDPARENT', 'LDEL', 'RDEL', 'DOLLARID',
        'EQUAL', 'ID', 'PTR', 'LDELMAKENOCACHE',
        'LDELIF', 'LDELFOR', 'SEMICOLON', 'INCDEC',
        'TO', 'STEP', 'LDELFOREACH', 'SPACE',
        'AS', 'APTR', 'LDELSETFILTER', 'CLOSETAG',
        'LDELSLASH', 'ATTR', 'INTEGER', 'COMMA',
        'OPENP', 'CLOSEP', 'MATH', 'UNIMATH',
        'ISIN', 'QMARK', 'NOT', 'TYPECAST',
        'HEX', 'DOT', 'INSTANCEOF', 'SINGLEQUOTESTRING',
        'DOUBLECOLON', 'NAMESPACE', 'AT', 'HATCH',
        'OPENB', 'CLOSEB', 'DOLLAR', 'LOGOP',
        'SLOGOP', 'TLOGOP', 'SINGLECOND', 'ARRAYOPEN',
        'QUOTE', 'BACKTICK', 'error', 'start',
        'template', 'literal_e2', 'literal_e1', 'smartytag',
        'tagbody', 'tag', 'outattr', 'eqoutattr',
        'varindexed', 'output', 'attributes', 'variable',
        'value', 'expr', 'modifierlist', 'statement',
        'statements', 'foraction', 'varvar', 'modparameters',
        'attribute', 'ternary', 'tlop', 'lop',
        'scond', 'array', 'function', 'ns1',
        'doublequoted_with_quotes', 'static_class_access', 'arraydef', 'object',
        'arrayindex', 'indexdef', 'varvarele', 'objectchain',
        'objectelement', 'method', 'params', 'modifier',
        'modparameter', 'arrayelements', 'arrayelement', 'doublequoted',
        'doublequotedcontent',
    );

    /**
     * internal error flag
     *
     * @var bool
     */
    private $internalError = false;                    /* Index of top element in stack */
    private $_retvalue;                 /* Shifts left before out of the error */
    /**
     * constructor
     *
     * @param Smarty_Internal_Templatelexer        $lex
     * @param Smarty_Internal_TemplateCompilerBase $compiler
     */
    public function __construct(Smarty_Internal_Templatelexer $lex, Smarty_Internal_TemplateCompilerBase $compiler)
    {
        $this->lex = $lex;
        $this->compiler = $compiler;
        $this->template = $this->compiler->template;
        $this->smarty = $this->template->smarty;
        $this->security = isset($this->smarty->security_policy) ? $this->smarty->security_policy : false;
        $this->current_buffer = $this->root_buffer = new Smarty_Internal_ParseTree_Template();
    }  /* The parser's stack */
    public static function yy_destructor($yymajor, $yypminor)
    {
        switch ($yymajor) {
            default:
                break;   /* If no destructor action specified: do nothing */
        }
    }

    /**
     * insert PHP code in current buffer
     *
     * @param string $code
     */
    public function insertPhpCode($code)
    {
        $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Tag($this, $code));
    }

    /**
     * error rundown
     *
     */
    public function errorRunDown()
    {
        while ($this->yystack !== array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    /**
     *  merge PHP code with prefix code and return parse tree tag object
     *
     * @param string $code
     *
     * @return Smarty_Internal_ParseTree_Tag
     */
    public function mergePrefixCode($code)
    {
        $tmp = '';
        foreach ($this->compiler->prefix_code as $preCode) {
            $tmp .= $preCode;
        }
        $this->compiler->prefix_code = array();
        $tmp .= $code;
        return new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp, true));
    }

    public function Trace($TraceFILE, $zTracePrompt)
    {
        if (!$TraceFILE) {
            $zTracePrompt = 0;
        } elseif (!$zTracePrompt) {
            $TraceFILE = 0;
        }
        $this->yyTraceFILE = $TraceFILE;
        $this->yyTracePrompt = $zTracePrompt;
    }

    public function PrintTrace()
    {
        $this->yyTraceFILE = fopen('php://output', 'w');
        $this->yyTracePrompt = '<br>';
    }

    public function tokenName($tokenType)
    {
        if ($tokenType === 0) {
            return 'End of Input';
        }
        if ($tokenType > 0 && $tokenType < count($this->yyTokenName)) {
            return $this->yyTokenName[ $tokenType ];
        } else {
            return 'Unknown';
        }
    }

    public function yy_pop_parser_stack()
    {
        if (empty($this->yystack)) {
            return;
        }
        $yytos = array_pop($this->yystack);
        if ($this->yyTraceFILE && $this->yyidx >= 0) {
            fwrite($this->yyTraceFILE,
                $this->yyTracePrompt . 'Popping ' . $this->yyTokenName[ $yytos->major ] .
                "\n");
        }
        $yymajor = $yytos->major;
        self::yy_destructor($yymajor, $yytos->minor);
        $this->yyidx--;
        return $yymajor;
    }

    public function __destruct()
    {
        while ($this->yystack !== array()) {
            $this->yy_pop_parser_stack();
        }
        if (is_resource($this->yyTraceFILE)) {
            fclose($this->yyTraceFILE);
        }
    }

    public function yy_get_expected_tokens($token)
    {
        static $res3 = array();
        static $res4 = array();
        $state = $this->yystack[ $this->yyidx ]->stateno;
        $expected = self::$yyExpectedTokens[ $state ];
        if (isset($res3[ $state ][ $token ])) {
            if ($res3[ $state ][ $token ]) {
                return $expected;
            }
        } else {
            if ($res3[ $state ][ $token ] = in_array($token, self::$yyExpectedTokens[ $state ], true)) {
                return $expected;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return array_unique($expected);
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[ $yyruleno ][ 1 ];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[ $this->yyidx ]->stateno,
                        self::$yyRuleInfo[ $yyruleno ][ 0 ]);
                    if (isset(self::$yyExpectedTokens[ $nextstate ])) {
                        $expected = array_merge($expected, self::$yyExpectedTokens[ $nextstate ]);
                        if (isset($res4[ $nextstate ][ $token ])) {
                            if ($res4[ $nextstate ][ $token ]) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        } else {
                            if ($res4[ $nextstate ][ $token ] =
                                in_array($token, self::$yyExpectedTokens[ $nextstate ], true)) {
                                $this->yyidx = $yyidx;
                                $this->yystack = $stack;
                                return array_unique($expected);
                            }
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[ $yyruleno ][ 0 ];
                        $this->yystack[ $this->yyidx ] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return array_unique($expected);
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return $expected;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;
        return array_unique($expected);
    }

    public function yy_is_expected_token($token)
    {
        static $res = array();
        static $res2 = array();
        if ($token === 0) {
            return true; // 0 is not part of this
        }
        $state = $this->yystack[ $this->yyidx ]->stateno;
        if (isset($res[ $state ][ $token ])) {
            if ($res[ $state ][ $token ]) {
                return true;
            }
        } else {
            if ($res[ $state ][ $token ] = in_array($token, self::$yyExpectedTokens[ $state ], true)) {
                return true;
            }
        }
        $stack = $this->yystack;
        $yyidx = $this->yyidx;
        do {
            $yyact = $this->yy_find_shift_action($token);
            if ($yyact >= self::YYNSTATE && $yyact < self::YYNSTATE + self::YYNRULE) {
                // reduce action
                $done = 0;
                do {
                    if ($done++ === 100) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // too much recursion prevents proper detection
                        // so give up
                        return true;
                    }
                    $yyruleno = $yyact - self::YYNSTATE;
                    $this->yyidx -= self::$yyRuleInfo[ $yyruleno ][ 1 ];
                    $nextstate = $this->yy_find_reduce_action(
                        $this->yystack[ $this->yyidx ]->stateno,
                        self::$yyRuleInfo[ $yyruleno ][ 0 ]);
                    if (isset($res2[ $nextstate ][ $token ])) {
                        if ($res2[ $nextstate ][ $token ]) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    } else {
                        if ($res2[ $nextstate ][ $token ] =
                            (isset(self::$yyExpectedTokens[ $nextstate ]) &&
                             in_array($token, self::$yyExpectedTokens[ $nextstate ], true))) {
                            $this->yyidx = $yyidx;
                            $this->yystack = $stack;
                            return true;
                        }
                    }
                    if ($nextstate < self::YYNSTATE) {
                        // we need to shift a non-terminal
                        $this->yyidx++;
                        $x = new TP_yyStackEntry;
                        $x->stateno = $nextstate;
                        $x->major = self::$yyRuleInfo[ $yyruleno ][ 0 ];
                        $this->yystack[ $this->yyidx ] = $x;
                        continue 2;
                    } elseif ($nextstate === self::YYNSTATE + self::YYNRULE + 1) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        if (!$token) {
                            // end of input: this is valid
                            return true;
                        }
                        // the last token was just ignored, we can't accept
                        // by ignoring input, this is in essence ignoring a
                        // syntax error!
                        return false;
                    } elseif ($nextstate === self::YY_NO_ACTION) {
                        $this->yyidx = $yyidx;
                        $this->yystack = $stack;
                        // input accepted, but not shifted (I guess)
                        return true;
                    } else {
                        $yyact = $nextstate;
                    }
                } while (true);
            }
            break;
        } while (true);
        $this->yyidx = $yyidx;
        $this->yystack = $stack;
        return true;
    }

    public function yy_find_shift_action($iLookAhead)
    {
        $stateno = $this->yystack[ $this->yyidx ]->stateno;
        /* if ($this->yyidx < 0) return self::YY_NO_ACTION;  */
        if (!isset(self::$yy_shift_ofst[ $stateno ])) {
            // no shift actions
            return self::$yy_default[ $stateno ];
        }
        $i = self::$yy_shift_ofst[ $stateno ];
        if ($i === self::YY_SHIFT_USE_DFLT) {
            return self::$yy_default[ $stateno ];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[ $i ] != $iLookAhead) {
            if (count(self::$yyFallback) && $iLookAhead < count(self::$yyFallback)
                && ($iFallback = self::$yyFallback[ $iLookAhead ]) != 0) {
                if ($this->yyTraceFILE) {
                    fwrite($this->yyTraceFILE, $this->yyTracePrompt . 'FALLBACK ' .
                                               $this->yyTokenName[ $iLookAhead ] . ' => ' .
                                               $this->yyTokenName[ $iFallback ] . "\n");
                }
                return $this->yy_find_shift_action($iFallback);
            }
            return self::$yy_default[ $stateno ];
        } else {
            return self::$yy_action[ $i ];
        }
    }

    public function yy_find_reduce_action($stateno, $iLookAhead)
    {
        /* $stateno = $this->yystack[$this->yyidx]->stateno; */
        if (!isset(self::$yy_reduce_ofst[ $stateno ])) {
            return self::$yy_default[ $stateno ];
        }
        $i = self::$yy_reduce_ofst[ $stateno ];
        if ($i === self::YY_REDUCE_USE_DFLT) {
            return self::$yy_default[ $stateno ];
        }
        if ($iLookAhead === self::YYNOCODE) {
            return self::YY_NO_ACTION;
        }
        $i += $iLookAhead;
        if ($i < 0 || $i >= self::YY_SZ_ACTTAB ||
            self::$yy_lookahead[ $i ] != $iLookAhead) {
            return self::$yy_default[ $stateno ];
        } else {
            return self::$yy_action[ $i ];
        }
    }

    // line 234 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_shift($yyNewState, $yyMajor, $yypMinor)
    {
        $this->yyidx++;
        if ($this->yyidx >= self::YYSTACKDEPTH) {
            $this->yyidx--;
            if ($this->yyTraceFILE) {
                fprintf($this->yyTraceFILE, "%sStack Overflow!\n", $this->yyTracePrompt);
            }
            while ($this->yyidx >= 0) {
                $this->yy_pop_parser_stack();
            }
            // line 221 "../smarty/lexer/smarty_internal_templateparser.y"
            $this->internalError = true;
            $this->compiler->trigger_template_error('Stack overflow in template parser');
            return;
        }
        $yytos = new TP_yyStackEntry;
        $yytos->stateno = $yyNewState;
        $yytos->major = $yyMajor;
        $yytos->minor = $yypMinor;
        $this->yystack[] = $yytos;
        if ($this->yyTraceFILE && $this->yyidx > 0) {
            fprintf($this->yyTraceFILE, "%sShift %d\n", $this->yyTracePrompt,
                $yyNewState);
            fprintf($this->yyTraceFILE, "%sStack:", $this->yyTracePrompt);
            for ($i = 1; $i <= $this->yyidx; $i++) {
                fprintf($this->yyTraceFILE, " %s",
                    $this->yyTokenName[ $this->yystack[ $i ]->major ]);
            }
            fwrite($this->yyTraceFILE, "\n");
        }
    }

    // line 242 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r0()
    {
        $this->root_buffer->prepend_array($this, $this->template_prefix);
        $this->root_buffer->append_array($this, $this->template_postfix);
        $this->_retvalue = $this->root_buffer->to_smarty_php($this);
    }

    // line 251 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r1()
    {
        $code =
            $this->compiler->compileTag('private_php',
                array(array('code' => $this->yystack[ $this->yyidx + 0 ]->minor), array('type' => $this->lex->phpType)),
                array());
        if ($this->compiler->has_code && !empty($code)) {
            $tmp = '';
            foreach ($this->compiler->prefix_code as $code) {
                $tmp .= $code;
            }
            $this->compiler->prefix_code = array();
            $this->current_buffer->append_subtree($this,
                new Smarty_Internal_ParseTree_Tag($this, $this->compiler->processNocacheCode($tmp . $code, true)));
        }
    }

    // line 255 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r2()
    {
    	$text = $this->yystack[ $this->yyidx + 0 ]->minor;

	    if ((string)$text == '') {
	    	$this->current_buffer->append_subtree($this, null);
	    }

	    $this->current_buffer->append_subtree($this, new Smarty_Internal_ParseTree_Text($text, $this->strip));
    }

    // line 259 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r3()
    {
        $this->strip = true;
    }

    // line 264 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r4()
    {
        $this->strip = false;
    }

    // line 269 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r5()
    {
        $this->current_buffer->append_subtree($this,
            new Smarty_Internal_ParseTree_Text($this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 272 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r6()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -3 ]->minor . $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 276 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r7()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 281 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r8()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 285 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r9()
    {
        $this->_retvalue = '';
    }

    // line 297 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r10()
    {
        if ($this->compiler->has_code) {
            $this->current_buffer->append_subtree($this,
                $this->mergePrefixCode($this->yystack[ $this->yyidx + 0 ]->minor));
        }
        $this->compiler->has_variable_string = false;
        $this->block_nesting_level = count($this->compiler->_tag_stack);
    }

    // line 307 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r12()
    {
        $var =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' $');
        if (preg_match('/^(.*)(\s+nocache)$/', $var, $match)) {
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array('nocache'),
                    array('value' => $this->compiler->compileVariable('\'' . $match[ 1 ] . '\'')));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array(),
                    array('value' => $this->compiler->compileVariable('\'' . $var . '\'')));
        }
    }

    // line 328 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r13()
    {
        $tag =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()));
        if ($tag == 'strip') {
            $this->strip = true;
            $this->_retvalue = null;
        } else {
            if (defined($tag)) {
                if ($this->security) {
                    $this->security->isTrustedConstant($tag, $this->compiler);
                }
                $this->_retvalue =
                    $this->compiler->compileTag('private_print_expression', array(), array('value' => $tag));
            } else {
                if (preg_match('/^(.*)(\s+nocache)$/', $tag, $match)) {
                    $this->_retvalue = $this->compiler->compileTag($match[ 1 ], array('\'nocache\''));
                } else {
                    $this->_retvalue = $this->compiler->compileTag($tag, array());
                }
            }
        }
    }

    // line 339 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r14()
    {
        $j = strrpos($this->yystack[ $this->yyidx + 0 ]->minor, '.');
        if ($this->yystack[ $this->yyidx + 0 ]->minor[ $j + 1 ] == 'c') {
            // {$smarty.block.child}
            $this->_retvalue =
                $this->compiler->compileTag('child', array(), array($this->yystack[ $this->yyidx + 0 ]->minor));
        } else {
            // {$smarty.block.parent}
            $this->_retvalue =
                $this->compiler->compileTag('parent', array(), array($this->yystack[ $this->yyidx + 0 ]->minor));
        }
    }

    // line 343 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r15()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 347 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r16()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 356 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r17()
    {
        $this->_retvalue =
            $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ],
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]));
    }

    // line 360 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r18()
    {
        $this->_retvalue =
            $this->compiler->compileTag('assign', array_merge(array(
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]),
                array('var' => '\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'')
            ), $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ]));
    }

    // line 364 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r19()
    {
        $this->_retvalue =
            $this->compiler->compileTag('assign', array_merge(array(
                array('value' => $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ]),
                array('var' => $this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ])
            ), $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ]), array(
                'smarty_internal_index' => $this->yystack[ $this->yyidx +
                                                           -1 ]->minor[ 'smarty_internal_index' ]
            ));
    }

    // line 368 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r20()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 383 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r21()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 393 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r25()
    {
        if (defined($this->yystack[ $this->yyidx + -1 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor,
                    array('value' => $this->yystack[ $this->yyidx + -1 ]->minor));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag($this->yystack[ $this->yyidx + -1 ]->minor,
                    $this->yystack[ $this->yyidx + 0 ]->minor);
        }
    }

    // line 406 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r26()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', array(),
                    array('value' => $this->yystack[ $this->yyidx + 0 ]->minor));
        } else {
            $this->_retvalue = $this->compiler->compileTag($this->yystack[ $this->yyidx + 0 ]->minor, array());
        }
    }

    // line 418 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r27()
    {
        if (defined($this->yystack[ $this->yyidx + -2 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + -2 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                $this->compiler->compileTag('private_print_expression', $this->yystack[ $this->yyidx + 0 ]->minor,
                    array(
                        'value'        => $this->yystack[ $this->yyidx + -2 ]->minor,
                        'modifierlist' => $this->yystack[ $this->yyidx + -1 ]->minor
                    ));
        } else {
            $this->_retvalue =
                $this->compiler->compileTag($this->yystack[ $this->yyidx + -2 ]->minor,
                    $this->yystack[ $this->yyidx + 0 ]->minor,
                    array('modifierlist' => $this->yystack[ $this->yyidx + -1 ]->minor));
        }
    }

    // line 423 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r28()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -3 ]->minor,
                $this->yystack[ $this->yyidx + 0 ]->minor,
                array('object_method' => $this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 428 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r29()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -4 ]->minor,
                $this->yystack[ $this->yyidx + 0 ]->minor, array(
                    'modifierlist'  => $this->yystack[ $this->yyidx + -1 ]->minor,
                    'object_method' => $this->yystack[ $this->yyidx + -2 ]->minor
                ));
    }

    // line 433 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r30()
    {
        $this->_retvalue =
            $this->compiler->compileTag('make_nocache',
                array(array('var' => '\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'')));
    }

    // line 438 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r31()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag, array(),
                array('if condition' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 443 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r32()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -2 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag,
                $this->yystack[ $this->yyidx + 0 ]->minor,
                array('if condition' => $this->yystack[ $this->yyidx + -1 ]->minor));
    }

    // line 454 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r33()
    {
        $tag = trim(substr($this->yystack[ $this->yyidx + -1 ]->minor, $this->compiler->getLdelLength()));
        $this->_retvalue =
            $this->compiler->compileTag(($tag === 'else if') ? 'elseif' : $tag, array(),
                array('if condition' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 458 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r35()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -6 ]->minor),
                array('ifexp' => $this->yystack[ $this->yyidx + -4 ]->minor),
                array('var' => $this->yystack[ $this->yyidx + -2 ]->minor),
                array('step' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 1);
    }

    // line 466 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r36()
    {
        $this->_retvalue = '=' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 470 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r38()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('to' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 0);
    }

    // line 475 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r39()
    {
        $this->_retvalue =
            $this->compiler->compileTag('for', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('start' => $this->yystack[ $this->yyidx + -5 ]->minor),
                array('to' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('step' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )), 0);
    }

    // line 479 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r40()
    {
        $this->_retvalue =
            $this->compiler->compileTag('foreach', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('from' => $this->yystack[ $this->yyidx + -3 ]->minor),
                array('item' => $this->yystack[ $this->yyidx + -1 ]->minor)
            )));
    }

    // line 482 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r41()
    {
        $this->_retvalue =
            $this->compiler->compileTag('foreach', array_merge($this->yystack[ $this->yyidx + 0 ]->minor, array(
                array('from' => $this->yystack[ $this->yyidx + -5 ]->minor),
                array('item' => $this->yystack[ $this->yyidx + -1 ]->minor),
                array('key' => $this->yystack[ $this->yyidx + -3 ]->minor)
            )));
    }

    // line 487 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r42()
    {
        $this->_retvalue = $this->compiler->compileTag('foreach', $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 491 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r43()
    {
        $this->_retvalue =
            $this->compiler->compileTag('setfilter', array(), array(
                'modifier_list' => array(
                    array_merge(array($this->yystack[ $this->yyidx + -1 ]->minor),
                        $this->yystack[ $this->yyidx + 0 ]->minor)
                )
            ));
    }

    // line 497 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r44()
    {
        $this->_retvalue =
            $this->compiler->compileTag('setfilter', array(), array(
                'modifier_list' => array_merge(array(
                    array_merge(array(
                        $this->yystack[ $this->yyidx +
                                        -2 ]->minor
                    ), $this->yystack[ $this->yyidx + -1 ]->minor)
                ), $this->yystack[ $this->yyidx + 0 ]->minor)
            ));
    }

    // line 506 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r45()
    {
        $tag =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' /');
        if ($tag === 'strip') {
            $this->strip = false;
            $this->_retvalue = null;
        } else {
            $this->_retvalue = $this->compiler->compileTag($tag . 'close', array());
        }
    }

    // line 510 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r46()
    {
        $this->_retvalue = $this->compiler->compileTag($this->yystack[ $this->yyidx + 0 ]->minor . 'close', array());
    }

    // line 515 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r47()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -1 ]->minor . 'close', array(),
                array('modifier_list' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 519 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r48()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -2 ]->minor . 'close', array(),
                array('object_method' => $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 527 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r49()
    {
        $this->_retvalue =
            $this->compiler->compileTag($this->yystack[ $this->yyidx + -3 ]->minor . 'close', array(), array(
                'object_method' => $this->yystack[ $this->yyidx + -1 ]->minor,
                'modifier_list' => $this->yystack[ $this->yyidx + 0 ]->minor
            ));
    }

    // line 533 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r50()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
        $this->_retvalue[] = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 538 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r51()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 543 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r52()
    {
        $this->_retvalue = array();
    }

    // line 554 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r53()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue =
                array($this->yystack[ $this->yyidx + -2 ]->minor => $this->yystack[ $this->yyidx + 0 ]->minor);
        } else {
            $this->_retvalue =
                array(
                    $this->yystack[ $this->yyidx + -2 ]->minor => '\'' .
                                                                  $this->yystack[ $this->yyidx + 0 ]->minor .
                                                                  '\''
                );
        }
    }

    // line 562 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r54()
    {
        $this->_retvalue =
            array(
                trim($this->yystack[ $this->yyidx + -1 ]->minor, " =\n\r\t") => $this->yystack[ $this->yyidx +
                                                                                                0 ]->minor
            );
    }

    // line 574 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r56()
    {
        $this->_retvalue = '\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\'';
    }

    // line 587 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r59()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -2 ]->minor => $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 592 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r61()
    {
        $this->yystack[ $this->yyidx + -2 ]->minor[] = $this->yystack[ $this->yyidx + 0 ]->minor;
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor;
    }

    // line 599 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r62()
    {
        $this->_retvalue =
            array(
                'var'   => '\'' . substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) . '\'',
                'value' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 603 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r64()
    {
        $this->_retvalue =
            array(
                'var'   => $this->yystack[ $this->yyidx + -2 ]->minor,
                'value' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 623 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r65()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 628 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r68()
    {
        $this->_retvalue =
            '$_smarty_tpl->getStreamVariable(\'' .
            substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) .
            '://' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '\')';
    }

    // line 638 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r69()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            trim($this->yystack[ $this->yyidx + -1 ]->minor) .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 642 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r71()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -1 ]->minor[ 'pre' ] .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor[ 'op' ] .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 646 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r72()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 650 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r73()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 654 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r74()
    {
        $this->_retvalue =
            'in_array(' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ',' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 662 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r75()
    {
        $this->_retvalue =
            'in_array(' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ',(array)' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ')';
    }

    // line 666 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r76()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -5 ]->minor .
            ' ? ' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + -2 ]->minor, 1) . '\'') .
            ' : ' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 676 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r77()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -5 ]->minor .
            ' ? ' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ' : ' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 681 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r79()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 702 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r80()
    {
        $this->_retvalue = '!' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 706 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r85()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor . '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 710 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r86()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . '.';
    }

    // line 715 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r87()
    {
        $this->_retvalue = '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 732 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r88()
    {
        if (defined($this->yystack[ $this->yyidx + 0 ]->minor)) {
            if ($this->security) {
                $this->security->isTrustedConstant($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler);
            }
            $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
        } else {
            $this->_retvalue = '\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\'';
        }
    }

    // line 736 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r90()
    {
        $this->_retvalue = '(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 754 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r91()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + -1 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 765 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r95()
    {
        if ($this->security && $this->security->static_classes !== array()) {
            $this->compiler->trigger_template_error('dynamic static class not allowed by security setting');
        }

        $prefixVar = $this->compiler->getNewPrefixVariable();
        if ($this->yystack[ $this->yyidx + -2 ]->minor[ 'var' ] === '\'smarty\'') {
            $this->compiler->appendPrefixCode("<?php {$prefixVar} = " .
                                              $this->compiler->compileTag('private_special_variable', array(),
                                                  $this->yystack[ $this->yyidx +
                                                                  -2 ]->minor[ 'smarty_internal_index' ]) .
                                              ';?>');
        } else {
            $this->compiler->appendPrefixCode("<?php  {$prefixVar} = " .
                                              $this->compiler->compileVariable($this->yystack[ $this->yyidx +
                                                                                               -2 ]->minor[ 'var' ]) .
                                              $this->yystack[ $this->yyidx + -2 ]->minor[ 'smarty_internal_index' ] .
                                              ';?>');
        }
        $this->_retvalue =
            $prefixVar .
            '::' .
            $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
            $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
    }

    // line 772 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r96()
    {
        $prefixVar = $this->compiler->getNewPrefixVariable();
        $tmp = $this->compiler->appendCode('<?php ob_start();?>', $this->yystack[ $this->yyidx + 0 ]->minor);
        $this->compiler->appendPrefixCode($this->compiler->appendCode($tmp, "<?php {$prefixVar} = ob_get_clean();?>"));
        $this->_retvalue = $prefixVar;
    }

    // line 785 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r97()
    {
        $this->_retvalue =
            $this->compiler->compileTag('private_modifier', array(), array(
                'value'        => $this->yystack[ $this->yyidx + -1 ]->minor,
                'modifierlist' => $this->yystack[ $this->yyidx + 0 ]->minor
            ));
    }

    // line 804 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r100()
    {
        if (!in_array(strtolower($this->yystack[ $this->yyidx + -2 ]->minor), array('self', 'parent')) &&
            (!$this->security ||
             $this->security->isTrustedStaticClassAccess($this->yystack[ $this->yyidx + -2 ]->minor,
                 $this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler))) {
            if (isset($this->smarty->registered_classes[ $this->yystack[ $this->yyidx + -2 ]->minor ])) {
                $this->_retvalue =
                    $this->smarty->registered_classes[ $this->yystack[ $this->yyidx + -2 ]->minor ] .
                    '::' .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
            } else {
                $this->_retvalue =
                    $this->yystack[ $this->yyidx + -2 ]->minor .
                    '::' .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 0 ] .
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 1 ];
            }
        } else {
            $this->compiler->trigger_template_error('static class \'' .
                                                    $this->yystack[ $this->yyidx + -2 ]->minor .
                                                    '\' is undefined or not allowed by security setting');
        }
    }

    // line 815 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r102()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 818 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r103()
    {
        $this->_retvalue =
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'');
    }

    // line 831 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r104()
    {
        if ($this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ] === '\'smarty\'') {
            $smarty_var =
                $this->compiler->compileTag('private_special_variable', array(),
                    $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ]);
            $this->_retvalue = $smarty_var;
        } else {
            // used for array reset,next,prev,end,current
            $this->last_variable = $this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ];
            $this->last_index = $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ];
            $this->_retvalue =
                $this->compiler->compileVariable($this->yystack[ $this->yyidx + 0 ]->minor[ 'var' ]) .
                $this->yystack[ $this->yyidx + 0 ]->minor[ 'smarty_internal_index' ];
        }
    }

    // line 841 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r105()
    {
        $this->_retvalue =
            '$_smarty_tpl->tpl_vars[' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            ']->' .
            $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 845 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r107()
    {
        $this->_retvalue =
            $this->compiler->compileConfigVariable('\'' . $this->yystack[ $this->yyidx + -1 ]->minor . '\'');
    }

    // line 849 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r108()
    {
        $this->_retvalue =
            '(is_array($tmp = ' .
            $this->compiler->compileConfigVariable('\'' . $this->yystack[ $this->yyidx + -2 ]->minor . '\'') .
            ') ? $tmp' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ' :null)';
    }

    // line 853 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r109()
    {
        $this->_retvalue = $this->compiler->compileConfigVariable($this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 857 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r110()
    {
        $this->_retvalue =
            '(is_array($tmp = ' .
            $this->compiler->compileConfigVariable($this->yystack[ $this->yyidx + -2 ]->minor) .
            ') ? $tmp' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ' : null)';
    }

    // line 860 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r111()
    {
        $this->_retvalue =
            array(
                'var'                   => '\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'',
                'smarty_internal_index' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 873 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r112()
    {
        $this->_retvalue =
            array(
                'var'                   => $this->yystack[ $this->yyidx + -1 ]->minor,
                'smarty_internal_index' => $this->yystack[ $this->yyidx + 0 ]->minor
            );
    }

    // line 879 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r114()
    {
        return;
    }

    // line 882 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r115()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'') .
            ']';
    }

    // line 886 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r116()
    {
        $this->_retvalue = '[' . $this->compiler->compileVariable($this->yystack[ $this->yyidx + 0 ]->minor) . ']';
    }

    // line 890 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r117()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable($this->yystack[ $this->yyidx + -2 ]->minor) .
            '->' .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            ']';
    }

    // line 894 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r118()
    {
        $this->_retvalue = '[\'' . $this->yystack[ $this->yyidx + 0 ]->minor . '\']';
    }

    // line 899 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r119()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + 0 ]->minor . ']';
    }

    // line 904 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r120()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + -1 ]->minor . ']';
    }

    // line 908 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r121()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileTag('private_special_variable', array(),
                '[\'section\'][\'' . $this->yystack[ $this->yyidx + -1 ]->minor . '\'][\'index\']') .
            ']';
    }

    // line 911 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r122()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileTag('private_special_variable', array(), '[\'section\'][\'' .
                                                                             $this->yystack[ $this->yyidx +
                                                                                             -3 ]->minor .
                                                                             '\'][\'' .
                                                                             $this->yystack[ $this->yyidx +
                                                                                             -1 ]->minor .
                                                                             '\']') .
            ']';
    }

    // line 917 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r123()
    {
        $this->_retvalue = '[' . $this->yystack[ $this->yyidx + -1 ]->minor . ']';
    }

    // line 933 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r125()
    {
        $this->_retvalue =
            '[' .
            $this->compiler->compileVariable('\'' . substr($this->yystack[ $this->yyidx + -1 ]->minor, 1) . '\'') .
            ']';
    }

    // line 943 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r129()
    {
        $this->_retvalue = '[]';
    }

    // line 947 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r130()
    {
        $this->_retvalue = '\'' . substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) . '\'';
    }

    // line 952 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r131()
    {
        $this->_retvalue = '\'\'';
    }

    // line 960 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r132()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . '.' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 966 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r134()
    {
        $var =
            trim(substr($this->yystack[ $this->yyidx + 0 ]->minor, $this->compiler->getLdelLength(),
                -$this->compiler->getRdelLength()), ' $');
        $this->_retvalue = $this->compiler->compileVariable('\'' . $var . '\'');
    }

    // line 973 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r135()
    {
        $this->_retvalue = '(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 982 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r136()
    {
        if ($this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ] === '\'smarty\'') {
            $this->_retvalue =
                $this->compiler->compileTag('private_special_variable', array(),
                    $this->yystack[ $this->yyidx + -1 ]->minor[ 'smarty_internal_index' ]) .
                $this->yystack[ $this->yyidx + 0 ]->minor;
        } else {
            $this->_retvalue =
                $this->compiler->compileVariable($this->yystack[ $this->yyidx + -1 ]->minor[ 'var' ]) .
                $this->yystack[ $this->yyidx + -1 ]->minor[ 'smarty_internal_index' ] .
                $this->yystack[ $this->yyidx + 0 ]->minor;
        }
    }

    // line 987 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r137()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 992 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r138()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 999 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r139()
    {
        if ($this->security && substr($this->yystack[ $this->yyidx + -1 ]->minor, 0, 1) === '_') {
            $this->compiler->trigger_template_error(self::ERR1);
        }
        $this->_retvalue =
            '->' . $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1006 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r140()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{' .
            $this->compiler->compileVariable($this->yystack[ $this->yyidx + -1 ]->minor) .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '}';
    }

    // line 1013 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r141()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{' . $this->yystack[ $this->yyidx + -2 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor . '}';
    }

    // line 1021 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r142()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $this->_retvalue =
            '->{\'' .
            $this->yystack[ $this->yyidx + -4 ]->minor .
            '\'.' .
            $this->yystack[ $this->yyidx + -2 ]->minor .
            $this->yystack[ $this->yyidx + 0 ]->minor .
            '}';
    }

    // line 1029 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r143()
    {
        $this->_retvalue = '->' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1037 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r144()
    {
        $this->_retvalue =
            $this->compiler->compilePHPFunctionCall($this->yystack[ $this->yyidx + -3 ]->minor,
                $this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 1044 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r145()
    {
        if ($this->security && substr($this->yystack[ $this->yyidx + -3 ]->minor, 0, 1) === '_') {
            $this->compiler->trigger_template_error(self::ERR1);
        }
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -3 ]->minor .
            '(' .
            implode(',', $this->yystack[ $this->yyidx + -1 ]->minor) .
            ')';
    }

    // line 1055 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r146()
    {
        if ($this->security) {
            $this->compiler->trigger_template_error(self::ERR2);
        }
        $prefixVar = $this->compiler->getNewPrefixVariable();
        $this->compiler->appendPrefixCode("<?php {$prefixVar} = " .
                                          $this->compiler->compileVariable('\'' .
                                                                           substr($this->yystack[ $this->yyidx +
                                                                                                  -3 ]->minor, 1) .
                                                                           '\'') .
                                          ';?>');
        $this->_retvalue = $prefixVar . '(' . implode(',', $this->yystack[ $this->yyidx + -1 ]->minor) . ')';
    }

    // line 1072 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r147()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -2 ]->minor, array($this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 1076 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r150()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -2 ]->minor, array(
                array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor)
            ));
    }

    // line 1084 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r151()
    {
        $this->_retvalue =
            array(array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor));
    }

    // line 1092 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r153()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1105 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r154()
    {
        $this->_retvalue =
            array_merge($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1114 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r157()
    {
        $this->_retvalue =
            array(trim($this->yystack[ $this->yyidx + -1 ]->minor) . $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1119 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r159()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor, '', 'method');
    }

    // line 1124 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r160()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor, 'method');
    }

    // line 1129 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r161()
    {
        $this->_retvalue = array($this->yystack[ $this->yyidx + 0 ]->minor, '');
    }

    // line 1134 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r162()
    {
        $this->_retvalue =
            array($this->yystack[ $this->yyidx + -1 ]->minor, $this->yystack[ $this->yyidx + 0 ]->minor, 'property');
    }

    // line 1140 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r163()
    {
        $this->_retvalue =
            array(
                $this->yystack[ $this->yyidx + -2 ]->minor,
                $this->yystack[ $this->yyidx + -1 ]->minor . $this->yystack[ $this->yyidx + 0 ]->minor, 'property'
            );
    }

    // line 1144 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r164()
    {
        $this->_retvalue = ' ' . trim($this->yystack[ $this->yyidx + 0 ]->minor) . ' ';
    }

    // line 1163 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r165()
    {
        static $lops = array(
            'eq'  => ' == ',
            'ne'  => ' != ',
            'neq' => ' != ',
            'gt'  => ' > ',
            'ge'  => ' >= ',
            'gte' => ' >= ',
            'lt'  => ' < ',
            'le'  => ' <= ',
            'lte' => ' <= ',
            'mod' => ' % ',
            'and' => ' && ',
            'or'  => ' || ',
            'xor' => ' xor ',
        );
        $op = strtolower(preg_replace('/\s*/', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $lops[ $op ];
    }

    // line 1176 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r166()
    {
        static $tlops = array(
            'isdivby'     => array('op' => ' % ', 'pre' => '!('),
            'isnotdivby'  => array('op' => ' % ', 'pre' => '('),
            'isevenby'    => array('op' => ' / ', 'pre' => '!(1 & '),
            'isnotevenby' => array('op' => ' / ', 'pre' => '(1 & '),
            'isoddby'     => array('op' => ' / ', 'pre' => '(1 & '),
            'isnotoddby'  => array('op' => ' / ', 'pre' => '!(1 & '),
        );
        $op = strtolower(preg_replace('/\s*/', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $tlops[ $op ];
    }

    // line 1190 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r167()
    {
        static $scond = array(
            'iseven'    => '!(1 & ',
            'isnoteven' => '(1 & ',
            'isodd'     => '(1 & ',
            'isnotodd'  => '!(1 & ',
        );
        $op = strtolower(str_replace(' ', '', $this->yystack[ $this->yyidx + 0 ]->minor));
        $this->_retvalue = $scond[ $op ];
    }

    // line 1201 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r168()
    {
        $this->_retvalue = 'array(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')';
    }

    // line 1209 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r171()
    {
        $this->_retvalue = $this->yystack[ $this->yyidx + -2 ]->minor . ',' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1213 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r173()
    {
        $this->_retvalue =
            $this->yystack[ $this->yyidx + -2 ]->minor . '=>' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1229 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r174()
    {
        $this->_retvalue =
            '\'' . $this->yystack[ $this->yyidx + -2 ]->minor . '\'=>' . $this->yystack[ $this->yyidx + 0 ]->minor;
    }

    // line 1235 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r177()
    {
        $this->compiler->leaveDoubleQuote();
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor->to_smarty_php($this);
    }

    // line 1240 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r178()
    {
        $this->yystack[ $this->yyidx + -1 ]->minor->append_subtree($this, $this->yystack[ $this->yyidx + 0 ]->minor);
        $this->_retvalue = $this->yystack[ $this->yyidx + -1 ]->minor;
    }

    // line 1244 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r179()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Dq($this, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    // line 1248 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r180()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Code('(string)' . $this->yystack[ $this->yyidx + -1 ]->minor);
    }

    // line 1252 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r181()
    {
        $this->_retvalue =
            new Smarty_Internal_ParseTree_Code('(string)(' . $this->yystack[ $this->yyidx + -1 ]->minor . ')');
    }

    // line 1264 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r182()
    {
        $this->_retvalue =
            new Smarty_Internal_ParseTree_Code('(string)$_smarty_tpl->tpl_vars[\'' .
                                               substr($this->yystack[ $this->yyidx + 0 ]->minor, 1) .
                                               '\']->value');
    }

    // line 1268 "../smarty/lexer/smarty_internal_templateparser.y"
    public function yy_r185()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_Tag($this, $this->yystack[ $this->yyidx + 0 ]->minor);
    }

    public function yy_r186()
    {
        $this->_retvalue = new Smarty_Internal_ParseTree_DqContent($this->yystack[ $this->yyidx + 0 ]->minor);
    }

    public function yy_reduce($yyruleno)
    {
        if ($this->yyTraceFILE && $yyruleno >= 0
            && $yyruleno < count(self::$yyRuleName)) {
            fprintf($this->yyTraceFILE, "%sReduce (%d) [%s].\n",
                $this->yyTracePrompt, $yyruleno,
                self::$yyRuleName[ $yyruleno ]);
        }
        $this->_retvalue = $yy_lefthand_side = null;
        if (isset(self::$yyReduceMap[ $yyruleno ])) {
            // call the action
            $this->_retvalue = null;
            $this->{'yy_r' . self::$yyReduceMap[ $yyruleno ]}();
            $yy_lefthand_side = $this->_retvalue;
        }
        $yygoto = self::$yyRuleInfo[ $yyruleno ][ 0 ];
        $yysize = self::$yyRuleInfo[ $yyruleno ][ 1 ];
        $this->yyidx -= $yysize;
        for ($i = $yysize; $i; $i--) {
            // pop all of the right-hand side parameters
            array_pop($this->yystack);
        }
        $yyact = $this->yy_find_reduce_action($this->yystack[ $this->yyidx ]->stateno, $yygoto);
        if ($yyact < self::YYNSTATE) {
            if (!$this->yyTraceFILE && $yysize) {
                $this->yyidx++;
                $x = new TP_yyStackEntry;
                $x->stateno = $yyact;
                $x->major = $yygoto;
                $x->minor = $yy_lefthand_side;
                $this->yystack[ $this->yyidx ] = $x;
            } else {
                $this->yy_shift($yyact, $yygoto, $yy_lefthand_side);
            }
        } elseif ($yyact === self::YYNSTATE + self::YYNRULE + 1) {
            $this->yy_accept();
        }
    }

    public function yy_parse_failed()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sFail!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
    }

    public function yy_syntax_error($yymajor, $TOKEN)
    {
        // line 214 "../smarty/lexer/smarty_internal_templateparser.y"
        $this->internalError = true;
        $this->yymajor = $yymajor;
        $this->compiler->trigger_template_error();
    }

    public function yy_accept()
    {
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sAccept!\n", $this->yyTracePrompt);
        }
        while ($this->yyidx >= 0) {
            $this->yy_pop_parser_stack();
        }
        // line 207 "../smarty/lexer/smarty_internal_templateparser.y"
        $this->successful = !$this->internalError;
        $this->internalError = false;
        $this->retvalue = $this->_retvalue;
    }

    public function doParse($yymajor, $yytokenvalue)
    {
        $yyerrorhit = 0;   /* True if yymajor has invoked an error */
        if ($this->yyidx === null || $this->yyidx < 0) {
            $this->yyidx = 0;
            $this->yyerrcnt = -1;
            $x = new TP_yyStackEntry;
            $x->stateno = 0;
            $x->major = 0;
            $this->yystack = array();
            $this->yystack[] = $x;
        }
        $yyendofinput = ($yymajor == 0);
        if ($this->yyTraceFILE) {
            fprintf($this->yyTraceFILE, "%sInput %s\n",
                $this->yyTracePrompt, $this->yyTokenName[ $yymajor ]);
        }
        do {
            $yyact = $this->yy_find_shift_action($yymajor);
            if ($yymajor < self::YYERRORSYMBOL &&
                !$this->yy_is_expected_token($yymajor)) {
                // force a syntax error
                $yyact = self::YY_ERROR_ACTION;
            }
            if ($yyact < self::YYNSTATE) {
                $this->yy_shift($yyact, $yymajor, $yytokenvalue);
                $this->yyerrcnt--;
                if ($yyendofinput && $this->yyidx >= 0) {
                    $yymajor = 0;
                } else {
                    $yymajor = self::YYNOCODE;
                }
            } elseif ($yyact < self::YYNSTATE + self::YYNRULE) {
                $this->yy_reduce($yyact - self::YYNSTATE);
            } elseif ($yyact === self::YY_ERROR_ACTION) {
                if ($this->yyTraceFILE) {
                    fprintf($this->yyTraceFILE, "%sSyntax Error!\n",
                        $this->yyTracePrompt);
                }
                if (self::YYERRORSYMBOL) {
                    if ($this->yyerrcnt < 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $yymx = $this->yystack[ $this->yyidx ]->major;
                    if ($yymx === self::YYERRORSYMBOL || $yyerrorhit) {
                        if ($this->yyTraceFILE) {
                            fprintf($this->yyTraceFILE, "%sDiscard input token %s\n",
                                $this->yyTracePrompt, $this->yyTokenName[ $yymajor ]);
                        }
                        $this->yy_destructor($yymajor, $yytokenvalue);
                        $yymajor = self::YYNOCODE;
                    } else {
                        while ($this->yyidx >= 0 &&
                               $yymx !== self::YYERRORSYMBOL &&
                               ($yyact = $this->yy_find_shift_action(self::YYERRORSYMBOL)) >= self::YYNSTATE
                        ) {
                            $this->yy_pop_parser_stack();
                        }
                        if ($this->yyidx < 0 || $yymajor == 0) {
                            $this->yy_destructor($yymajor, $yytokenvalue);
                            $this->yy_parse_failed();
                            $yymajor = self::YYNOCODE;
                        } elseif ($yymx !== self::YYERRORSYMBOL) {
                            $u2 = 0;
                            $this->yy_shift($yyact, self::YYERRORSYMBOL, $u2);
                        }
                    }
                    $this->yyerrcnt = 3;
                    $yyerrorhit = 1;
                } else {
                    if ($this->yyerrcnt <= 0) {
                        $this->yy_syntax_error($yymajor, $yytokenvalue);
                    }
                    $this->yyerrcnt = 3;
                    $this->yy_destructor($yymajor, $yytokenvalue);
                    if ($yyendofinput) {
                        $this->yy_parse_failed();
                    }
                    $yymajor = self::YYNOCODE;
                }
            } else {
                $this->yy_accept();
                $yymajor = self::YYNOCODE;
            }
        } while ($yymajor !== self::YYNOCODE && $this->yyidx >= 0);
    }
}

