<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

require('common.php');

$fileurl = 'payment_stats.php';
$tempfile = 'payment_stats.html';
$table = $DB->table('payment_records');

// 检查付费记录表是否存在
$check_table = $DB->query("SHOW TABLES LIKE '$table'");
if ($DB->num_rows($check_table) == 0) {
    echo "<html><body>";
    echo "<h2>付费统计</h2>";
    echo "<p style='color: red;'>付费记录表不存在，请先初始化数据库。</p>";
    echo "<p><a href='payment_fix.php' style='background: #007bff; color: #fff; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>点击修复数据库</a></p>";
    echo "<p><a href='check_recommend.php'>检查推荐字段</a></p>";
    echo "<p><a href='admin.php'>返回管理首页</a></p>";
    echo "</body></html>";
    exit;
}

// 检查表结构
try {
    $test_query = $DB->query("SELECT COUNT(*) as total FROM $table");
    if (!$test_query) {
        throw new Exception("无法查询付费记录表");
    }
    $test_result = $DB->fetch_array($test_query);
    $record_count = $test_result['total'];
} catch (Exception $e) {
    echo "<html><body>";
    echo "<h2>付费统计</h2>";
    echo "<p style='color: red;'>数据库查询错误: " . $e->getMessage() . "</p>";
    echo "<p><a href='payment_fix.php'>修复数据库</a></p>";
    echo "</body></html>";
    exit;
}

if (!isset($action)) $action = 'list';

/** 付费统计列表 */
if ($action == 'list') {
    $pagetitle = '付费统计';
    
    // 筛选条件
    $payment_type = intval($_GET['type']);
    $start_date = trim($_GET['start_date']);
    $end_date = trim($_GET['end_date']);
    $keywords = addslashes(trim($_POST['keywords'] ? $_POST['keywords'] : $_GET['keywords']));
    
    $pageurl = $fileurl;
    $where_conditions = array();
    $params = array();
    
    // 构建查询条件
    if ($payment_type > 0) {
        $where_conditions[] = "payment_type = $payment_type";
        $params[] = "type=$payment_type";
    }
    
    if (!empty($start_date)) {
        $start_timestamp = strtotime($start_date . ' 00:00:00');
        if ($start_timestamp) {
            $where_conditions[] = "payment_time >= $start_timestamp";
            $params[] = "start_date=" . urlencode($start_date);
        }
    }
    
    if (!empty($end_date)) {
        $end_timestamp = strtotime($end_date . ' 23:59:59');
        if ($end_timestamp) {
            $where_conditions[] = "payment_time <= $end_timestamp";
            $params[] = "end_date=" . urlencode($end_date);
        }
    }
    
    if (!empty($keywords)) {
        $where_conditions[] = "(web_name LIKE '%$keywords%' OR web_url LIKE '%$keywords%')";
        $params[] = "keywords=" . urlencode($keywords);
    }
    
    $where = !empty($where_conditions) ? implode(' AND ', $where_conditions) : '1=1';
    
    if (!empty($params)) {
        $pageurl .= '?' . implode('&', $params);
    }
    
    // 获取总数
    $total = $DB->get_count($table, $where);
    
    // 分页
    $showpage = '';
    if ($total > $pagesize) {
        $pages = ceil($total / $pagesize);
        $showpage = showpage($pageurl, $total, $curpage, $pagesize);
    }
    
    // 获取记录列表
    $sql = "SELECT * FROM $table WHERE $where ORDER BY payment_time DESC LIMIT $start, $pagesize";
    $query = $DB->query($sql);
    $records = array();
    
    while ($row = $DB->fetch_array($query)) {
        // 格式化付费类型
        switch ($row['payment_type']) {
            case 1:
                $row['type_name'] = 'VIP';
                $row['type_color'] = '#ff6600';
                break;
            case 2:
                $row['type_name'] = '推荐';
                $row['type_color'] = '#28a745';
                break;
            case 3:
                $row['type_name'] = '快审';
                $row['type_color'] = '#007bff';
                break;
            default:
                $row['type_name'] = '未知';
                $row['type_color'] = '#666';
        }

        // 格式化时间
        $row['payment_date'] = date('Y-m-d H:i:s', $row['payment_time']);
        $row['expire_date'] = $row['expire_time'] ? date('Y-m-d H:i:s', $row['expire_time']) : '无';

        // 判断状态
        if ($row['expire_time'] > 0 && $row['expire_time'] < time()) {
            $row['status'] = 0;
            $row['status_name'] = '已过期';
            $row['status_color'] = '#dc3545';
        } else {
            $row['status_name'] = $row['status'] ? '有效' : '已过期';
            $row['status_color'] = $row['status'] ? '#28a745' : '#dc3545';
        }

        // 简化处理，不依赖价格模块
        $row['unit_price'] = '￥' . $row['payment_amount'];

        $records[] = $row;
    }
    
    // 统计数据
    $stats = array();
    $stats['total_count'] = $total;
    $amount_sql = "SELECT SUM(CAST(payment_amount AS DECIMAL(10,2))) as total FROM $table WHERE $where";
    $amount_result = $DB->fetch_one($amount_sql);
    $stats['total_amount'] = $amount_result && $amount_result['total'] ? number_format($amount_result['total'], 2) : '0.00';
    
    // 按类型统计 - 修复版本
    $type_stats = array();
    $websites_table = $DB->table('websites');

    // VIP统计
    $vip_where = ($where == '1=1') ? "payment_type = 1" : $where . " AND payment_type = 1";
    $vip_record_count = $DB->get_count($table, $vip_where);
    $vip_actual_count = $DB->get_count($websites_table, 'web_ispay = 1');
    $vip_amount_sql = "SELECT SUM(CAST(payment_amount AS DECIMAL(10,2))) as total FROM $table WHERE $vip_where";
    $vip_amount_result = $DB->fetch_one($vip_amount_sql);
    $vip_amount = $vip_amount_result && $vip_amount_result['total'] ? number_format($vip_amount_result['total'], 2) : '0.00';

    $type_stats[] = array(
        'type' => 1,
        'name' => 'VIP',
        'count' => $vip_actual_count,
        'record_count' => $vip_record_count,
        'amount' => $vip_amount
    );

    // 推荐统计 - 排除VIP+推荐的网站，只统计纯推荐网站
    $recommend_where = ($where == '1=1') ? "payment_type = 2" : $where . " AND payment_type = 2";
    $recommend_record_count = $DB->get_count($table, $recommend_where);
    // 只统计推荐但不是VIP的网站
    $recommend_actual_count = $DB->get_count($websites_table, 'web_isbest = 1 AND web_ispay = 0');
    $recommend_amount_sql = "SELECT SUM(CAST(payment_amount AS DECIMAL(10,2))) as total FROM $table WHERE $recommend_where";
    $recommend_amount_result = $DB->fetch_one($recommend_amount_sql);
    $recommend_amount = $recommend_amount_result && $recommend_amount_result['total'] ? number_format($recommend_amount_result['total'], 2) : '0.00';

    $type_stats[] = array(
        'type' => 2,
        'name' => '推荐',
        'count' => $recommend_actual_count,
        'record_count' => $recommend_record_count,
        'amount' => $recommend_amount
    );

    // 快审统计
    $fast_where = ($where == '1=1') ? "payment_type = 3" : $where . " AND payment_type = 3";
    $fast_record_count = $DB->get_count($table, $fast_where);
    $fast_amount_sql = "SELECT SUM(CAST(payment_amount AS DECIMAL(10,2))) as total FROM $table WHERE $fast_where";
    $fast_amount_result = $DB->fetch_one($fast_amount_sql);
    $fast_amount = $fast_amount_result && $fast_amount_result['total'] ? number_format($fast_amount_result['total'], 2) : '0.00';

    $type_stats[] = array(
        'type' => 3,
        'name' => '快审',
        'count' => $fast_record_count,
        'record_count' => $fast_record_count,
        'amount' => $fast_amount
    );
    
    $smarty->assign('records', $records);
    $smarty->assign('stats', $stats);
    $smarty->assign('type_stats', $type_stats);
    $smarty->assign('showpage', $showpage);
    $smarty->assign('total', $total);
    $smarty->assign('payment_type', $payment_type);
    $smarty->assign('start_date', $start_date);
    $smarty->assign('end_date', $end_date);
    $smarty->assign('keywords', $keywords);
    $smarty->assign('fileurl', $fileurl);
}

smarty_output($tempfile);
?>
