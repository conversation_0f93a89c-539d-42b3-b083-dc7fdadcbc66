<?php
/*
 * <AUTHOR>  　 @祥💥　技术支持
 * @Mail         : <EMAIL>
 * @Date         : 2025-02-12 15:22:31
 * @LastEditTime : 2025-02-15 17:17:51
 * @LastEditors  :  　 @祥💥　技术支持
 * @Description  : 
 * @FilePath     : \35dir\module\webdir.php
 * It's up to you ^_^
 * Copyright (c) 2025 by <EMAIL>, All Rights Reserved. 
 */
if (!defined('IN_IWEBDIR')) exit('Access Denied');

$pagename = '网站目录';
$pageurl = '?mod=webdir';
$tempfile = 'webdir.html';
$table = $DB->table('websites');

$pagesize = 10;
$curpage = intval(isset($_GET['page'])? $_GET['page'] : 1);
if ($curpage > 1) {
	$start = ($curpage - 1) * $pagesize;
} else {
	$start = 0;
	$curpage = 1;
}
		
$cate_id = intval(isset($_GET['cid'])? $_GET['cid'] : 0);
$cache_id = $cate_id.'-'.$curpage;

$pageurl .= '&cid='.$cate_id;

if (!$smarty->isCached($tempfile, $cache_id)) {
	// 默认SEO设置
	$seo_title = $pagename . ' - ' . $options['site_name'];
	$seo_keywords = $options['site_keywords'];
	$seo_description = $options['site_description'];

	$where = "w.web_status=3 AND (w.web_violation_status IS NULL OR w.web_violation_status=0)";
	if ($cate_id > 0) {
		$cate = get_one_category($cate_id);
		if (!$cate) {
			unset($cate);
			redirect('?mod=category');
		}

		// 优化分类页面SEO标题
		$seo_title = $cate['cate_name'] . '网站大全 - ' . $cate['cate_name'] . '网站目录 - ' . $options['site_name'];

		// 优化关键词 - 结合分类关键词和网站关键词
		$seo_keywords_array = array();
		if (!empty($cate['cate_keywords'])) {
			$cate_keywords = explode(',', $cate['cate_keywords']);
			$seo_keywords_array = array_merge($seo_keywords_array, $cate_keywords);
		}
		$seo_keywords_array[] = $cate['cate_name'] . '网站';
		$seo_keywords_array[] = $cate['cate_name'] . '网站大全';
		$seo_keywords_array[] = $cate['cate_name'] . '网站目录';
		$seo_keywords_array[] = '网站收录';
		if (!empty($options['site_keywords'])) {
			$site_keywords = explode(',', $options['site_keywords']);
			$seo_keywords_array = array_merge($seo_keywords_array, $site_keywords);
		}
		$seo_keywords = implode(',', array_unique($seo_keywords_array));

		// 优化描述 - 更丰富的描述信息
		if (!empty($cate['cate_description'])) {
			$seo_description = $cate['cate_description'];
		} else {
			$seo_description = $cate['cate_name'] . '网站大全，收录优质的' . $cate['cate_name'] . '相关网站';
		}

		// 添加统计信息到描述中
		$total_count = $DB->get_count($DB->table('websites').' w', $where . " AND w.cate_id IN (" . ($cate['cate_childcount'] > 0 ? $cate['cate_arrchildid'] : $cate_id) . ")");
		if ($total_count > 0) {
			$seo_description .= '，共收录' . $total_count . '个优质网站';
		}
		$seo_description .= '。' . $options['site_name'] . '为您提供' . $cate['cate_name'] . '网站分类目录服务。';

		// 确保描述长度适中
		if (mb_strlen($seo_description, 'UTF-8') > 160) {
			$seo_description = mb_substr($seo_description, 0, 157, 'UTF-8') . '...';
		}
		if ($cate['cate_childcount'] > 0) {
			$where .= " AND w.cate_id IN (".$cate['cate_arrchildid'].")";
			$categories = get_categories($cate['cate_id']);
		} else {
			$where .= " AND w.cate_id=$cate_id";
			$categories = get_categories($cate['root_id']);
		}

		$smarty->assign('site_path', get_sitepath($cate['cate_mod'], $cate['cate_id']));
		$smarty->assign('site_rss', get_rssfeed($cate['cate_mod'], $cate['cate_id']));
	} else {
		$categories = get_categories();
		$smarty->assign('site_path', get_sitepath('webdir'));
		$smarty->assign('site_rss', get_rssfeed('webdir'));
	}

	// 应用SEO设置
	$smarty->assign('site_title', $seo_title);
	$smarty->assign('site_keywords', $seo_keywords);
	$smarty->assign('site_description', $seo_description);
			
	$websites = get_website_list($where, 'web_ctime', 'DESC', $start, $pagesize);
	$total = $DB->get_count($table.' w', $where);
	$showpage = showpage($pageurl, $total, $curpage, $pagesize);
	
	$smarty->assign('pagename', $pagename);
	$smarty->assign('cate_id', isset($cate_id) ? $cate_id : 0);
	$smarty->assign('cate_name', isset($cate['cate_name']) ? '“'.$cate['cate_name'].'”网站目录' : $pagename);
	$smarty->assign('categories', $categories);
	$smarty->assign('total', $total);
	$smarty->assign('websites', $websites);
	$smarty->assign('showpage', $showpage);
	unset($child_category, $websites);
}

smarty_output($tempfile, $cache_id);
?>