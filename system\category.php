<?php
require('common.php');
require(APP_PATH.'module/category.php');

$fileurl = 'category.php';
$tempfile = 'category.html';
$table = $DB->table('categories');

$root_id = intval($_GET['root_id']);
$cate_mod = !empty($_GET['mod']) ? $_GET['mod'] : 'webdir';
$smarty->assign('root_id', $root_id);
$smarty->assign('cate_mod', $cate_mod);

if (!isset($action)) $action = 'list';

/** list */
if ($action == 'list') {
	$pagetitle = '分类列表';
	
	$sql = "SELECT * FROM $table WHERE root_id=$root_id AND cate_mod='$cate_mod' ORDER BY cate_order ASC";
	$query = $DB->query($sql);
	$categories = array();
	while ($row = $DB->fetch_array($query)) {
		$row['cate_mod'] = $category_modules[$row['cate_mod']];
		$cate_attr = empty($row['cate_url']) ? '<span class="gre">内部</span>' : '<span class="red">外部</span>';
		$cate_attr .= $row['cate_isbest'] != 0 ? ' - <span class="gre">推荐</span>' : '';
		$row['cate_attr'] = $cate_attr;
		$row['cate_operate'] = '<a href="'.$fileurl.'?mod='.$cate_mod.'&act=edit&cate_id='.$row['cate_id'].'">编辑</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?mod='.$cate_mod.'&act=clear&cate_id='.$row['cate_id'].'" onClick="return confirm(\'注：该操作将清空此分类及其子分类下的内容！\n\n确定清空吗？\');">清空</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?mod='.$cate_mod.'&act=del&cate_id='.$row['cate_id'].'" onClick="return confirm(\'注：该操作将同时删除此分类下的子分类及相关内容！\n\n确定删除吗？\');">删除</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?mod='.$cate_mod.'&root_id='.$row['cate_id'].'">进入子类</a>&nbsp;|&nbsp;<a href="'.$fileurl.'?mod='.$cate_mod.'&act=add&root_id='.$row['cate_id'].'">添加子类</a>';
		$categories[] = $row;
	}
	unset($row);
	$DB->free_result($query);
	
	$smarty->assign('cate_mod', $cate_mod);
	$smarty->assign('categories', $categories);
	unset($categories);
}

/** add */
if ($action == 'add') {
	$pagetitle = '添加分类';
	
	$smarty->assign('category_option', get_category_option($cate_mod, 0, $root_id, 0));
	$smarty->assign('h_action', 'saveadd');
}

/** batch_import */
if ($action == 'batch_import') {
	$pagetitle = '批量导入分类';

	$smarty->assign('category_option', get_category_option($cate_mod, 0, $root_id, 0));
	$smarty->assign('h_action', 'save_batch_import');
}

/** edit */
if ($action == 'edit') {
	$pagetitle = '编辑分类';
	
	$cate_id = intval($_GET['cate_id']);
	$row = get_one_category($cate_id);;
	if (!$row) {
		msgbox('指定的内容不存在！');
	}
	
	$smarty->assign('category_option', get_category_option($row['cate_mod'], 0, $row['root_id'], 0));
	$smarty->assign('row', $row);
	$smarty->assign('h_action', 'saveedit');
}

/** reset */
if ($action == 'reset') {
	$pagetitle = '复位分类';
	
	$smarty->assign('h_action', 'savereset');
}

/** merge */
if ($action == 'merge') {
	$pagetitle = '合并分类';
	$category_option = get_category_option($cate_mod, 0, 0, 0);
	
	$smarty->assign('category_option', $category_option);
	$smarty->assign('h_action', 'saveunite');
}

/** save batch import */
if ($action == 'save_batch_import') {
	$root_id = intval(trim($_POST['root_id']));
	$cate_mod = trim($_POST['cate_mod']);
	$categories_text = trim($_POST['categories_text']);

	if (empty($cate_mod)) $cate_mod = 'webdir';

	if (empty($categories_text)) {
		msgbox('请输入分类数据！');
	}

	// 解析分类数据，支持层级结构
	$lines = explode("\n", $categories_text);
	$success_count = 0;
	$error_count = 0;
	$skip_count = 0;
	$results = array();
	$parent_stack = array(); // 用于跟踪父级分类

	foreach ($lines as $line_num => $line) {
		$original_line = $line;
		$line = rtrim($line); // 只去除右边的空白，保留左边的缩进
		if (empty(trim($line))) continue;

		// 检测缩进级别
		$indent_level = 0;
		$clean_line = ltrim($line);
		$indent_chars = substr($line, 0, strlen($line) - strlen($clean_line));

		// 计算缩进级别（2个空格或1个Tab为一级）
		$spaces = substr_count($indent_chars, ' ');
		$tabs = substr_count($indent_chars, "\t");
		$indent_level = intval($spaces / 2) + $tabs;

		// 支持多种格式：
		// 1. 分类名称
		// 2. 分类名称|目录名|关键词|描述
		// 3. 分类名称,目录名,关键词,描述

		$parts = array();
		if (strpos($clean_line, '|') !== false) {
			$parts = explode('|', $clean_line);
		} elseif (strpos($clean_line, ',') !== false) {
			$parts = explode(',', $clean_line);
		} else {
			$parts = array($clean_line);
		}

		$cate_name = trim($parts[0]);
		$cate_dir = isset($parts[1]) ? trim($parts[1]) : '';
		$cate_keywords = isset($parts[2]) ? trim($parts[2]) : '';
		$cate_description = isset($parts[3]) ? trim($parts[3]) : '';

		if (empty($cate_name)) continue;

		// 根据缩进级别确定父级分类
		$current_parent_id = $root_id;
		if ($indent_level > 0) {
			// 调整父级分类栈
			$parent_stack = array_slice($parent_stack, 0, $indent_level);
			if (isset($parent_stack[$indent_level - 1])) {
				$current_parent_id = $parent_stack[$indent_level - 1];
			} else {
				$results[] = "✗ 缩进错误，找不到父级分类：{$cate_name}";
				$error_count++;
				continue;
			}
		} else {
			// 一级分类，清空父级分类栈
			$parent_stack = array();
			$current_parent_id = $root_id;
		}

		// 如果没有指定目录名，自动生成英文目录名
		if (empty($cate_dir)) {
			$cate_dir = generate_english_dir($cate_name);
		}

		// 检查分类是否已存在（在同一父级下）
		$check_sql = "SELECT cate_id FROM $table WHERE cate_name='".addslashes($cate_name)."' AND cate_mod='$cate_mod' AND root_id='$current_parent_id'";
		$result = $DB->query($check_sql);

		if ($DB->num_rows($result) > 0) {
			$existing_row = $DB->fetch_array($result);
			$existing_id = $existing_row['cate_id'];
			$results[] = "⚠ 分类已存在：{$cate_name} (ID: {$existing_id})";
			$skip_count++;

			// 即使跳过，也要将现有分类ID加入父级栈，以便子分类使用
			if ($indent_level < count($parent_stack)) {
				$parent_stack = array_slice($parent_stack, 0, $indent_level);
			}
			$parent_stack[$indent_level] = $existing_id;
			continue;
		}

		// 检查目录名是否重复
		if (!empty($cate_dir)) {
			$check_dir_sql = "SELECT cate_id FROM $table WHERE cate_dir='".addslashes($cate_dir)."' AND cate_mod='$cate_mod'";
			$dir_result = $DB->query($check_dir_sql);
			if ($DB->num_rows($dir_result) > 0) {
				$cate_dir .= '_' . time(); // 添加时间戳避免重复
			}
		}

		// 准备插入数据
		$data = array(
			'root_id' => $current_parent_id,
			'cate_mod' => $cate_mod,
			'cate_name' => $cate_name,
			'cate_dir' => $cate_dir,
			'cate_url' => '',
			'cate_isbest' => 0,
			'cate_order' => 0,
			'cate_keywords' => $cate_keywords,
			'cate_description' => $cate_description,
		);

		// 插入分类
		if ($DB->insert($table, $data)) {
			$new_cate_id = $DB->insert_id();

			// 构建层级显示
			$level_prefix = str_repeat('　', $indent_level);
			$results[] = "✓ 成功添加分类：{$level_prefix}{$cate_name} (ID: {$new_cate_id}, 父级ID: {$current_parent_id})";
			$success_count++;

			// 将新分类ID加入父级栈，供子分类使用
			if ($indent_level < count($parent_stack)) {
				$parent_stack = array_slice($parent_stack, 0, $indent_level);
			}
			$parent_stack[$indent_level] = $new_cate_id;
		} else {
			$level_prefix = str_repeat('　', $indent_level);
			$results[] = "✗ 添加分类失败：{$level_prefix}{$cate_name}";
			$error_count++;
		}
	}

	// 更新分类缓存
	if ($success_count > 0) {
		update_categories();
		update_cache('categories');
	}

	// 显示结果
	$message = "批量导入完成！\n\n";
	$message .= "成功添加：{$success_count} 个分类\n";
	$message .= "跳过重复：{$skip_count} 个分类\n";
	$message .= "添加失败：{$error_count} 个分类\n";
	$message .= "总计处理：" . ($success_count + $skip_count + $error_count) . " 个分类\n\n";
	$message .= "详细结果：\n" . implode("\n", $results);

	$fileurl = empty($root_id) ? $fileurl .= '?mod='.$cate_mod : $fileurl .= '?mod='.$cate_mod.'&root_id='.$root_id;
	msgbox($message, $fileurl);
}

/** save data */
if (in_array($action, array('saveadd', 'saveedit'))) {
	$root_id = intval(trim($_POST['root_id']));
	$cate_mod = trim($_POST['cate_mod']);
	$cate_name = trim($_POST['cate_name']);
	$cate_dir = trim($_POST['cate_dir']);
	$cate_url = trim($_POST['cate_url']);
	$cate_isbest = intval($_POST['cate_isbest']);
	$cate_order = intval($_POST['cate_order']);
	$cate_keywords = trim($_POST['cate_keywords']);
	$cate_description = trim($_POST['cate_description']);
	
	if (empty($cate_mod)) $cate_mod = 'webdir';

	if (empty($cate_name)) {
		msgbox('请输入分类名称！');
	}
	
	if (!empty($cate_dir)) {
		if (!is_valid_dir($cate_dir)) {
			msgbox('目录名称只能是英文字母开头，数字，中划线，下划线组成！');
		}
	}
	
	$data = array(
		'root_id' => $root_id,
		'cate_mod' => $cate_mod,
		'cate_name' => $cate_name,
		'cate_dir' => $cate_dir,
		'cate_url' => $cate_url,
		'cate_isbest' => $cate_isbest,
		'cate_order' => $cate_order,
		'cate_keywords' => $cate_keywords,
		'cate_description' => $cate_description,
	);
	
	if ($action == 'saveadd') {
    	$query = $DB->query("SELECT cate_id FROM $table WHERE root_id='$root_id' AND cate_name='$cate_name'");
    	if ($DB->num_rows($query)) {
        	msgbox('您所添加的分类已存在！');
    	}
		$DB->insert($table, $data);
		update_categories();
		update_cache('categories');
		
		$fileurl = empty($root_id) ? $fileurl .= '?mod='.$cate_mod : $fileurl .= '?mod='.$cate_mod.'&root_id='.$root_id;
		redirect($fileurl);
	} elseif ($action == 'saveedit') {
		$cate_id = intval($_POST['cate_id']);
		$where = array('cate_id' => $cate_id);
		
		$DB->update($table, $data, $where);
		update_categories();
		update_cache('categories');
		
		$fileurl = empty($root_id) ? $fileurl .= '?mod='.$cate_mod : $fileurl .= '?mod='.$cate_mod.'&root_id='.$root_id;
		redirect($fileurl);
	}
}

/** del */
if ($action == 'del') {
	$cate_id = intval($_GET['cate_id']);
	
	$sql = "SELECT cate_arrchildid FROM $table WHERE cate_mod='$cate_mod' AND cate_id=$cate_id";
	$cate = $DB->fetch_one($sql);
	if (!$cate) {
		msgbox('指定的分类不存在！');
	} else {
		$child_cate = $cate['cate_arrchildid'];
	}
	
	$DB->delete($table, 'cate_id IN ('.$child_cate.')');
	$DB->delete($DB->table('websites'), 'cate_id IN ('.$child_cate.')');
	$DB->delete($DB->table('articles'), 'cate_id IN ('.$child_cate.')');
	update_categories();
	update_cache('categories');
	
	msgbox('分类删除成功！', $fileurl.'?mod='.$cate_mod);
}

/** clear */
if ($action == 'clear') {
	$cate_id = intval($_GET['cate_id']);
	
	$sql = "SELECT cate_arrchildid FROM $table WHERE cate_mod='$cate_mod' AND cate_id=$cate_id";
	$cate = $DB->fetch_one($sql);
	if (!$cate) {
		msgbox('指定的分类不存在！');
	} else {
		$child_cate = $cate['cate_arrchildid'];
	}
	
	if ($cate_mod == 'webdir') {
		$DB->delete($DB->table('websites'), 'cate_id IN ('.$child_cate.')');
	} else {
		$DB->delete($DB->table('articles'), 'cate_id IN ('.$child_cate.')');
	}
	update_categories();
	
	msgbox('指定分类下的内容已清空！', $fileurl.'?mod='.$cate_mod);
}

/** reset */
if ($action == 'savereset') {
	$cate_mod = trim($_POST['cate_mod']);
	$DB->update($table, array('root_id' => 0), array('cate_mod' => $cate_mod));
	update_categories();
	update_cache('categories');
	
	msgbox('分类复位成功，请重新对分类进行归属设置！', $fileurl);
}

/** unite */
if ($action == 'saveunite') {
	$source_id = (int) $_POST['source_id'];
	$target_id = (int) $_POST['target_id'];
	$cate_mod = trim($_POST['cate_mod']);
	
	if (empty($source_id)) {
		msgbox('请选择要合并的分类！');
	}
	
	if (empty($target_id)) {
		msgbox('请选择目标分类！');
	}
	
	if ($source_id == $target_id) {
		msgbox('请不要在相同的分类内操作！');
	}
	
	$sql = "SELECT cate_childcount FROM $table WHERE cate_mod='$cate_mod' AND cate_id=$target_id";
	$cate = $DB->fetch_one($sql);
	if (!$cate) {
		msgbox('指定的目标分类不存在！');
	} else {
		$target_child_count = $cate['cate_childcount'];
	}
	
	if ($target_child_count > 0) {
		msgbox('目标分类中含有子分类，不能进行操作！');
	}
	
	$DB->delete($table, array('cate_id' => $source_id, 'cate_mod' => $cate_mod));
	if ($cate_mod == 'webdir') {
		$DB->update($DB->table('websites'), array('cate_id' => $target_id), array('cate_id' => $source_id));
	} else {
		$DB->update($DB->table('articles'), array('cate_id' => $target_id), array('cate_id' => $source_id));
	}
	update_categories();
	update_cache('categories');
	
	msgbox('分类合并成功，且内容已转移到目标分类中！', $fileurl);
}

function update_categories() {
	global $DB, $table, $category;
	
	$sql = "SELECT cate_id, cate_mod FROM $table ORDER BY cate_id ASC";
	$cate_ids = $DB->fetch_all($sql);
	
	foreach ($cate_ids as $id) {
		$parent_id = get_category_parent_ids($id['cate_id']);
		$child_id = $id['cate_id'].get_category_child_ids($id['cate_id']);
		$child_count = get_category_count($id['cate_id']);
		if ($id['cate_mod'] == 'webdir') {
			// 修正：只统计已审核通过的网站（web_status=3）
			$post_count = $DB->get_count($DB->table('websites'), 'cate_id IN ('.$child_id.') AND web_status=3');
		} else {
			$post_count = $DB->get_count($DB->table('articles'), 'cate_id IN ('.$child_id.')');
		}

		$data = array(
			'cate_arrparentid' => $parent_id,
			'cate_arrchildid' => $child_id,
			'cate_childcount' => $child_count,
			'cate_postcount' => $post_count,
		);
		$where = array('cate_id' => $id['cate_id']);

		$DB->update($table, $data, $where);
	}
}

/**
 * 生成英文目录名
 * @param string $cate_name 分类名称
 * @return string 英文目录名
 */
function generate_english_dir($cate_name) {
	// 中文到英文的映射表
	$chinese_to_english = array(
		// 编程开发类
		'编程开发' => 'programming',
		'程序开发' => 'development',
		'前端开发' => 'frontend',
		'后端开发' => 'backend',
		'移动开发' => 'mobile',
		'APP开发' => 'app',
		'网站开发' => 'web',
		'软件开发' => 'software',
		'游戏开发' => 'game',
		'数据库' => 'database',
		'运维部署' => 'devops',
		'云计算' => 'cloud',
		'人工智能' => 'ai',
		'机器学习' => 'ml',
		'区块链' => 'blockchain',
		'物联网' => 'iot',
		'大数据' => 'bigdata',
		'网络安全' => 'security',
		'算法' => 'algorithm',
		'架构设计' => 'architecture',

		// 具体技术
		'HTML' => 'html',
		'CSS' => 'css',
		'JavaScript' => 'javascript',
		'JS' => 'js',
		'PHP' => 'php',
		'Python' => 'python',
		'Java' => 'java',
		'C++' => 'cpp',
		'C#' => 'csharp',
		'Go' => 'golang',
		'Rust' => 'rust',
		'Swift' => 'swift',
		'Kotlin' => 'kotlin',
		'Vue' => 'vue',
		'React' => 'react',
		'Angular' => 'angular',
		'Node' => 'nodejs',
		'MySQL' => 'mysql',
		'MongoDB' => 'mongodb',
		'Redis' => 'redis',
		'Docker' => 'docker',
		'Kubernetes' => 'k8s',
		'Linux' => 'linux',
		'Windows' => 'windows',
		'Android' => 'android',
		'iOS' => 'ios',

		// 设计类
		'设计素材' => 'design',
		'平面设计' => 'graphic',
		'UI设计' => 'ui',
		'UX设计' => 'ux',
		'网页设计' => 'webdesign',
		'LOGO设计' => 'logo',
		'品牌设计' => 'brand',
		'包装设计' => 'package',
		'海报设计' => 'poster',
		'图标设计' => 'icon',
		'插画设计' => 'illustration',
		'摄影' => 'photography',
		'视频' => 'video',
		'音频' => 'audio',
		'字体' => 'font',
		'模板' => 'template',
		'素材' => 'material',
		'背景' => 'background',
		'纹理' => 'texture',
		'图片' => 'image',
		'矢量' => 'vector',

		// 工具软件类
		'工具软件' => 'tools',
		'实用工具' => 'utilities',
		'系统工具' => 'system',
		'办公软件' => 'office',
		'开发工具' => 'devtools',
		'设计工具' => 'designtools',
		'网络工具' => 'network',
		'安全工具' => 'security',
		'优化工具' => 'optimize',
		'清理工具' => 'cleaner',
		'压缩工具' => 'compress',
		'下载工具' => 'download',
		'播放器' => 'player',
		'浏览器' => 'browser',
		'编辑器' => 'editor',
		'IDE' => 'ide',
		'文本编辑' => 'texteditor',
		'图像处理' => 'imageprocess',
		'视频编辑' => 'videoeditor',
		'音频编辑' => 'audioeditor',

		// 学习教育类
		'学习教育' => 'education',
		'在线教育' => 'online',
		'课程' => 'course',
		'教程' => 'tutorial',
		'培训' => 'training',
		'考试' => 'exam',
		'认证' => 'certification',
		'语言学习' => 'language',
		'英语学习' => 'english',
		'编程教程' => 'coding',
		'技术文档' => 'docs',
		'学术' => 'academic',
		'科研' => 'research',
		'论文' => 'paper',
		'书籍' => 'books',
		'电子书' => 'ebook',
		'视频教程' => 'video',
		'直播课程' => 'live',

		// 生活服务类
		'生活服务' => 'life',
		'本地服务' => 'local',
		'同城服务' => 'city',
		'家政服务' => 'housekeeping',
		'维修服务' => 'repair',
		'美食' => 'food',
		'餐饮' => 'restaurant',
		'外卖' => 'delivery',
		'购物' => 'shopping',
		'电商' => 'ecommerce',
		'商城' => 'mall',
		'超市' => 'supermarket',
		'交通' => 'transport',
		'出行' => 'travel',
		'地图' => 'map',
		'导航' => 'navigation',
		'公交' => 'bus',
		'地铁' => 'subway',
		'打车' => 'taxi',
		'租车' => 'rental',
		'酒店' => 'hotel',
		'民宿' => 'homestay',
		'旅游' => 'tourism',
		'景点' => 'attraction',
		'票务' => 'ticket',

		// 新闻资讯类
		'新闻资讯' => 'news',
		'科技新闻' => 'tech',
		'财经新闻' => 'finance',
		'体育新闻' => 'sports',
		'娱乐新闻' => 'entertainment',
		'社会新闻' => 'society',
		'国际新闻' => 'international',
		'军事新闻' => 'military',
		'汽车新闻' => 'auto',
		'房产新闻' => 'real-estate',
		'健康新闻' => 'health',
		'教育新闻' => 'edu',
		'时尚新闻' => 'fashion',
		'游戏新闻' => 'gaming',

		// 娱乐类
		'游戏娱乐' => 'gaming',
		'网络游戏' => 'online-game',
		'手机游戏' => 'mobile-game',
		'单机游戏' => 'pc-game',
		'游戏攻略' => 'guide',
		'电影' => 'movie',
		'电视剧' => 'tv',
		'综艺' => 'variety',
		'动漫' => 'anime',
		'音乐' => 'music',
		'小说' => 'novel',
		'漫画' => 'comic',
		'直播' => 'live',
		'短视频' => 'short-video',

		// 金融理财类
		'金融理财' => 'finance',
		'投资理财' => 'investment',
		'股票' => 'stock',
		'基金' => 'fund',
		'保险' => 'insurance',
		'银行' => 'bank',
		'贷款' => 'loan',
		'信用卡' => 'credit-card',
		'外汇' => 'forex',
		'期货' => 'futures',
		'数字货币' => 'crypto',
		'比特币' => 'bitcoin',
		'区块链' => 'blockchain',

		// 医疗健康类
		'医疗健康' => 'health',
		'医院' => 'hospital',
		'诊所' => 'clinic',
		'药店' => 'pharmacy',
		'体检' => 'checkup',
		'养生' => 'wellness',
		'健身' => 'fitness',
		'减肥' => 'weight-loss',
		'美容' => 'beauty',
		'护肤' => 'skincare',
		'化妆' => 'makeup',
		'母婴' => 'maternity',
		'育儿' => 'parenting',
		'老人' => 'elderly',
		'心理' => 'psychology',

		// 汽车交通类
		'汽车' => 'auto',
		'新车' => 'new-car',
		'二手车' => 'used-car',
		'汽车评测' => 'review',
		'汽车保养' => 'maintenance',
		'汽车配件' => 'parts',
		'驾驶' => 'driving',
		'驾考' => 'driving-test',
		'车险' => 'car-insurance',
		'加油' => 'gas-station',
		'停车' => 'parking',
		'洗车' => 'car-wash',

		// 房产家居类
		'房产' => 'real-estate',
		'买房' => 'buy-house',
		'租房' => 'rent',
		'装修' => 'decoration',
		'家具' => 'furniture',
		'家电' => 'appliance',
		'建材' => 'material',
		'设计' => 'design',
		'园艺' => 'garden',
		'清洁' => 'cleaning',

		// 体育运动类
		'体育运动' => 'sports',
		'足球' => 'football',
		'篮球' => 'basketball',
		'网球' => 'tennis',
		'羽毛球' => 'badminton',
		'乒乓球' => 'ping-pong',
		'游泳' => 'swimming',
		'跑步' => 'running',
		'健身' => 'fitness',
		'瑜伽' => 'yoga',
		'武术' => 'martial-arts',
		'户外' => 'outdoor',
		'登山' => 'climbing',
		'骑行' => 'cycling',

		// 时尚美容类
		'时尚' => 'fashion',
		'服装' => 'clothing',
		'鞋子' => 'shoes',
		'包包' => 'bags',
		'配饰' => 'accessories',
		'珠宝' => 'jewelry',
		'手表' => 'watch',
		'美容' => 'beauty',
		'护肤' => 'skincare',
		'化妆品' => 'cosmetics',
		'香水' => 'perfume',
		'美发' => 'hair',
		'美甲' => 'nail',

		// 其他常用词
		'热门' => 'hot',
		'推荐' => 'recommend',
		'最新' => 'latest',
		'精选' => 'featured',
		'免费' => 'free',
		'付费' => 'paid',
		'会员' => 'vip',
		'专业' => 'professional',
		'企业' => 'enterprise',
		'个人' => 'personal',
		'商业' => 'business',
		'创业' => 'startup',
		'科技' => 'technology',
		'互联网' => 'internet',
		'电子商务' => 'ecommerce',
		'社交网络' => 'social',
		'论坛' => 'forum',
		'博客' => 'blog',
		'资源' => 'resource',
		'下载' => 'download',
		'分享' => 'share',
		'交流' => 'community',
		'服务' => 'service',
		'平台' => 'platform',
		'网站' => 'website',
		'应用' => 'app',
		'软件' => 'software',
		'系统' => 'system',
		'技术' => 'tech',
		'解决方案' => 'solution',
		'产品' => 'product',
		'项目' => 'project',
		'案例' => 'case',
		'经验' => 'experience',
		'技巧' => 'tips',
		'指南' => 'guide',
		'帮助' => 'help',
		'支持' => 'support',
		'文档' => 'docs',
		'API' => 'api',
		'开源' => 'opensource',
		'框架' => 'framework',
		'库' => 'library',
		'插件' => 'plugin',
		'扩展' => 'extension',
		'主题' => 'theme',
		'模板' => 'template',
	);

	// 先尝试直接匹配
	$clean_name = trim($cate_name);
	if (isset($chinese_to_english[$clean_name])) {
		return $chinese_to_english[$clean_name];
	}

	// 尝试部分匹配
	foreach ($chinese_to_english as $chinese => $english) {
		if (strpos($clean_name, $chinese) !== false) {
			return $english;
		}
	}

	// 如果包含英文，提取英文部分
	if (preg_match('/[a-zA-Z]+/', $clean_name, $matches)) {
		$english_part = strtolower($matches[0]);
		if (strlen($english_part) >= 3) {
			return $english_part;
		}
	}

	// 如果都没有匹配，使用拼音转换（简化版）
	$pinyin_map = array(
		'a' => 'a', 'ai' => 'ai', 'an' => 'an', 'ang' => 'ang', 'ao' => 'ao',
		'ba' => 'ba', 'bai' => 'bai', 'ban' => 'ban', 'bang' => 'bang', 'bao' => 'bao',
		'bei' => 'bei', 'ben' => 'ben', 'beng' => 'beng', 'bi' => 'bi', 'bian' => 'bian',
		'biao' => 'biao', 'bie' => 'bie', 'bin' => 'bin', 'bing' => 'bing', 'bo' => 'bo',
		'bu' => 'bu', 'ca' => 'ca', 'cai' => 'cai', 'can' => 'can', 'cang' => 'cang',
		'cao' => 'cao', 'ce' => 'ce', 'cen' => 'cen', 'ceng' => 'ceng', 'cha' => 'cha',
		'chai' => 'chai', 'chan' => 'chan', 'chang' => 'chang', 'chao' => 'chao', 'che' => 'che',
		'chen' => 'chen', 'cheng' => 'cheng', 'chi' => 'chi', 'chong' => 'chong', 'chou' => 'chou',
		'chu' => 'chu', 'chuai' => 'chuai', 'chuan' => 'chuan', 'chuang' => 'chuang', 'chui' => 'chui',
		'chun' => 'chun', 'chuo' => 'chuo', 'ci' => 'ci', 'cong' => 'cong', 'cou' => 'cou',
		'cu' => 'cu', 'cuan' => 'cuan', 'cui' => 'cui', 'cun' => 'cun', 'cuo' => 'cuo'
	);

	// 最后的备选方案：生成通用目录名
	$fallback_names = array('category', 'section', 'group', 'type', 'class', 'item');
	return $fallback_names[array_rand($fallback_names)] . '_' . time();
}

smarty_output($tempfile);
?>