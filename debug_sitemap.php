<?php
/**
 * 调试站点地图生成
 */

// 开启错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

try {
    require(APP_PATH.'init.php');
    require(APP_PATH.'module/prelink.php');
    require(APP_PATH.'module/category.php');
    require(APP_PATH.'module/website.php');
    
    echo "<!DOCTYPE html>\n";
    echo "<html>\n";
    echo "<head>\n";
    echo "<meta charset='utf-8'>\n";
    echo "<title>站点地图调试</title>\n";
    echo "<style>\n";
    echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
    echo ".debug-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }\n";
    echo ".success { background-color: #d4edda; border-color: #c3e6cb; }\n";
    echo ".error { background-color: #f8d7da; border-color: #f5c6cb; }\n";
    echo ".warning { background-color: #fff3cd; border-color: #ffeaa7; }\n";
    echo "pre { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto; }\n";
    echo "</style>\n";
    echo "</head>\n";
    echo "<body>\n";
    echo "<h1>站点地图调试信息</h1>\n";
    
    // 检查数据库连接
    echo "<div class='debug-section'>\n";
    echo "<h2>数据库连接检查</h2>\n";
    if (isset($DB) && is_object($DB)) {
        echo "<p style='color: green;'>✓ 数据库连接正常</p>\n";
        
        // 检查网站数据
        $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
        echo "<p>正常网站数量: $website_count</p>\n";
        
        // 检查文章数据
        $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
        echo "<p>正常文章数量: $article_count</p>\n";
        
        // 检查分类数据
        $category_count = $DB->get_count($DB->table('categories'), "1=1");
        echo "<p>分类数量: $category_count</p>\n";
        
    } else {
        echo "<p style='color: red;'>✗ 数据库连接失败</p>\n";
    }
    echo "</div>\n";
    
    // 检查必要函数
    echo "<div class='debug-section'>\n";
    echo "<h2>函数检查</h2>\n";
    $functions = ['get_website_url', 'get_article_url', 'get_category_url', 'get_one_category', 'datediff'];
    foreach ($functions as $func) {
        if (function_exists($func)) {
            echo "<p style='color: green;'>✓ $func 函数存在</p>\n";
        } else {
            echo "<p style='color: red;'>✗ $func 函数不存在</p>\n";
        }
    }
    echo "</div>\n";
    
    // 测试webdir站点地图生成
    echo "<div class='debug-section'>\n";
    echo "<h2>Webdir站点地图测试</h2>\n";
    
    try {
        // 捕获所有输出和错误
        ob_start();
        $error_output = '';
        
        // 设置错误处理
        set_error_handler(function($severity, $message, $file, $line) use (&$error_output) {
            $error_output .= "错误: $message (文件: $file, 行: $line)\n";
        });
        
        get_website_sitemap(0);
        
        restore_error_handler();
        $output = ob_get_clean();
        
        if (!empty($error_output)) {
            echo "<div class='error'>\n";
            echo "<h3>发现错误:</h3>\n";
            echo "<pre>" . htmlspecialchars($error_output) . "</pre>\n";
            echo "</div>\n";
        }
        
        $length = strlen($output);
        $url_count = substr_count($output, '<url>');
        $has_xml_start = strpos($output, '<?xml') === 0;
        $has_xml_end = strpos($output, '</urlset>') !== false;
        $has_namespace = strpos($output, 'xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"') !== false;
        
        echo "<p><strong>输出长度:</strong> $length 字符</p>\n";
        echo "<p><strong>URL数量:</strong> $url_count</p>\n";
        echo "<p><strong>XML开始:</strong> " . ($has_xml_start ? '✓' : '✗') . "</p>\n";
        echo "<p><strong>XML结束:</strong> " . ($has_xml_end ? '✓' : '✗') . "</p>\n";
        echo "<p><strong>命名空间:</strong> " . ($has_namespace ? '✓' : '✗') . "</p>\n";
        
        if ($has_xml_start && $has_xml_end && $has_namespace && $url_count > 0) {
            echo "<p style='color: green; font-weight: bold;'>✓ 站点地图生成成功！</p>\n";
        } else {
            echo "<p style='color: red; font-weight: bold;'>✗ 站点地图生成失败</p>\n";
        }
        
        // 显示输出内容的前1000个字符
        echo "<h3>输出内容预览:</h3>\n";
        echo "<pre>" . htmlspecialchars(substr($output, 0, 1000));
        if ($length > 1000) {
            echo "\n... (还有 " . ($length - 1000) . " 个字符)";
        }
        echo "</pre>\n";
        
        // 尝试解析XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($output);
        if ($xml !== false) {
            echo "<p style='color: green;'>✓ XML解析成功</p>\n";
        } else {
            echo "<p style='color: red;'>✗ XML解析失败</p>\n";
            $errors = libxml_get_errors();
            if (!empty($errors)) {
                echo "<h4>XML错误:</h4>\n";
                echo "<pre>";
                foreach ($errors as $error) {
                    echo htmlspecialchars(trim($error->message)) . " (行: {$error->line})\n";
                }
                echo "</pre>\n";
            }
        }
        libxml_clear_errors();
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div class='error'>\n";
        echo "<p><strong>异常:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "<p><strong>文件:</strong> " . htmlspecialchars($e->getFile()) . "</p>\n";
        echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>\n";
        echo "</div>\n";
    }
    
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<div class='error'>\n";
    echo "<h2>初始化错误</h2>\n";
    echo "<p><strong>错误:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p><strong>文件:</strong> " . htmlspecialchars($e->getFile()) . "</p>\n";
    echo "<p><strong>行号:</strong> " . $e->getLine() . "</p>\n";
    echo "</div>\n";
}

echo "</body>\n";
echo "</html>\n";
?>
