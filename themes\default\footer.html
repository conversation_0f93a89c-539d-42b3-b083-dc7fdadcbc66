
    <div id="footer">
        <a href="https://frogdr.com/95dir.com?utm_source=95dir.com" target="_blank" 
   style="display: block; text-align: center; margin: 0 auto 15px auto;">
    <img src="https://frogdr.com/95dir.com/badge-white.svg" 
         alt="Monitor your Domain Rating with FrogDR" 
         width="250" height="54" 
         style="display: inline-block; margin: 0 auto; vertical-align: middle;">
</a>
    	<div id="fmenu">{#foreach from=get_pages() item=item#}<a href="{#$item.page_link#}" title="{#$item.page_name#}">{#$item.page_name#}</a> | {#/foreach#}<a href="?mod=update" title="最新收录网站">最新收录</a> | <a href="?mod=archives" title="网站数据归档">数据归档</a> | <a href="?mod=top" title="热门网站排行榜">TOP排行榜</a> | <a href="?mod=blacklist" title="违规网站黑名单">黑名单</a> | <a href="?mod=rejected" title="审核不通过网站">审核不通过</a> | <a href="?mod=datastats" title="网站数据统计">数据公示</a> | <a href="?mod=sitemap" title="网站地图">站点地图</a></div>


    	<!-- SEO内容优化：网站描述和关键词 -->
    	<div style="margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; font-size: 14px; line-height: 1.6; color: #666;">
    	    <p style="margin: 0 0 10px 0; text-align: center;">
    	        <strong>95分类目录</strong> - 专业的网站分类目录平台，致力于为用户提供优质的网站收录服务。我们精心收录各行业优质网站，包括
    	        <a href="?mod=webdir" title="网站目录分类浏览">网站目录</a>、
    	        <a href="?mod=vip_list" title="VIP优质网站推荐">VIP网站</a>、
    	        <a href="?mod=article" title="站长资讯文章">站长资讯</a>等内容。
    	    </p>
    	    <p style="margin: 0; text-align: center; font-size: 13px;">
    	        关键词：网站目录、网站收录、分类目录、网站推荐、优质网站、VIP网站、站长资讯、网站分类、网站大全、免费收录
    	    </p>
    	</div>

    	<div id="fcopy">{#$site_copyright#} | <a href="https://beian.miit.gov.cn/#/Integrated/index" rel="nofollow" target="_blank">鄂ICP备2024062716号-1</a></div>
		<div id="fcopy">{#insert name="script_time"#}</div>

<!-- 在线统计和QQ群 -->
<div id="statsContainer" style="text-align: center; margin: 10px 0; font-size: 14px;">
    <div style="margin-bottom: 5px;">当前在线：<span id="onlineCount">加载中...</span>人 | 总访客：<span id="totalVisitors">加载中...</span>人</div>
    <a href="https://qun.qq.com/universal-share/share?ac=1&authKey=VD1VzovGfKEgsMPHwCRCMu6An9Er9ihrNfC88vO3Vkf0YgWW6O2OUZ6rQcFXnfXi&busi_data=eyJncm91cENvZGUiOiI4Njg4NTAyMTYiLCJ0b2tlbiI6IjZlcDhhc0srbGtNRlNrWjRkK3pVazA3blR0OGlUditUWGhYdldYdUI5N3M2VFF3dU0vMTBCSWl0c09relVZNUEiLCJ1aW4iOiIzNjMyMDk0In0=&data=3-2hnd4BuWS_RPz79Qa7DgA98Eag27Uvx7rVtiXfpes_7LoAlc5BtxTTkos3JyBmIcVTHp9ZC0K6p3qVXmJvYt__wRyd070zIKdMxpWzS_0&svctype=5&tempid=h5_group_info" target="icp_edi_blank" style="color: #007bff; text-decoration: none;">🐧群：868850216</a>
</div>
    </div>

<script>
    // 更新在线统计
    function updateOnlineStats() {
        // 添加时间戳防止缓存
        const timestamp = new Date().getTime();
        const url = '/data/online_stats/online.php?t=' + timestamp;

        fetch(url, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('HTTP ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            // 确保数据有效
            const online = parseInt(data.online) || 0;
            const total = parseInt(data.total) || 0;

            document.getElementById('onlineCount').textContent = online;
            document.getElementById('totalVisitors').textContent = total.toLocaleString();

            // 调试信息（可选）
            console.log('在线统计更新:', { online: online, total: total });
        })
        .catch(error => {
            console.error('在线统计服务错误:', error);
            document.getElementById('onlineCount').textContent = '加载中...';
            document.getElementById('totalVisitors').textContent = '加载中...';
        });
    }

    // 页面加载时立即执行
    document.addEventListener('DOMContentLoaded', function() {
        updateOnlineStats();
    });

    // 设置定时器 - 在线统计每60秒更新
    setInterval(updateOnlineStats, 60000);

    // 页面获得焦点时也更新一次
    window.addEventListener('focus', updateOnlineStats);
</script>