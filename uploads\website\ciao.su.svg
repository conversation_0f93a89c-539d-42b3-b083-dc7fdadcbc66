<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LibreDomains - 自由域名分发服务</title>
    <meta name="description" content="LibreDomains 是一个开源的二级域名分发服务，通过 GitHub Pull Request 申请和管理免费二级域名">
    <link rel="stylesheet" href="style.css">
    <link rel="icon" href="favicon.ico" type="image/x-icon">
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="logo">
                <h1>LibreDomains</h1>
                <p>自由域名分发服务</p>
            </div>
            <nav class="nav">
                <a href="#features">功能特性</a>
                <a href="#domains">可用域名</a>
                <a href="#guide">申请指南</a>
                <a href="#docs">文档</a>
                <a href="https://github.com/bestzwei/LibreDomains" target="_blank">GitHub</a>
            </nav>
        </div>
    </header>

    <main>
        <section class="hero">
            <div class="container">
                <h2>免费获得你的专属二级域名</h2>
                <p>通过 GitHub Pull Request 快速申请和管理二级域名，完全开源、免费使用</p>
                <div class="hero-actions">
                    <a href="#guide" class="btn btn-primary">开始申请</a>
                    <a href="https://github.com/bestzwei/LibreDomains" target="_blank" class="btn btn-secondary">查看源码</a>
                </div>
            </div>
        </section>

        <section id="features" class="features">
            <div class="container">
                <h2>功能特性</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🚀</div>
                        <h3>快速申请</h3>
                        <p>通过 GitHub PR 快速申请域名，自动化审核流程</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3>安全可靠</h3>
                        <p>基于 Cloudflare 的 DNS 服务，提供安全防护和高可用性</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🆓</div>
                        <h3>完全免费</h3>
                        <p>开源项目，完全免费使用，无隐藏费用</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>快速生效</h3>
                        <p>域名记录通常在 5-15 分钟内生效</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">🛠️</div>
                        <h3>灵活配置</h3>
                        <p>支持 A、AAAA、CNAME、TXT、MX 等多种记录类型</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>易于管理</h3>
                        <p>通过 GitHub 轻松管理和更新域名记录</p>
                    </div>
                </div>
            </div>
        </section>

        <section id="domains" class="domains">
            <div class="container">
                <h2>子域名查询</h2>
                <p class="section-description">检查你想要的子域名是否可用，选择合适的主域名进行申请</p>

                <!-- 子域名检测功能 -->
                <div class="subdomain-checker">
                    <h3>🔍 检测子域名可用性</h3>
                    <p>选择主域名并输入你想要的子域名，我们会实时检测是否可以申请</p>
                    
                    <div class="checker-form">
                        <div class="domain-selector">
                            <label for="domainSelect">选择主域名：</label>
                            <select id="domainSelect" class="domain-select">
                                <option value="ciao.su" data-status="available">ciao.su (可用)</option>
                                <option value="ciallo.de" data-status="paused">ciallo.de (暂停开放)</option>
                            </select>
                        </div>

                        <div class="input-group">
                            <input type="text" id="subdomainInput" placeholder="输入子域名（至少3个字符）" maxlength="63">
                            <span class="domain-suffix" id="domainSuffix">.ciao.su</span>
                            <button id="checkButton" class="btn btn-primary">检测</button>
                        </div>
                        
                        <div class="checker-result" id="checkerResult">
                            <div class="result-icon"></div>
                            <div class="result-text"></div>
                            <div class="result-details"></div>
                        </div>
                        
                        <div class="checker-stats">
                            <div class="stat-item">
                                <span class="stat-number" id="totalDomains">-</span>
                                <span class="stat-label">已注册域名</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number" id="availableDomains">∞</span>
                                <span class="stat-label">可申请域名</span>
                            </div>
                        </div>
                    </div>

                    <div class="recent-domains">
                        <h4>最近注册的域名</h4>
                        <div class="domains-list" id="recentDomainsList">
                            <div class="loading">正在加载...</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <section id="guide" class="guide">
            <div class="container">
                <h2>申请指南</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <div class="step-content">
                            <h3>Fork 仓库</h3>
                            <p>访问 <a href="https://github.com/bestzwei/LibreDomains" target="_blank">GitHub 仓库</a> 并 Fork 到你的账户</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <div class="step-content">
                            <h3>创建配置文件</h3>
                            <p>在 <code>domains/ciao.su/</code> 目录下创建 JSON 配置文件</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <div class="step-content">
                            <h3>填写配置</h3>
                            <p>按照指定格式填写域名配置信息</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <div class="step-content">
                            <h3>提交 PR</h3>
                            <p>提交 Pull Request 并等待自动检查和审核</p>
                        </div>
                    </div>
                    <div class="step">
                        <div class="step-number">5</div>
                        <div class="step-content">
                            <h3>域名生效</h3>
                            <p>PR 合并后，域名记录将在几分钟内生效</p>
                        </div>
                    </div>
                </div>

                <div class="config-example">
                    <h3>配置文件示例</h3>
                    <pre><code>{
  "description": "个人博客网站",
  "owner": {
    "name": "你的名字",
    "github": "你的GitHub用户名",
    "email": "你的邮箱"
  },
  "records": [
    {
      "type": "A",
      "name": "@",
      "content": "***************",
      "ttl": 3600,
      "proxied": false
    },
    {
      "type": "CNAME",
      "name": "www",
      "content": "你的GitHub用户名.github.io",
      "ttl": 3600,
      "proxied": false
    }
  ]
}</code></pre>
                </div>
            </div>
        </section>

        <section class="rules">
            <div class="container">
                <h2>申请规则</h2>
                <div class="rules-grid">
                    <div class="rule-card">
                        <h3>域名格式</h3>
                        <ul>
                            <li>小写字母、数字和连字符组成</li>
                            <li>长度在 2-63 个字符之间</li>
                            <li>不能以连字符开头或结尾</li>
                        </ul>
                    </div>
                    <div class="rule-card">
                        <h3>数量限制</h3>
                        <ul>
                            <li>每个用户最多申请 3 个子域名</li>
                            <li>每个子域名最多 10 个 DNS 记录</li>
                        </ul>
                    </div>
                    <div class="rule-card">
                        <h3>保留域名</h3>
                        <ul>
                            <li>系统保留：www, mail, api, admin</li>
                            <li>服务保留：cdn, ftp, ns, dns</li>
                            <li>开发保留：dev, test, staging</li>
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <section id="docs" class="docs">
            <div class="container">
                <h2>相关文档</h2>
                <div class="docs-grid">
                    <a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/user-guide.md" target="_blank" class="doc-card">
                        <div class="doc-icon">📖</div>
                        <h3>用户指南</h3>
                        <p>详细的使用教程和常见问题解答</p>
                    </a>
                    <a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/admin-guide.md" target="_blank" class="doc-card">
                        <div class="doc-icon">👥</div>
                        <h3>管理员指南</h3>
                        <p>管理员操作指南和维护说明</p>
                    </a>
                    <a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/technical-doc.md" target="_blank" class="doc-card">
                        <div class="doc-icon">🔧</div>
                        <h3>技术文档</h3>
                        <p>技术实现细节和 API 参考</p>
                    </a>
                    <a href="https://github.com/bestzwei/LibreDomains" target="_blank" class="doc-card">
                        <div class="doc-icon">📊</div>
                        <h3>GitHub 仓库</h3>
                        <p>查看源码，提交 Issue 和 PR</p>
                    </a>
                    <a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/terms-of-service.md" target="_blank" class="doc-card">
                        <div class="doc-icon">📋</div>
                        <h3>服务条款</h3>
                        <p>服务使用条款和用户协议</p>
                    </a>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3>LibreDomains</h3>
                    <p>开源的二级域名分发服务</p>
                </div>
                <div class="footer-section">
                    <h3>链接</h3>
                    <ul>
                        <li><a href="https://github.com/bestzwei/LibreDomains" target="_blank">GitHub</a></li>
                        <li><a href="https://github.com/bestzwei/LibreDomains/issues" target="_blank">问题反馈</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h3>文档</h3>
                    <ul>
                        <li><a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/user-guide.md" target="_blank">用户指南</a></li>
                        <li><a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/technical-doc.md" target="_blank">技术文档</a></li>
                        <li><a href="https://github.com/bestzwei/LibreDomains/blob/main/docs/terms-of-service.md" target="_blank">服务条款</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; <span id="year"></span> LibreDomains. 采用 MIT 许可证开源</p>
                <script>
                  document.getElementById('year').textContent = new Date().getFullYear();
                </script>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
