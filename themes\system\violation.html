{#include file="header.html"#}

<h3 class="title"><em>违规管理</em><span>共 {#$total#} 个违规网站</span></h3>

<div class="listbox">
    <div class="toolbar">
        <span>
            <a href="blacklist.php" class="btn">黑名单管理</a>
            <a href="website.php?status=1" class="btn">网站管理</a>
        </span>
    </div>

    <table width="100%" border="0" cellspacing="1" cellpadding="0">
        <tr>
            <th width="60">ID</th>
            <th width="200">网站名称</th>
            <th width="250">网站地址</th>
            <th width="120">拉黑时间</th>
            <th width="100">操作</th>
        </tr>
        {#foreach from=$websites item=website#}
        <tr>
            <td>{#$website.web_id#}</td>
            <td class="ltext">
                <a href="website.php?act=edit&web_id={#$website.web_id#}" title="编辑网站">
                    {#$website.web_name#}
                </a>
            </td>
            <td class="ltext">
                <a href="http://{#$website.web_url#}" target="_blank" title="{#$website.web_url#}">
                    {#if strlen($website.web_url) > 40#}
                        {#$website.web_url|substr:0:40#}...
                    {#else#}
                        {#$website.web_url#}
                    {#/if#}
                </a>
            </td>
            <td>{#$website.ctime_formatted#}</td>
            <td>
                <a href="website.php?act=edit&web_id={#$website.web_id#}">编辑</a>
                |
                <a href="website.php?act=del&web_id={#$website.web_id#}" 
                   onclick="return confirm('确认删除此网站吗？');" 
                   style="color: #f00;">删除</a>
            </td>
        </tr>
        {#foreachelse#}
        <tr><td colspan="5">暂无违规网站</td></tr>
        {#/foreach#}
    </table>
</div>

<style>
.ltext {
    text-align: left !important;
    padding-left: 10px !important;
}

.toolbar {
    margin-bottom: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 3px;
}

.btn {
    background: #007cba;
    color: white;
    padding: 5px 15px;
    text-decoration: none;
    border-radius: 3px;
    border: none;
    cursor: pointer;
    margin-right: 10px;
}

.btn:hover {
    background: #005a87;
}
</style>

{#include file="footer.html"#}
