<?php
/**
 * 分段调试webdir站点地图
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>Webdir站点地图分段调试</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".section { margin: 15px 0; padding: 15px; border: 1px solid #ddd; }\n";
echo ".success { background-color: #d4edda; }\n";
echo ".error { background-color: #f8d7da; }\n";
echo ".warning { background-color: #fff3cd; }\n";
echo "pre { background: #f8f9fa; padding: 10px; max-height: 200px; overflow-y: auto; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>Webdir站点地图分段调试</h1>\n";

// 第1步：检查基本数据
echo "<div class='section'>\n";
echo "<h2>第1步：检查基本数据</h2>\n";
try {
    $website_count = $DB->get_count($DB->table('websites'), "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)");
    $category_count = $DB->get_count($DB->table('categories'), "1=1");
    $article_count = $DB->get_count($DB->table('articles'), "art_status=3");
    $weblink_count = $DB->get_count($DB->table('weblinks'), "link_hide=0");
    
    echo "<div class='success'>\n";
    echo "<p>✓ 数据库查询成功</p>\n";
    echo "<p>网站数量: $website_count</p>\n";
    echo "<p>分类数量: $category_count</p>\n";
    echo "<p>文章数量: $article_count</p>\n";
    echo "<p>友情链接数量: $weblink_count</p>\n";
    echo "</div>\n";
} catch (Exception $e) {
    echo "<div class='error'>\n";
    echo "<p>✗ 数据库查询失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
echo "</div>\n";

// 第2步：测试XML头部生成
echo "<div class='section'>\n";
echo "<h2>第2步：测试XML头部生成</h2>\n";
try {
    ob_start();
    header("Content-Type: application/xml;");
    echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    echo "<url>\n";
    echo "<loc>".$options['site_url']."</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>1.0</priority>\n";
    echo "</url>\n";
    $header_output = ob_get_clean();
    
    echo "<div class='success'>\n";
    echo "<p>✓ XML头部生成成功</p>\n";
    echo "<p>长度: " . strlen($header_output) . " 字符</p>\n";
    echo "</div>\n";
    echo "<h4>输出内容:</h4>\n";
    echo "<pre>" . htmlspecialchars($header_output) . "</pre>\n";
} catch (Exception $e) {
    ob_end_clean();
    echo "<div class='error'>\n";
    echo "<p>✗ XML头部生成失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
echo "</div>\n";

// 第3步：测试网站数据查询
echo "<div class='section'>\n";
echo "<h2>第3步：测试网站数据查询</h2>\n";
try {
    $where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";
    $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
    $sql .= " WHERE $where ORDER BY web_id DESC LIMIT 5"; // 只取5个测试
    $query = $DB->query($sql);
    $results = array();
    $count = 0;
    while ($row = $DB->fetch_array($query)) {
        $row['web_link'] = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
        $results[] = $row;
        $count++;
    }
    $DB->free_result($query);
    
    echo "<div class='success'>\n";
    echo "<p>✓ 网站数据查询成功</p>\n";
    echo "<p>查询到 $count 个网站</p>\n";
    echo "</div>\n";
    
    if (!empty($results)) {
        echo "<h4>示例数据:</h4>\n";
        echo "<pre>";
        foreach (array_slice($results, 0, 2) as $row) {
            echo "ID: {$row['web_id']}, 名称: {$row['web_name']}, 链接: {$row['web_link']}\n";
        }
        echo "</pre>\n";
    }
} catch (Exception $e) {
    echo "<div class='error'>\n";
    echo "<p>✗ 网站数据查询失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
echo "</div>\n";

// 第4步：测试分类数据查询
echo "<div class='section'>\n";
echo "<h2>第4步：测试分类数据查询</h2>\n";
try {
    $categories = get_all_category();
    $webdir_count = 0;
    $article_count = 0;
    
    foreach ($categories as $cate) {
        if ($cate['cate_mod'] == 'webdir') $webdir_count++;
        if ($cate['cate_mod'] == 'article') $article_count++;
    }
    
    echo "<div class='success'>\n";
    echo "<p>✓ 分类数据查询成功</p>\n";
    echo "<p>总分类数量: " . count($categories) . "</p>\n";
    echo "<p>网站分类数量: $webdir_count</p>\n";
    echo "<p>文章分类数量: $article_count</p>\n";
    echo "</div>\n";
} catch (Exception $e) {
    echo "<div class='error'>\n";
    echo "<p>✗ 分类数据查询失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
echo "</div>\n";

// 第5步：测试完整的站点地图生成（但限制数量）
echo "<div class='section'>\n";
echo "<h2>第5步：测试完整站点地图生成（限制版本）</h2>\n";
try {
    ob_start();
    
    // 生成简化版本的站点地图
    header("Content-Type: application/xml;");
    echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 首页
    echo "<url>\n";
    echo "<loc>".$options['site_url']."</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>1.0</priority>\n";
    echo "</url>\n";
    
    // 只添加前3个网站
    $where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";
    $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
    $sql .= " WHERE $where ORDER BY web_id DESC LIMIT 3";
    $query = $DB->query($sql);
    while ($row = $DB->fetch_array($query)) {
        $web_link = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
        echo "<url>\n";
        echo "<loc>".$web_link."</loc>\n";
        echo "<lastmod>".date('c', $row['web_ctime'])."</lastmod>\n";
        echo "<changefreq>weekly</changefreq>\n";
        echo "<priority>0.6</priority>\n";
        echo "</url>\n";
    }
    $DB->free_result($query);
    
    echo "</urlset>\n";
    
    $output = ob_get_clean();
    
    $length = strlen($output);
    $url_count = substr_count($output, '<url>');
    $has_xml_start = strpos($output, '<?xml') === 0;
    $has_xml_end = strpos($output, '</urlset>') !== false;
    
    if ($has_xml_start && $has_xml_end && $url_count > 0) {
        echo "<div class='success'>\n";
        echo "<p>✓ 简化站点地图生成成功</p>\n";
        echo "<p>长度: $length 字符</p>\n";
        echo "<p>URL数量: $url_count</p>\n";
        echo "</div>\n";
    } else {
        echo "<div class='error'>\n";
        echo "<p>✗ 简化站点地图生成失败</p>\n";
        echo "<p>长度: $length 字符</p>\n";
        echo "<p>URL数量: $url_count</p>\n";
        echo "<p>XML开始: " . ($has_xml_start ? '✓' : '✗') . "</p>\n";
        echo "<p>XML结束: " . ($has_xml_end ? '✓' : '✗') . "</p>\n";
        echo "</div>\n";
    }
    
    echo "<h4>输出内容:</h4>\n";
    echo "<pre>" . htmlspecialchars($output) . "</pre>\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<div class='error'>\n";
    echo "<p>✗ 站点地图生成失败: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "</div>\n";
}
echo "</div>\n";

echo "</body>\n";
echo "</html>\n";
?>
