@charset "utf-8";
@import url("reset.css");

/* 基础响应式设置 */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

img {
    max-width: 100%;
    height: auto;
}

/* 响应式布局容器 */
#wrapper {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 响应式网格系统 */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

/* 响应式导航 */
#navbox {
    background: url(blue.png) repeat-x;
    width: 100%;
}

.navbar {
    display: flex;
    flex-wrap: wrap;
}

.navbar li {
    flex: 1;
    min-width: 100px;
    text-align: center;
    font: 14px/35px "微软雅黑";
}

.navbar li a {
    display: block;
    color: #fff;
    padding: 0 10px;
}

.navbar li a:hover {
    background: #0080c6;
}

/* 响应式搜索框 */
#sobox {
    width: 100%;
    padding: 20px 0;
}

.sofrm {
    display: flex;
    max-width: 600px;
    margin: 0 auto;
    padding: 12px;
    position: relative;
}

.sipt {
    flex: 1;
    height: 40px;
    padding: 0 10px 0 90px;
    border: 1px solid #dadada;
    border-radius: 4px;
    font-size: 14px;
}

.sbtn {
    width: 80px;
    height: 40px;
    margin-left: 10px;
    background: #65bc0b;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

/* 响应式列表 */
.sitelist li {
    display: flex;
    flex-wrap: wrap;
    padding: 15px 5px;
    border-bottom: 1px dashed #e8e8e8;
}

.sitelist li .thumb {
    width: 100px;
    margin-right: 15px;
}

.sitelist li .info {
    flex: 1;
    min-width: 200px;
}

/* 响应式卡片 */
.bestlist {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    padding: 15px;
}

.bestlist li {
    flex: 1;
    min-width: 200px;
    text-align: center;
}

/* 响应式布局盒子 */
#homebox-left,
#homebox-right,
#mainbox-left,
#mainbox-right {
    width: 100%;
}

/* 媒体查询 */
@media screen and (min-width: 768px) {
    #homebox-left {
        width: 30%;
    }
    
    #homebox-right {
        width: 68%;
    }
    
    #mainbox-left {
        width: 70%;
    }
    
    #mainbox-right {
        width: 28%;
    }
}

@media screen and (max-width: 767px) {
    .navbar li {
        width: 50%;
    }
    
    .sofrm {
        flex-direction: column;
    }
    
    .sipt {
        width: 100%;
        margin-bottom: 10px;
    }
    
    .sbtn {
        width: 100%;
        margin-left: 0;
    }
    
    .sitelist li .thumb {
        margin-bottom: 10px;
    }
    
    .sitelist li .info {
        width: 100%;
    }
}

/* 响应式表格 */
.weblink {
    width: 100%;
    overflow-x: auto;
}

.weblink table {
    min-width: 600px;
}

/* 响应式分页 */
.showpage {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    justify-content: center;
    padding: 15px 0;
}

/* 响应式文章内容 */
.content {
    width: 100%;
    overflow-wrap: break-word;
}

.content img {
    max-width: 100%;
    height: auto;
}

/* 响应式页脚 */
#footer {
    width: 100%;
    padding: 20px 15px;
    text-align: center;
}

/* 响应式工具类 */
.hidden-mobile {
    display: none;
}

@media screen and (min-width: 768px) {
    .hidden-mobile {
        display: block;
    }
}

/* 响应式字体 */
html {
    font-size: 16px;
}

@media screen and (max-width: 767px) {
    html {
        font-size: 14px;
    }
    
    h1 {
        font-size: 1.8rem;
    }
    
    h2 {
        font-size: 1.5rem;
    }
    
    h3 {
        font-size: 1.2rem;
    }
} 