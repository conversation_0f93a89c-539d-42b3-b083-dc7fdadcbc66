<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>

</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- 黑名单状态横幅 -->
    		<div style="background: linear-gradient(45deg, #dc3545, #c82333); color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
    			<div style="position: absolute; top: -10px; right: -10px; width: 80px; height: 80px; opacity: 0.3; font-size: 60px; transform: rotate(15deg);">⚠️</div>
    			<h2 style="margin: 0; font-size: 18px; font-weight: bold;">🛡️ 黑名单网站 - {#$web.category_name#}</h2>
    			<p style="margin: 5px 0 0 0; font-size: 14px;">该网站已被列入黑名单，存在安全风险或违规内容</p>
    			<p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.9;">拉黑时间：{#$web.blacklist_time_formatted#} | 操作员：{#$web.web_blacklist_operator#}</p>
    		</div>
        	<div id="siteinfo">
            	<h1 class="wtitle">
                    <span style="float: right; margin-top: 5px;">
                        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#dc3545;border-radius:1em;padding:5px 15px;">
                            {#$web.category_name#}
                        </span>
                    </span>
                    <em style="color: #dc3545;">{#$web.web_name#} - 黑名单网站</em>
                </h1>

				<ul class="wdata">
                    <li class="line"><em style="color: #dc3545;">黑名单</em>网站状态</li>
                    <li class="line"><em style="color: #dc3545;">{#$web.category_name#}</em>违规分类</li>
                    <li class="line"><em style="color: #666;">已隐藏</em>访问地址</li>
                    <li class="line"><em style="color: #666;">已屏蔽</em>搜索收录</li>
                    <li class="line"><em style="color: #666;">已禁用</em>访问统计</li>
                    <li class="line"><em style="color: #666;">已停止</em>推荐展示</li>
                    <li class="line"><em style="color: #666;">{#$web.web_blacklist_operator#}</em>操作员</li>
                    <li class="line"><em style="color: #666;">-</em>安全等级</li>
                    <li class="line"><em>{#$web.blacklist_time_formatted#}</em>拉黑时间</li>
                    <li class="line"><em>{#$web.ctime_formatted#}</em>提交时间</li>
                    <li class="line"><em>{#$web.web_ctime#}</em>提交日期</li>
                    <li class="line"><em style="color: #f60;">{#$web.web_utime#}</em>更新日期</li>
                </ul>

                <div class="blank10"></div>

                <div class="clearfix params" style="display: flex; align-items: flex-start; gap: 15px;">
                    <div style="position: relative; flex-shrink: 0;">
                        <img src="{#$web.web_pic#}" width="130" height="110" alt="{#$web.web_name#}" class="wthumb" />
                        <div style="position: absolute; top: 5px; right: 5px; width: 60px; height: 20px; background: #dc3545; color: white; text-align: center; font-size: 12px; line-height: 20px; border-radius: 3px; opacity: 0.9; z-index: 10;">黑名单</div>
                    </div>
                    <ul class="siteitem" style="flex: 1; margin: 0; padding: 0; list-style: none;">
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;">
                        <strong>网站地址：</strong>
                        <span style="color:#f00;font-size:12px;">隐私保护：网址信息已隐藏</span>
                        <!-- 黑名单状态标识 -->
                        <span style="line-height:20px;color:#FFF;font-size:12px;text-align:center;background:#dc3545;border-radius:1em;float:right;width:60px;margin-left:10px;">
                            黑名单
                        </span>
                    </li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>域名信息：</strong><span style="color: #666;">
                        <script>
                        document.write(function(){
                            var domain = '{#$web.domain#}';
                            if(domain.length > 8) {
                                return domain.substring(0, 3) + '****' + domain.substring(domain.length-3);
                            } else if(domain.length > 4) {
                                return domain.substring(0, 2) + '****' + domain.substring(domain.length-2);
                            } else {
                                return '****';
                            }
                        }());
                        </script>
                        <noscript>****</noscript>
                    </span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>服务器IP：</strong><span style="color: #666;">隐私保护</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>违规分类：</strong><span style="color: #dc3545; font-weight: bold;">{#$web.category_name#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>拉黑理由：</strong><span style="color: #856404; line-height: 23px;">{#$web.web_blacklist_reason#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>拉黑时间：</strong><span style="color: #666;">{#$web.blacklist_time_formatted#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>操作员：</strong><span style="color: #666;">{#$web.web_blacklist_operator#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>网站描述：</strong><span style="line-height: 23px;">{#$web.web_intro#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>TAG标签：</strong>{#foreach from=$web_tags item=item#}<a href="javascript:void(0)" style="background: #e9ecef; color: #495057; padding: 2px 6px; border-radius: 3px; font-size: 11px; margin-right: 5px; text-decoration: none;">{#$item.tag_name#}</a>{#/foreach#}</li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;">{#get_adcode(1)#}</li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>网站状态：</strong><span style="color: #dc3545; font-weight: bold;">该网站已被列入黑名单，存在安全风险</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0; border-bottom: 1px solid #f0f0f0;"><strong>提交时间：</strong><span style="color: #666;">{#$web.ctime_formatted#}</span></li>
                    <li style="margin-bottom: 8px; padding: 5px 0;"><strong>本页地址：</strong><a href="{#$site_url#}?mod=blacklist_detail&id={#$web.web_id#}" style="color: #007bff; text-decoration: none;">{#$site_url#}?mod=blacklist_detail&id={#$web.web_id#}</a></li>
                    </ul>
                </div>

                <div class="blank10"></div>

                <!-- 拉黑理由 -->
                <div class="params">
                    <h3 style="color: #dc3545;">⚠️ 拉黑理由</h3>
                    <div class="content" style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; color: #856404;">
                        {#$web.web_blacklist_reason#}
                    </div>
                </div>

                <div class="blank10"></div>

                <!-- 网站介绍 -->
                <div class="params">
                    <h3>网站介绍</h3>
                    <div class="content">
                        {#if $web.web_ai_intro#}
                            <div class="web_ai_intro">{#$web.web_ai_intro#}</div>
                            <div class="blank10"></div>
                        {#/if#}
                        {#$web.web_intro#}
                    </div>
                </div>

               <div class="blank10"></div>

                <!-- 同分类黑名单网站 -->
                 <!--<div class="params">
                    <h3>同分类黑名单</h3>
                    <div class="content">
                        {#if $related_blacklist#}
                        <ul class="rellist" style="list-style: none; padding: 0;">
                            {#foreach from=$related_blacklist item=rel#}
                            <li style="margin-bottom: 10px; padding: 8px; border: 1px solid #e9ecef; border-radius: 5px; background: #f8f9fa;">
                                <a href="?mod=blacklist_detail&id={#$rel.web_id#}" style="display: flex; align-items: center; text-decoration: none; color: #333;">
                                    <img src="{#$rel.web_pic#}" width="60" height="48" alt="{#$rel.web_name#}" style="margin-right: 10px; border-radius: 3px;" />
                                    <div>
                                        <strong style="color: #dc3545;">{#$rel.web_name#}</strong>
                                        <div style="font-size: 12px; color: #666; margin-top: 2px;">违规分类：{#$rel.category_name#}</div>
                                    </div>
                                </a>
                            </li>
                            {#foreachelse#}
                            <li style="text-align: center; color: #666; padding: 20px;">暂无其他同分类黑名单网站</li>
                            {#/foreach#}
                        </ul>
                        {#else#}
                        <p style="text-align: center; color: #666; padding: 20px;">暂无其他同分类黑名单网站</p>
                        {#/if#}
                    </div>
                </div>

                <div class="blank10"></div>-->
            <!-- 黑名单警告说明 -->
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; border-left: 4px solid #dc3545; border-radius: 5px; padding: 12px 15px; margin-bottom: 15px; color: #721c24; font-size: 13px;">
                <strong>⚠️ 安全警告：</strong>该网站已被列入黑名单，可能存在安全风险或违规内容。为保护用户安全，已隐藏具体网址和访问入口。请谨慎访问此类网站，建议选择其他优质网站。
            </div>

            </div>

            <div class="blank10"></div>

            <!-- 上一站下一站导航 -->
            <div class="website-navigation" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border: 1px solid #e9ecef;">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div class="prev-website" style="flex: 1; text-align: left;">
                        {#if $prev_website#}
                            <a href="{#$prev_website.web_link#}" style="color: #dc3545; text-decoration: none; font-size: 14px;"
                               onmouseover="this.style.color='#c82333'" onmouseout="this.style.color='#dc3545'">
                                <i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
                                上一站：{#$prev_website.web_name#}
                            </a>
                        {#else#}
                            <span style="color: #6c757d; font-size: 14px;">
                                <i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
                                上一站：暂无
                            </span>
                        {#/if#}
                    </div>

                    <div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
                        <span style="color: #6c757d; font-size: 12px;">黑名单导航</span>
                    </div>

                    <div class="next-website" style="flex: 1; text-align: right;">
                        {#if $next_website#}
                            <a href="{#$next_website.web_link#}" style="color: #dc3545; text-decoration: none; font-size: 14px;"
                               onmouseover="this.style.color='#c82333'" onmouseout="this.style.color='#dc3545'">
                                下一站：{#$next_website.web_name#}
                                <i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
                            </a>
                        {#else#}
                            <span style="color: #6c757d; font-size: 14px;">
                                下一站：暂无
                                <i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
                            </span>
                        {#/if#}
                    </div>
                </div>
            </div>

        </div>

        <div id="mainbox-right">

            <!-- VIP站点推荐 -->
            <div id="bestweb" class="mag">
                <h3>vip站点</h3>
                <ul class="weblist_b">
                    {#foreach from=get_websites(0, 10, 'views') item=quick#}
                    <li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 推荐资讯 -->
            <div id="bestart">
                <h3>推荐资讯</h3>
                <ul class="artlist_b">
                    {#foreach from=get_articles(0, 10) item=art#}
                    <li>[<em><a href="{#$art.cate_link#}" title="{#$art.cate_name#}">{#$art.cate_name#}</a></em>]<a href="{#$art.art_link#}">{#$art.art_title#}</a></li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 最新收录 -->
            <div id="bestart">
                <h3>最新收录 <span style="float: right; font-size: 12px; color: #999; font-weight: normal;">{#$smarty.now|date_format:"%Y-%m-%d"#}</span></h3>
                <ul class="artlist_b">
                    {#foreach from=get_websites(0, 10, 'ctime') item=new#}
                    <li>
                        <a href="{#$new.web_link#}" title="{#$new.web_name#}">{#$new.web_name#}</a>
                        <span style="float: right; font-size: 11px; color: #999;">{#$new.web_ctime|date_format:"%m-%d"#}</span>
                    </li>
                    {#foreachelse#}
                    <li style="color: #999; text-align: center;">暂无收录网站</li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 右侧广告位 -->
            <div class="ad250x250">
                {#get_adcode(2)#}
            </div>
        </div>
    </div>
</div>

{#include file="footer.html"#}
</body>
</html>
