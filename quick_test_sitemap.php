<?php
/**
 * 快速测试站点地图修复
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>快速站点地图测试</title>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>快速站点地图测试</h1>\n";

// 测试webdir站点地图
echo "<h2>测试 Webdir 站点地图</h2>\n";

try {
    ob_start();
    get_website_sitemap(0);
    $output = ob_get_clean();
    
    $length = strlen($output);
    $url_count = substr_count($output, '<url>');
    $has_xml_start = strpos($output, '<?xml') === 0;
    $has_xml_end = strpos($output, '</urlset>') !== false;
    
    echo "<p><strong>输出长度:</strong> $length 字符</p>\n";
    echo "<p><strong>URL数量:</strong> $url_count</p>\n";
    echo "<p><strong>XML开始:</strong> " . ($has_xml_start ? '✓' : '✗') . "</p>\n";
    echo "<p><strong>XML结束:</strong> " . ($has_xml_end ? '✓' : '✗') . "</p>\n";
    
    if ($has_xml_start && $has_xml_end && $url_count > 0) {
        echo "<p style='color: green;'><strong>✓ 测试通过</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ 测试失败</strong></p>\n";
    }
    
    // 显示前500个字符
    echo "<h3>输出预览:</h3>\n";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; max-height: 300px; overflow-y: auto;'>";
    echo htmlspecialchars(substr($output, 0, 500));
    if ($length > 500) {
        echo "\n... (还有 " . ($length - 500) . " 个字符)";
    }
    echo "</pre>\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red;'><strong>异常:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</body>\n";
echo "</html>\n";
?>
