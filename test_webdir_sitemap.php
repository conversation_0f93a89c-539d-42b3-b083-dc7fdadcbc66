<?php
/**
 * 测试webdir和weblink站点地图
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');
require(APP_PATH.'module/article.php');

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>Webdir站点地图测试</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }\n";
echo ".xml-output { background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; font-family: monospace; white-space: pre-wrap; max-height: 400px; overflow-y: auto; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>Webdir和Weblink站点地图测试</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

// 测试webdir站点地图
echo "<div class='test-section'>\n";
echo "<h2>Webdir站点地图测试</h2>\n";

try {
    ob_start();
    
    // 模拟请求参数
    $_GET['mod'] = 'sitemap';
    $_GET['type'] = 'webdir';
    $_GET['format'] = 'xml';
    $_GET['cid'] = 0;
    
    get_website_sitemap(0);
    
    $webdir_output = ob_get_clean();
    
    echo "<p><strong>输出长度:</strong> " . strlen($webdir_output) . " 字符</p>\n";
    echo "<p><strong>URL数量:</strong> " . substr_count($webdir_output, '<url>') . "</p>\n";
    
    // 检查XML格式
    $xml_valid = false;
    if (strpos($webdir_output, '<?xml') === 0 && strpos($webdir_output, '</urlset>') !== false) {
        $xml_valid = true;
        echo "<p style='color: green;'><strong>✓ XML格式正确</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ XML格式错误</strong></p>\n";
    }
    
    // 尝试解析XML
    libxml_use_internal_errors(true);
    $xml = simplexml_load_string($webdir_output);
    if ($xml !== false) {
        echo "<p style='color: green;'><strong>✓ XML解析成功</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ XML解析失败</strong></p>\n";
        $errors = libxml_get_errors();
        foreach ($errors as $error) {
            echo "<p style='color: red;'>错误: " . htmlspecialchars(trim($error->message)) . " (行: {$error->line})</p>\n";
        }
    }
    libxml_clear_errors();
    
    echo "<h3>XML输出内容:</h3>\n";
    echo "<div class='xml-output'>" . htmlspecialchars($webdir_output) . "</div>\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red;'><strong>异常:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</div>\n";

// 测试weblink站点地图
echo "<div class='test-section'>\n";
echo "<h2>Weblink站点地图测试</h2>\n";

try {
    ob_start();
    
    // 模拟请求参数
    $_GET['mod'] = 'sitemap';
    $_GET['type'] = 'weblink';
    $_GET['format'] = 'xml';
    $_GET['cid'] = 0;
    
    get_website_sitemap(0);
    
    $weblink_output = ob_get_clean();
    
    echo "<p><strong>输出长度:</strong> " . strlen($weblink_output) . " 字符</p>\n";
    echo "<p><strong>URL数量:</strong> " . substr_count($weblink_output, '<url>') . "</p>\n";
    
    // 检查XML格式
    $xml_valid = false;
    if (strpos($weblink_output, '<?xml') === 0 && strpos($weblink_output, '</urlset>') !== false) {
        $xml_valid = true;
        echo "<p style='color: green;'><strong>✓ XML格式正确</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ XML格式错误</strong></p>\n";
    }
    
    // 尝试解析XML
    libxml_use_internal_errors(true);
    $xml = simplexml_load_string($weblink_output);
    if ($xml !== false) {
        echo "<p style='color: green;'><strong>✓ XML解析成功</strong></p>\n";
    } else {
        echo "<p style='color: red;'><strong>✗ XML解析失败</strong></p>\n";
        $errors = libxml_get_errors();
        foreach ($errors as $error) {
            echo "<p style='color: red;'>错误: " . htmlspecialchars(trim($error->message)) . " (行: {$error->line})</p>\n";
        }
    }
    libxml_clear_errors();
    
    echo "<h3>XML输出内容:</h3>\n";
    echo "<div class='xml-output'>" . htmlspecialchars($weblink_output) . "</div>\n";
    
} catch (Exception $e) {
    ob_end_clean();
    echo "<p style='color: red;'><strong>异常:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "</div>\n";

echo "</body>\n";
echo "</html>\n";
?>
