<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>

</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- 审核不通过状态横幅 -->
    		<div style="background: linear-gradient(45deg, #f39c12, #e67e22); color: white; padding: 15px; border-radius: 8px; margin-bottom: 15px; text-align: center; position: relative; overflow: hidden; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
    			<div style="position: absolute; top: -10px; right: -10px; width: 80px; height: 80px; opacity: 0.3; font-size: 60px; transform: rotate(15deg);">❌</div>
    			<h2 style="margin: 0; font-size: 18px; font-weight: bold;">🔍 审核不通过</h2>
    			<p style="margin: 5px 0 0 0; font-size: 14px;">该网站未通过人工审核，暂时无法收录</p>
    			{#if $web.web_reject_reason#}
    			<p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.9;">不通过原因：{#$web.web_reject_reason#}</p>
    			{#/if#}
    		</div>
        	<div id="siteinfo">
            	<h1 class="wtitle">
                    <span style="float: right; margin-top: 5px;">
                        <span style="line-height:20px;color:#FFF;font-size:14px;text-align:center;background:#f39c12;border-radius:1em;padding:5px 15px;">
                            审核不通过
                        </span>
                    </span>
                    <em style="color: #f39c12;">{#$web.web_name#} - 审核不通过</em>
                </h1>

				<ul class="wdata">
                    <li class="line"><em style="color: #f39c12;">审核不通过</em>网站状态</li>
                    <li class="line"><em style="color: #666;">{#$cate_name#}</em>提交分类</li>
                    <li class="line"><em style="color: #666;">已隐藏</em>访问地址</li>
                    <li class="line"><em style="color: #666;">未收录</em>搜索引擎</li>
                    <li class="line"><em style="color: #666;">未开启</em>访问统计</li>
                    <li class="line"><em style="color: #666;">未展示</em>推荐位置</li>
                    <li class="line"><em style="color: #666;">-</em>审核状态</li>
                    <li class="line"><em style="color: #666;">-</em>收录等级</li>
                    <li class="line"><em>{#$web.ctime_formatted#}</em>提交时间</li>
                    <li class="line"><em>{#$web.web_utime#}</em>更新时间</li>
                </ul>
            </div>

            <div class="blank10"></div>

            <!-- 审核不通过原因详情 -->
            <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 5px; margin-bottom: 15px;">
                <h3 style="margin: 0 0 10px 0; color: #f39c12;">📋 审核不通过详情</h3>
                {#if $web.web_reject_reason#}
                <p><strong>不通过原因：</strong>{#$web.web_reject_reason#}</p>
                {#else#}
                <p><strong>不通过原因：</strong>该网站未通过审核标准</p>
                {#/if#}
                <p><strong>提交时间：</strong>{#$web.ctime_formatted#}</p>
                <p><strong>所属分类：</strong>{#$cate_name#}</p>
                <p><strong>处理建议：</strong>网站提交者可以根据不通过原因进行整改后重新提交</p>
            </div>

            <div class="web_ai_intro">关于【{#$web.web_name#}】的详细介绍</div>
            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
        	    <div style="text-align: center; padding: 20px; background: #f8f9fa; border-radius: 8px; margin-bottom: 15px;">
        	        <div style="font-size: 80px; color: #f39c12; margin-bottom: 10px;">❌</div>
        	        <p style="margin: 10px 0 0 0; color: #666; font-size: 14px;">该网站审核不通过，无法提供访问</p>
        	    </div>
        	    <div class="blank10"></div>
				{#if $web.web_intro#}
				<p>{#$web.web_intro#}</p>
				{#else#}
				<p>该网站未通过审核，暂无详细介绍。</p>
				{#/if#}
			</div>

			<div class="blank10"></div>
            <div class="web_ai_intro">注意事项：网站审核不通过可能由多种原因造成，建议网站管理员根据反馈进行整改。</div>
            <div class="blank10"></div>
			<div id="relsite" class="clearfix">
                <p><strong>{#$web.web_name#}</strong>于{#$web.ctime_formatted#}被提交到{#$cate_name#}分类目录，但未通过审核。{#if $web.web_reject_reason#}不通过原因：{#$web.web_reject_reason#}。{#/if#}网站提交者可以根据审核意见进行整改后重新提交。如果您认为审核结果有误，请联系网站管理员进行申诉。</p>
            </div>

            <!-- 隐私保护警告 -->
            <div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 15px; border-radius: 5px; margin: 15px 0; font-size: 13px;">
                <strong>🔒 隐私保护提醒：</strong>
                <ul style="margin: 5px 0 0 20px; padding: 0;">
                    <li>该网站审核不通过，网址信息已被隐藏</li>
                    <li>不提供任何访问入口和跳转链接</li>
                    <li>网站数据仅供审核参考使用</li>
                    <li>如需申诉请联系网站管理员</li>
                </ul>
            </div>

            <!-- 相关审核不通过网站 -->
            {#if $related_rejected#}
            <div class="blank10"></div>
            <div class="web_ai_intro">同分类其他审核不通过网站</div>
            <div class="blank10"></div>
            <div id="relsite" class="clearfix">
                <ul class="weblist_c">
                    {#foreach from=$related_rejected item=rel#}
                    <li>
                        <a href="{#$rel.web_link#}">
                            <img src="{#$rel.web_pic#}" width="100" height="80" alt="{#$rel.web_name#}" />
                        </a>
                        <strong><a href="{#$rel.web_link#}" title="{#$rel.web_name#}">{#$rel.web_name#}</a></strong>
                        <p>该网站审核不通过</p>
                    </li>
                    {#/foreach#}
                </ul>
            </div>
            {#/if#}

            <!-- 上一站下一站 -->
            {#if $prev_website || $next_website#}
            <div class="blank10"></div>
            <div class="prevnext">
                {#if $prev_website#}
                <span class="prev"><a href="{#$prev_website.web_link#}">上一站：{#$prev_website.web_name#}</a></span>
                {#/if#}
                {#if $next_website#}
                <span class="next"><a href="{#$next_website.web_link#}">下一站：{#$next_website.web_name#}</a></span>
                {#/if#}
            </div>
            {#/if#}

        </div>

        <div id="mainbox-right">
        	<div id="bestweb" class="mag">
            	<h3>vip站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 10, 'views') item=quick#}
                   	<li><a href="{#$quick.web_link#}"><img src="{#$quick.web_pic#}" width="100" height="80" alt="{#$quick.web_name#}" /></a><strong><a href="{#$quick.web_link#}" title="{#$quick.web_name#}">{#$quick.web_name#}</a></strong><p>{#$quick.web_intro#}</p><address><a href="{#$quick.web_furl#}" target="_blank" class="visit" onClick="clickout({#$quick.web_id#})">{#$quick.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>
            <div id="bestweb">
            	<h3>推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li><a href="{#$best.web_link#}"><img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" /></a><strong><a href="{#$best.web_link#}" title="{#$best.web_name#}">{#$best.web_name#}</a></strong><p>{#$best.web_intro#}</p><address><a href="/go.php?url=http://{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})">{#$best.web_url#}</a></address></li>
                   	{#/foreach#}
               	</ul>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

</body>
</html>
