/**
 * SVG图片显示修复JavaScript
 * 解决网站详情页SVG图片不显示的问题
 */

(function() {
    'use strict';
    
    // SVG支持检测
    function supportsSVG() {
        var div = document.createElement('div');
        div.innerHTML = '<svg/>';
        return (div.firstChild && div.firstChild.namespaceURI) === 'http://www.w3.org/2000/svg';
    }
    
    // 添加浏览器支持类
    function addBrowserClasses() {
        var html = document.documentElement;
        if (supportsSVG()) {
            html.className += ' svg';
        } else {
            html.className += ' no-svg';
        }
        
        // 检测IE浏览器
        if (navigator.userAgent.indexOf('MSIE') !== -1 || navigator.userAgent.indexOf('Trident/') !== -1) {
            html.className += ' ie';
        }
    }
    
    // SVG图片处理函数
    function processSVGImages() {
        var svgImages = document.querySelectorAll('img[src$=".svg"]');
        
        svgImages.forEach(function(img, index) {
            // 添加加载状态
            img.setAttribute('data-svg-index', index);
            
            // 处理加载成功
            img.onload = function() {
                this.setAttribute('data-loaded', 'true');
                this.removeAttribute('data-error');
                console.log('SVG加载成功:', this.src);
            };
            
            // 处理加载失败
            img.onerror = function() {
                this.setAttribute('data-error', 'true');
                this.removeAttribute('data-loaded');
                console.error('SVG加载失败:', this.src);
                
                // 尝试降级处理
                handleSVGError(this);
            };
            
            // 如果图片已经加载完成
            if (img.complete) {
                if (img.naturalWidth === 0) {
                    img.onerror();
                } else {
                    img.onload();
                }
            }
        });
    }
    
    // SVG加载失败处理 - 增强版
    function handleSVGError(img) {
        var originalSrc = img.src;
        var fallbackAttempts = img.getAttribute('data-fallback-attempts') || 0;
        fallbackAttempts = parseInt(fallbackAttempts) + 1;
        img.setAttribute('data-fallback-attempts', fallbackAttempts);

        console.error('SVG加载失败 (尝试 #' + fallbackAttempts + '):', originalSrc);

        // 尝试不同的降级方案
        if (fallbackAttempts === 1) {
            // 方案1: 重新加载 (可能是临时网络问题)
            setTimeout(function() {
                console.log('重新尝试加载SVG:', originalSrc);
                img.src = originalSrc + '?retry=' + Date.now();
            }, 1000);
        } else if (fallbackAttempts === 2) {
            // 方案2: 尝试添加缓存破坏参数
            var cacheBustSrc = originalSrc.split('?')[0] + '?v=' + Date.now();
            console.log('尝试缓存破坏:', cacheBustSrc);
            img.src = cacheBustSrc;
        } else if (fallbackAttempts === 3) {
            // 方案3: 尝试PNG替换
            var pngSrc = originalSrc.replace('.svg', '.png').split('?')[0];
            console.log('尝试PNG降级:', pngSrc);
            img.src = pngSrc;
        } else if (fallbackAttempts === 4) {
            // 方案4: 尝试JPG替换
            var jpgSrc = originalSrc.replace('.svg', '.jpg').replace('.png', '.jpg').split('?')[0];
            console.log('尝试JPG降级:', jpgSrc);
            img.src = jpgSrc;
        } else if (fallbackAttempts === 5) {
            // 方案5: 使用默认图片
            var defaultImg = img.getAttribute('data-default') ||
                            img.getAttribute('data-fallback') ||
                            '/public/images/nopic.gif';
            console.log('使用默认图片:', defaultImg);
            img.src = defaultImg;
        } else {
            // 方案6: 创建占位符
            console.log('创建SVG占位符');
            createPlaceholder(img);
        }
    }
    
    // 创建占位符
    function createPlaceholder(img) {
        var width = img.getAttribute('width') || img.offsetWidth || 130;
        var height = img.getAttribute('height') || img.offsetHeight || 110;
        var alt = img.getAttribute('alt') || '图片';
        
        // 创建SVG占位符
        var placeholderSVG = 'data:image/svg+xml;base64,' + btoa(
            '<svg width="' + width + '" height="' + height + '" xmlns="http://www.w3.org/2000/svg">' +
            '<rect width="' + width + '" height="' + height + '" fill="#f5f5f5" stroke="#ddd" stroke-width="2"/>' +
            '<text x="50%" y="50%" text-anchor="middle" dy="0.3em" font-size="12" fill="#999">' + alt + '</text>' +
            '</svg>'
        );
        
        img.src = placeholderSVG;
        img.setAttribute('data-placeholder', 'true');
        console.log('创建占位符:', width + 'x' + height);
    }
    
    // 预加载关键SVG
    function preloadSVGs() {
        var criticalSVGs = [
            '/public/images/nopic.gif' // 默认图片
        ];
        
        criticalSVGs.forEach(function(src) {
            var link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = src;
            document.head.appendChild(link);
        });
    }
    
    // 延迟加载SVG
    function lazyLoadSVGs() {
        if ('IntersectionObserver' in window) {
            var lazyImageObserver = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        var dataSrc = img.getAttribute('data-src');
                        if (dataSrc) {
                            img.src = dataSrc;
                            img.removeAttribute('data-src');
                            lazyImageObserver.unobserve(img);
                        }
                    }
                });
            });
            
            var lazyImages = document.querySelectorAll('img[data-src$=".svg"]');
            lazyImages.forEach(function(img) {
                lazyImageObserver.observe(img);
            });
        }
    }
    
    // 添加调试功能
    function addDebugFeatures() {
        // 添加调试快捷键
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+S 切换SVG调试模式
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                document.body.classList.toggle('debug-svg');
                console.log('SVG调试模式:', document.body.classList.contains('debug-svg') ? '开启' : '关闭');
            }
        });
        
        // 添加右键菜单
        document.addEventListener('contextmenu', function(e) {
            if (e.target.tagName === 'IMG' && e.target.src.endsWith('.svg')) {
                e.preventDefault();
                showSVGInfo(e.target);
            }
        });
    }
    
    // 显示SVG信息
    function showSVGInfo(img) {
        var info = [
            'SVG图片信息:',
            '原始路径: ' + img.getAttribute('data-original-src') || img.src,
            '当前路径: ' + img.src,
            '尺寸: ' + (img.naturalWidth || img.offsetWidth) + 'x' + (img.naturalHeight || img.offsetHeight),
            '加载状态: ' + (img.getAttribute('data-loaded') ? '成功' : '失败'),
            '降级次数: ' + (img.getAttribute('data-fallback-attempts') || 0)
        ].join('\n');
        
        alert(info);
    }
    
    // 修复网站详情页特定问题
    function fixSiteinfoSVG() {
        // 确保网站缩略图正确显示
        var siteThumb = document.querySelector('.wthumb img, img.wthumb');
        if (siteThumb && siteThumb.src.endsWith('.svg')) {
            // 设置固定尺寸
            siteThumb.style.width = '130px';
            siteThumb.style.height = '110px';
            siteThumb.style.objectFit = 'contain';
            siteThumb.style.display = 'block';
        }
        
        // 修复相关网站列表
        var relatedImages = document.querySelectorAll('.rellist li img[src$=".svg"]');
        relatedImages.forEach(function(img) {
            img.style.width = '100px';
            img.style.height = '80px';
            img.style.objectFit = 'contain';
        });
    }
    
    // 性能监控
    function monitorPerformance() {
        var svgLoadTimes = [];
        var svgImages = document.querySelectorAll('img[src$=".svg"]');
        
        svgImages.forEach(function(img) {
            var startTime = performance.now();
            
            img.addEventListener('load', function() {
                var loadTime = performance.now() - startTime;
                svgLoadTimes.push(loadTime);
                console.log('SVG加载时间:', loadTime.toFixed(2) + 'ms', img.src);
            });
        });
        
        // 页面加载完成后统计
        window.addEventListener('load', function() {
            if (svgLoadTimes.length > 0) {
                var avgTime = svgLoadTimes.reduce(function(a, b) { return a + b; }) / svgLoadTimes.length;
                console.log('SVG平均加载时间:', avgTime.toFixed(2) + 'ms');
            }
        });
    }
    
    // 初始化函数
    function init() {
        // 添加浏览器支持检测
        addBrowserClasses();
        
        // 预加载关键资源
        preloadSVGs();
        
        // 处理现有SVG图片
        processSVGImages();
        
        // 修复网站详情页特定问题
        fixSiteinfoSVG();
        
        // 延迟加载
        lazyLoadSVGs();
        
        // 性能监控
        if (window.console && console.log) {
            monitorPerformance();
        }
        
        // 调试功能（仅在开发环境）
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('test')) {
            addDebugFeatures();
        }
        
        // 监听动态添加的图片
        if ('MutationObserver' in window) {
            var observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            var svgImages = node.querySelectorAll ? node.querySelectorAll('img[src$=".svg"]') : [];
                            if (node.tagName === 'IMG' && node.src && node.src.endsWith('.svg')) {
                                svgImages = [node];
                            }
                            
                            if (svgImages.length > 0) {
                                Array.prototype.forEach.call(svgImages, function(img) {
                                    processSVGImages();
                                });
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });
        }
        
        console.log('SVG修复脚本已初始化');
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }
    
    // 导出到全局作用域（用于调试）
    window.SVGFix = {
        processSVGImages: processSVGImages,
        supportsSVG: supportsSVG,
        createPlaceholder: createPlaceholder,
        showSVGInfo: showSVGInfo
    };
    
})();
