<?php
/**
 * 付费价格管理模块
 */

/**
 * 获取当前有效的价格配置
 * @param int $service_type 服务类型：1=VIP，2=推荐，3=快审
 * @param int $timestamp 指定时间戳，默认为当前时间
 * @return array|false 价格配置信息
 */
function get_current_price($service_type, $timestamp = null) {
    global $DB;
    
    if ($timestamp === null) {
        $timestamp = time();
    }
    
    $table = $DB->table('payment_prices');
    $sql = "SELECT * FROM $table 
            WHERE service_type = $service_type 
            AND effective_time <= $timestamp 
            AND status = 1 
            ORDER BY effective_time DESC 
            LIMIT 1";
    
    return $DB->fetch_one($sql);
}

/**
 * 获取所有服务类型的当前价格
 * @return array 所有服务的价格配置
 */
function get_all_current_prices() {
    $prices = array();
    for ($i = 1; $i <= 3; $i++) {
        $price = get_current_price($i);
        if ($price) {
            $prices[$i] = $price;
        }
    }
    return $prices;
}

/**
 * 添加新的价格配置
 * @param int $service_type 服务类型
 * @param string $service_name 服务名称
 * @param float $price 价格
 * @param string $unit 单位
 * @param int $duration_days 服务时长（天数）
 * @param int $effective_time 生效时间
 * @param string $operator 操作员
 * @param string $remark 备注
 * @return bool 是否成功
 */
function add_price_config($service_type, $service_name, $price, $unit, $duration_days, $effective_time, $operator, $remark = '') {
    global $DB;
    
    $table = $DB->table('payment_prices');
    $data = array(
        'service_type' => intval($service_type),
        'service_name' => addslashes($service_name),
        'price' => floatval($price),
        'unit' => addslashes($unit),
        'duration_days' => intval($duration_days),
        'effective_time' => intval($effective_time),
        'status' => 1,
        'operator' => addslashes($operator),
        'remark' => addslashes($remark),
        'created_at' => time()
    );
    
    return $DB->insert($table, $data);
}

/**
 * 更新价格配置状态
 * @param int $id 配置ID
 * @param int $status 状态
 * @return bool 是否成功
 */
function update_price_status($id, $status) {
    global $DB;
    
    $table = $DB->table('payment_prices');
    return $DB->update($table, array('status' => intval($status)), array('id' => intval($id)));
}

/**
 * 获取价格历史记录
 * @param int $service_type 服务类型，0表示所有类型
 * @return array 价格历史记录
 */
function get_price_history($service_type = 0) {
    global $DB;
    
    $table = $DB->table('payment_prices');
    $where = $service_type > 0 ? "WHERE service_type = $service_type" : "";
    $sql = "SELECT * FROM $table $where ORDER BY service_type ASC, effective_time DESC";
    
    $query = $DB->query($sql);
    $history = array();
    while ($row = $DB->fetch_array($query)) {
        $row['effective_date'] = date('Y-m-d H:i:s', $row['effective_time']);
        $row['created_date'] = date('Y-m-d H:i:s', $row['created_at']);
        $row['status_text'] = $row['status'] ? '有效' : '已停用';
        $history[] = $row;
    }
    
    return $history;
}

/**
 * 计算付费金额和到期时间
 * @param int $service_type 服务类型
 * @param int $quantity 数量（月数、年数等）
 * @param int $current_expire 当前到期时间
 * @return array 包含金额和新到期时间
 */
function calculate_payment($service_type, $quantity = 1, $current_expire = 0) {
    $price_config = get_current_price($service_type);
    if (!$price_config) {
        return false;
    }
    
    $current_time = time();
    $amount = $price_config['price'] * $quantity;
    
    // 计算新的到期时间
    $new_expire = 0;
    if ($price_config['duration_days'] > 0) {
        $extend_seconds = $price_config['duration_days'] * 24 * 3600 * $quantity;
        $base_time = max($current_expire, $current_time);
        $new_expire = $base_time + $extend_seconds;
    }
    
    return array(
        'amount' => $amount,
        'new_expire' => $new_expire,
        'price_config' => $price_config
    );
}

/**
 * 获取服务类型名称
 * @param int $service_type 服务类型
 * @return string 服务名称
 */
function get_service_type_name($service_type) {
    $names = array(
        1 => 'VIP服务',
        2 => '推荐服务',
        3 => '快审服务'
    );
    return isset($names[$service_type]) ? $names[$service_type] : '未知服务';
}

/**
 * 检查价格配置是否存在冲突
 * @param int $service_type 服务类型
 * @param int $effective_time 生效时间
 * @param int $exclude_id 排除的配置ID
 * @return bool 是否存在冲突
 */
function check_price_conflict($service_type, $effective_time, $exclude_id = 0) {
    global $DB;
    
    $table = $DB->table('payment_prices');
    $where = "service_type = $service_type AND effective_time = $effective_time AND status = 1";
    if ($exclude_id > 0) {
        $where .= " AND id != $exclude_id";
    }
    
    $count = $DB->get_count($table, $where);
    return $count > 0;
}

/**
 * 获取指定时间的价格配置（用于历史记录查询）
 * @param int $service_type 服务类型
 * @param int $timestamp 时间戳
 * @return array|false 价格配置
 */
function get_price_at_time($service_type, $timestamp) {
    global $DB;
    
    $table = $DB->table('payment_prices');
    $sql = "SELECT * FROM $table 
            WHERE service_type = $service_type 
            AND effective_time <= $timestamp 
            AND status = 1 
            ORDER BY effective_time DESC 
            LIMIT 1";
    
    return $DB->fetch_one($sql);
}

/**
 * 格式化价格显示
 * @param float $price 价格
 * @param string $unit 单位
 * @return string 格式化后的价格字符串
 */
function format_price_display($price, $unit = '') {
    $formatted = '￥' . number_format($price, 2);
    if (!empty($unit)) {
        $formatted .= '/' . $unit;
    }
    return $formatted;
}
?>
