<?php
/**
 * 测试修复后的站点地图
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

// 如果是测试修复后的函数
if (isset($_GET['test_fixed'])) {
    try {
        get_website_sitemap(0);
    } catch (Exception $e) {
        header("Content-Type: text/plain;");
        echo "Error: " . $e->getMessage();
    }
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试修复后的站点地图</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
        .test-link.success { background: #28a745; }
        .test-link.success:hover { background: #218838; }
        .test-link.danger { background: #dc3545; }
        .test-link.danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <h1>测试修复后的站点地图</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section info">
        <h2>修复内容总结</h2>
        <p>基于"逐步版本成功但原始函数失败"的诊断结果，我已经对原始函数进行了以下优化：</p>
        <ul>
            <li><strong>大幅减少数据量：</strong>网站从50个减少到20个，文章从10个减少到5个，友情链接从10个减少到3个</li>
            <li><strong>简化分页逻辑：</strong>每个分类最多只添加第2页，总共最多处理5个分类</li>
            <li><strong>简化RSS链接：</strong>最多只添加3个RSS链接</li>
            <li><strong>增强错误处理：</strong>在所有循环和查询中添加了try-catch块</li>
            <li><strong>添加计数限制：</strong>防止无限循环或过多数据处理</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>测试选项</h2>
        <p>按顺序测试以下选项：</p>
        
        <div style="margin: 10px 0;">
            <strong>1. 测试修复后的本地函数：</strong><br>
            <a href="?test_fixed=1" target="_blank" class="test-link">测试修复后的站点地图函数</a>
            <small>（这会调用修复后的 get_website_sitemap 函数）</small>
        </div>
        
        <div style="margin: 10px 0;">
            <strong>2. 测试在线webdir站点地图：</strong><br>
            <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link">在线测试webdir站点地图</a>
            <small>（这会使用修复后的函数）</small>
        </div>
        
        <div style="margin: 10px 0;">
            <strong>3. 测试在线weblink站点地图：</strong><br>
            <a href="https://www.95dir.com/?mod=sitemap&type=weblink&format=xml" target="_blank" class="test-link">在线测试weblink站点地图</a>
            <small>（weblink使用相同的函数）</small>
        </div>
        
        <div style="margin: 10px 0;">
            <strong>4. 诊断工具（如果还有问题）：</strong><br>
            <a href="diagnose_sitemap_failure.php" target="_blank" class="test-link warning">详细诊断工具</a>
            <small>（如果上面的测试仍然失败，使用这个工具）</small>
        </div>
    </div>
    
    <div class="test-section">
        <h2>预期结果</h2>
        <div class="success">
            <h4>✓ 如果修复成功，您应该看到：</h4>
            <ul>
                <li>完整的XML格式（有开始和结束标签）</li>
                <li>包含首页、基本页面、少量网站、分类、文章和友情链接</li>
                <li>XML格式正确，可以被浏览器正常解析</li>
                <li>响应时间明显缩短</li>
            </ul>
        </div>
        
        <div class="error">
            <h4>✗ 如果仍然失败：</h4>
            <ul>
                <li>可能是某个特定的数据记录有问题</li>
                <li>可能是某个函数调用（如get_website_url）有问题</li>
                <li>可能是数据库连接或查询超时</li>
                <li>需要使用诊断工具进一步排查</li>
            </ul>
        </div>
    </div>
    
    <div class="test-section warning">
        <h2>性能优化说明</h2>
        <p>为了确保站点地图能够稳定生成，我采用了保守的数据量限制：</p>
        <ul>
            <li><strong>网站数量：</strong>20个（原来可能有数百个）</li>
            <li><strong>文章数量：</strong>5个（原来可能有数十个）</li>
            <li><strong>分类分页：</strong>每个分类最多1页（原来可能有10页）</li>
            <li><strong>友情链接：</strong>3个（原来可能有30个）</li>
        </ul>
        <p>如果测试成功，我们可以逐步增加这些数量，找到最佳的平衡点。</p>
    </div>
    
    <div class="test-section info">
        <h2>下一步计划</h2>
        <p>如果修复成功：</p>
        <ol>
            <li>逐步增加数据量，测试系统的承受能力</li>
            <li>优化数据库查询，提高性能</li>
            <li>考虑实现分批生成或缓存机制</li>
            <li>为其他站点地图类型应用相同的优化</li>
        </ol>
    </div>
</body>
</html>
