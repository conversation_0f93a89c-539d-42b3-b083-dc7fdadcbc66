<!doctype html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>KindEditor Toolbar Test</title>
		<link rel="stylesheet" href="../lib/qunit/qunit.css" />
		<!-- <script src="../lib/firebug-lite/build/firebug-lite.js#startOpened"></script> -->
		<script src="../lib/qunit/qunit.js"></script>
		<link rel="stylesheet" href="../themes/default/default.css" />
		<!-- include src files -->
		<script src="../src/core.js"></script>
		<script src="../src/config.js"></script>
		<script src="../src/event.js"></script>
		<script src="../src/html.js"></script>
		<script src="../src/selector.js"></script>
		<script src="../src/node.js"></script>
		<script src="../src/range.js"></script>
		<script src="../src/cmd.js"></script>
		<script src="../src/widget.js"></script>
		<script src="../src/toolbar.js"></script>
	</head>
	<body>
		<h1>KindEditor Toolbar Test</h1>
		<div id="toolbar"></div>
		<br />
		<br />
		<input type="button" id="enable" value="Enable" />
		<input type="button" id="disable" value="Disable" />
		<input type="button" id="toggle" value="Toggle enable/disable" />
		<input type="button" id="select" value="Select bold" />
		<input type="button" id="unselect" value="Unelect bold" />
		<script src="toolbar.js"></script>
	</body>
</html>
