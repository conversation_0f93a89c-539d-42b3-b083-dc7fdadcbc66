<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/svg-fix.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}themes/default/skin/vip-style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />
{#include file="script.html"#}
<script type="text/javascript" src="{#$site_root#}themes/default/skin/svg-fix.js"></script>
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>


</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
	{#include file="header.html"#}
    <div id="mainbox" class="clearfix">
    	<div id="mainbox-left">
    		<!-- VIP状态横幅 -->
    		<div class="vip-header vip-floating-card vip-stars">
    			<div class="vip-crown vip-sparkle">👑</div>
    			<h2 class="vip-title vip-gradient-text">VIP尊享网站 - {#$cate_name#}</h2>
    			<p class="vip-subtitle">享受优质服务，体验尊贵特权</p>
    			<div style="margin-top: 18px;">
    				<span class="vip-badge vip-pulse">{#$web.vip_level#}</span>
    			</div>
    		</div>

        	<div class="vip-content vip-glow">
            	<h1 class="wtitle">
                    <span style="float: right; margin-top: 5px;">
                        <a href="{#$web.web_links#}" target="_blank" onClick="clickout({#$web.web_id#})" class="vip-access-button">
                            立即访问
                        </a>
                    </span>
                    <a href="{#$web.web_links#}" target="_blank" onClick="clickout({#$web.web_id#})">
                        <em class="vip-gradient-text" style="font-size: 22px;">👑 {#$web.web_name#} - VIP官网入口</em>
                    </a>
                </h1>

				<!-- VIP统计数据 -->
				<div class="vip-stats">
                    <div class="vip-stat-item">
                        <span class="vip-stat-number">{#$web.web_views#}</span>
                        <div class="vip-stat-label">👁️ 人气指数</div>
                    </div>
                    <div class="vip-stat-item">
                        <span class="vip-stat-number">{#$web.web_grank#}</span>
                        <div class="vip-stat-label">📊 百度收录</div>
                    </div>
                    <div class="vip-stat-item">
                        <span class="vip-stat-number">{#$web.web_instat#}</span>
                        <div class="vip-stat-label">📈 入站次数</div>
                    </div>
                    <div class="vip-stat-item">
                        <span class="vip-stat-number">{#$web.web_outstat#}</span>
                        <div class="vip-stat-label">📉 出站次数</div>
                    </div>
                </div>

				<div class="clearfix params">
					<div style="position: relative; display: inline-block;">
						<img src="{#$web.web_pic#}" width="130" height="110" alt="{#$web.web_name#}" class="wthumb" style="border: 3px solid #ffd700; border-radius: 10px;" />
						<div style="position: absolute; top: -10px; right: -10px; background: #ffd700; color: #333; padding: 5px 10px; border-radius: 15px; font-size: 12px; font-weight: bold; box-shadow: 0 2px 8px rgba(0,0,0,0.3);">VIP</div>
					</div>
					<ul class="siteitem">
						<li>
                            <strong>🌐 网站地址：</strong>
                            <a href="{#$web.web_links#}" target="_blank" onClick="clickout({#$web.web_id#})" style="color: #28a745; font-weight: bold;">
                                {#$web.web_url#}
                            </a>
                            <span style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; padding: 3px 8px; border-radius: 12px; font-size: 11px; margin-left: 10px; font-weight: bold;">
                                ⚡ VIP直达
                            </span>
                        </li>
            			<li><strong>🖥️ 服务器IP：</strong>{#$web.web_ip#}</li>
                        <li><strong>📝 网站描述：</strong><span style="line-height: 23px;">{#$web.web_intro#}</span></li>
                        <li><strong>⚖️ 综合权重：</strong>
                            <span><img src="https://baidurank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="百度权重" /></span>
                            <span><img src="https://baidurank.aizhan.com/api/mbr?domain={#$web.web_url#}&style=images" alt="百度移动权重" /></span>
                            <span><img src="https://sogourank.aizhan.com/api/br?domain={#$web.web_url#}&style=images" alt="搜狗权重" /></span>
                        </li>
                        <li><strong>📋 备案信息：</strong><script type="text/javascript" src="https://icp.aizhan.com/geticp/?host={#$web.web_url#}&amp;style=text" charset="utf-8"></script></li>
                        <li><strong>👤 联系站长：</strong><a href="http://wpa.qq.com/msgrd?v=3&amp;uin={#$user.user_qq#}&amp;site={#$user.nick_name#}&amp;menu=yes" target="_blank"><img border="0" alt="点击这里给我发消息" src="http://wpa.qq.com/pa?p=2:{#$user.user_qq#}:41"></a></li>
                        <li><strong>🏷️ TAG标签：</strong>{#foreach from=$web_tags item=item#}<a href="{#$item.tag_link#}" style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; padding: 3px 8px; border-radius: 15px; font-size: 11px; margin-right: 8px; text-decoration: none; display: inline-block; margin-bottom: 5px;">{#$item.tag_name#}</a>{#/foreach#}</li>
                        <li>{#get_adcode(1)#}</li>
                        <li><strong>🔍 相关查询：</strong>
                            <a rel="external nofollow" href="https://www.aizhan.com/cha/{#$web.web_url#}" target="_blank" title="{#$web.web_name#}" style="color: #007bff;">SEO综合查询</a> |
                            <a rel="external nofollow" href="https://rank.aizhan.com/{#$web.web_url#}" target="_blank" title="{#$web.web_name#}" style="color: #007bff;">网站权重查询</a> |
                            <a rel="external nofollow" href="http://www.baidu.com/s?wd=site%3A{#$web.web_url#}&amp;cl=3" target="_blank" title="{#$web.web_name#}" style="color: #007bff;">百度查询</a>
                        </li>
                        <li><strong>📅 收录日期：</strong><span style="color: #28a745; font-weight: bold;">{#$web.web_ctime#}</span></li>
                        <li><strong>🔄 更新日期：</strong><span style="color: #ff6b35; font-weight: bold;">{#$web.web_utime#}</span></li>
                        <li><strong>🔗 本页地址：</strong><a href="{#$site_url#}?mod=vip_detail&id={#$web.web_id#}">{#$site_url#}?mod=vip_detail&id={#$web.web_id#}</a></li>
					</ul>
				</div>
            </div>

            <!-- VIP特权说明 -->
            <div class="vip-features">
                <h3>🌟 VIP网站特权</h3>
                <ul class="vip-feature-list">
                    <li>优先展示位置，获得更多曝光机会</li>
                    <li>专属VIP标识，彰显网站品质</li>
                    <li>快速审核通道，极速上线</li>
                    <li>专业客服支持，贴心服务保障</li>
                    <li>数据统计优先，实时监控网站表现</li>
                    <li>搜索引擎优化，提升网站排名</li>
                </ul>
            </div>

            <div class="blank10"></div>

            <div class="web_ai_intro">关于【{#$web.web_name#}】的详细介绍</div>
            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
        	    <div style="text-align: center; padding: 30px; background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); border-radius: 15px; margin-bottom: 20px; border: 3px solid #ffd700;">
        	        <div style="font-size: 48px; margin-bottom: 15px;">👑</div>
        	        <h3 style="color: #333; margin: 0 0 10px 0; font-size: 20px;">VIP尊享网站</h3>
        	        <p style="margin: 0; color: #666; font-size: 14px;">享受优质服务，体验尊贵特权</p>
        	    </div>
        	    <div class="blank10"></div>
				{#if $web.web_ai_intro#}
				<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #ffd700;">
					{#$web.web_ai_intro#}
				</div>
				{#else#}
				<div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #ffd700;">
					<p>该VIP网站提供优质的服务和内容，享受专属特权和优先支持。我们致力于为VIP用户提供最佳的浏览体验。</p>
				</div>
				{#/if#}
			</div>

			<div class="blank10"></div>
            <div class="web_ai_intro">注意事项：凡违反中国国家法律法规的网站，95分类目录一律不给予收录。</div>
            <div class="blank10"></div>
			<div id="relsite" class="clearfix">
                <p><strong>{#$web.web_name#}</strong>于{#$web.web_ctime#}被收录到{#$cate_name#}分类目录，并获得VIP认证。该网站享受VIP特权服务，包括优先展示、快速审核等特殊待遇。相关信息来自网站提交者提供！由于网站内容动态属性，时刻在变动，本站无法保证该网站的合法性以及内容真实可靠！请大家查阅时，谨慎选择、自辩真伪，感谢您的理解与支持。如果您在了解（{#$web.web_name#}）时发现：信息不实或网站存在非法等相关内容，请及时联系我们处理，我们将在第一时间处理。</p>
            </div>

            <!-- VIP专属提醒 -->
            <div style="background: linear-gradient(135deg, #28a745, #20c997); border: 1px solid #20c997; color: white; padding: 20px; border-radius: 12px; margin: 20px 0; font-size: 14px; box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);">
                <strong>🌟 VIP专属提醒：</strong>
                <ul style="margin: 10px 0 0 20px; padding: 0;">
                    <li>该网站已通过VIP认证，享受优质服务保障</li>
                    <li>VIP网站拥有优先展示权和快速响应支持</li>
                    <li>如遇任何问题，VIP客服将优先为您处理</li>
                    <li>感谢您选择VIP认证网站，祝您使用愉快</li>
                </ul>
            </div>

            <!-- 上一站下一站导航 -->
            <div class="website-navigation" style="margin: 20px 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
            	<div style="display: flex; justify-content: space-between; align-items: center;">
            		<div class="prev-website" style="flex: 1; text-align: left;">
            			{#if $prev_website#}
            				<a href="{#$prev_website.web_link#}" style="color: #ffd700; text-decoration: none; font-size: 14px; font-weight: bold;"
            				   onmouseover="this.style.color='#ffed4e'" onmouseout="this.style.color='#ffd700'">
            					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
            					👑 上一站：{#$prev_website.web_name#}
            				</a>
            			{#else#}
            				<span style="color: rgba(255,255,255,0.7); font-size: 14px;">
            					<i class="fas fa-chevron-left" style="margin-right: 5px;"></i>
            					👑 上一站：暂无
            				</span>
            			{#/if#}
            		</div>

            		<div class="navigation-center" style="flex: 0 0 auto; margin: 0 20px;">
            			<span style="color: #ffd700; font-size: 12px; font-weight: bold;">VIP导航</span>
            		</div>

            		<div class="next-website" style="flex: 1; text-align: right;">
            			{#if $next_website#}
            				<a href="{#$next_website.web_link#}" style="color: #ffd700; text-decoration: none; font-size: 14px; font-weight: bold;"
            				   onmouseover="this.style.color='#ffed4e'" onmouseout="this.style.color='#ffd700'">
            					👑 下一站：{#$next_website.web_name#}
            					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
            				</a>
            			{#else#}
            				<span style="color: rgba(255,255,255,0.7); font-size: 14px;">
            					👑 下一站：暂无
            					<i class="fas fa-chevron-right" style="margin-left: 5px;"></i>
            				</span>
            			{#/if#}
            		</div>
            	</div>
            </div>

            <div class="blank10"></div>

            <!-- VIP操作按钮 -->
            <div style="display: flex; justify-content: center; align-items: center; gap: 20px; margin: 25px 0; flex-wrap: wrap;">
                <a href="{#$web.web_links#}" target="_blank" onClick="clickout({#$web.web_id#})" class="vip-access-button">
                    🚀 访问VIP网站
                </a>
                <a href="?mod=webdir" style="background: linear-gradient(45deg, #007bff, #0056b3); color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; box-shadow: 0 4px 15px rgba(0, 123, 255, 0.4);">返回网站目录</a>
                <a href="?mod=webdir&cid={#$web.cate_id#}" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 12px 25px; text-decoration: none; border-radius: 25px; font-size: 16px; font-weight: bold; display: inline-block; box-shadow: 0 4px 15px rgba(40, 167, 69, 0.4);">查看同分类</a>
                <button onclick="addfav({#$web.web_id#})" title="点击收藏" style="background: linear-gradient(45deg, #ffc107, #e0a800); color: #212529; padding: 12px 25px; border: none; border-radius: 25px; font-size: 16px; font-weight: bold; cursor: pointer; display: inline-block; box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);">⭐ 收藏网站</button>
            </div>

            <div class="blank10"></div>

            <!-- 版权声明 -->
            <div class="text-xs text-muted">
                <div><span>©</span> 版权声明</div>
                <div class="posts-copyright">
                    <div><br>
                        <fieldset style="border:1px dashed #ffd700;padding:15px;border-radius:10px;line-height: 2em;color: #6D6D6D; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
                            <legend align="center" style="color:#FFFFFF;width:220px;text-align:center;background: linear-gradient(45deg, #ffd700, #ffed4e);font-size: 14px;border-radius: 8px; padding: 5px;">👑 95分类目录 - VIP版权声明</legend>
                            1、本主题所有言论和图片纯属会员个人意见，与本站立场无关。<br>
                            2、本站所有主题由该文章作者发表，该文章作者与<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>享有文章相关版权。<br>
                            3、其他单位或个人使用、转载或引用本文时必须同时征得该文章作者和<a href="https://www.95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>的同意。<br>
                            4、文章作者须承担一切因本文发表而直接或间接导致的民事或刑事法律责任。<br>
                            5、本帖部分内容转载自其它媒体，但并不代表本站赞同其观点和对其真实性负责。<br>
                            6、如本帖侵犯到任何版权问题，请立即告知本站，本站将及时予与删除并致以最深的歉意。<br>
                            7、<a href="https://95dir.com/" rel="nofollow"><font color="#FF6600">95分类目录</font></a>管理员有权不事先通知发贴者而删除本文。<br>
                            8、<strong style="color: #ffd700;">VIP网站享受优先处理权和专属客服支持。</strong>
                        </fieldset><br>
                    </div>
                </div>
            </div>

            <div class="blank10"></div>
        	<div id="relsite" class="clearfix">
            	<h2 style="color: #ffd700;">👑 同分类VIP站点</h2>
               	<ul class="rellist">
              		{#foreach from=$related_vip item=rel#}
               		<li style="border: 2px solid #ffd700; border-radius: 8px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
               		    <a href="{#$rel.web_link#}">
               		        <img src="{#$rel.web_pic#}" width="100" height="80" alt="{#$rel.web_name#}" style="border-radius: 5px;" />
               		        <strong style="color: #ff6b35;">👑 {#$rel.web_name#}</strong>
               		    </a>
               		</li>
               		{#foreachelse#}
               		<li>暂无其他同分类VIP网站</li>
               		{#/foreach#}
              	</ul>
            </div>

            <div class="blank10"></div>
            <div id="relsite" class="clearfix">
            	<h2 style="color: #667eea;">🏆 本类排行榜</h2>
               	<ul class="rellist">
              		{#foreach from=get_websites($web.cate_id, 10, false, false, 'views') item=hot name=hot_website#}
            <li><a href="{#$hot.web_link#}"><img src="{#$hot.web_pic#}" width="100" height="80" alt="{#$hot.web_name#}" /><strong>{#$hot.web_name#}</strong></a></li>
            {#/foreach#}
              	</ul>
            </div>
        </div>

        <div id="mainbox-right">
            <!-- VIP专属推荐 -->
            <div id="bestweb" class="mag" style="border: 3px solid #ffd700; border-radius: 12px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
            	<h3 style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">👑 VIP尊享站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 8, false, true) item=vip#}
                   	<li style="border-bottom: 1px solid #ffd700; padding-bottom: 10px; margin-bottom: 10px;">
                   	    <a href="{#$vip.web_link#}">
                   	        <img src="{#$vip.web_pic#}" width="100" height="80" alt="{#$vip.web_name#}" style="border: 2px solid #ffd700; border-radius: 5px;" />
                   	    </a>
                   	    <strong style="display: flex; align-items: center; justify-content: space-between; margin: 5px 0;">
                   	        <a href="{#$vip.web_link#}" title="{#$vip.web_name#}" style="color: #ff6b35; flex: 1;">
                   	            👑 {#$vip.web_name#}
                   	        </a>
                   	        <a href="{#$vip.web_furl#}" target="_blank" class="visit" onClick="clickout({#$vip.web_id#})" style="background: linear-gradient(45deg, #28a745, #20c997); color: white; padding: 3px 8px; border-radius: 12px; font-size: 10px; text-decoration: none; margin-left: 8px; white-space: nowrap;">
                   	            ⚡ VIP直达
                   	        </a>
                   	    </strong>
                   	    <p style="color: #666; margin: 5px 0 0 0;">{#$vip.web_intro#}</p>
                   	</li>
                   	{#/foreach#}
               	</ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart" style="border: 2px solid #667eea; border-radius: 10px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
            	<h3 style="background: linear-gradient(45deg, #667eea, #764ba2); color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">📰 推荐资讯</h3>
                <ul class="artlist_b" style="list-style: none; padding: 0; margin: 0;">
                	{#foreach from=get_articles(0, 10) item=art#}
                	<li style="border-bottom: 1px solid #e9ecef; padding: 8px 0; margin: 0; line-height: 1.5;">
                	    <span style="color: #667eea; font-size: 12px; margin-right: 5px;">[{#$art.cate_name#}]</span>
                	    <a href="{#$art.art_link#}" style="color: #333; text-decoration: none; font-size: 13px;" onmouseover="this.style.color='#667eea'" onmouseout="this.style.color='#333'">{#$art.art_title#}</a>
                	</li>
                    {#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <div id="bestart" style="border: 2px solid #28a745; border-radius: 10px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
                <h3 style="background: linear-gradient(45deg, #28a745, #20c997); color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">🆕 最新收录</h3>
            <ul class="artlist_b" style="list-style: none; padding: 0; margin: 0;">
                    {#foreach from=get_websites(0, 8) item=new#}
					<li style="border-bottom: 1px solid #e9ecef; padding: 8px 0; margin: 0; line-height: 1.5;">
					    <span style="color: #28a745; font-size: 12px; margin-right: 5px;">[{#$new.cate_name#}]</span>
					    <a href="{#$new.web_link#}" title="{#$new.web_name#}" style="color: #333; text-decoration: none; font-size: 13px;" onmouseover="this.style.color='#28a745'" onmouseout="this.style.color='#333'">{#$new.web_name#}</a>
					    <span style="color: #999; font-size: 11px; margin-left: 8px;">{#$new.web_ctime#}</span>
					</li>
                   	{#/foreach#}
                </ul>
            </div>

            <div class="blank10"></div>

            <div id="bestweb" style="border: 2px solid #ff6b35; border-radius: 10px; background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);">
            	<h3 style="background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; margin: -1px -1px 15px -1px; padding: 12px; border-radius: 8px 8px 0 0; text-align: center;">⭐ 推荐站点</h3>
                <ul class="weblist_b">
                   	{#foreach from=get_websites(0, 5, false, true) item=best#}
                   	<li style="border-bottom: 1px solid #ff6b35; padding-bottom: 10px; margin-bottom: 10px;">
                   	    <a href="{#$best.web_link#}">
                   	        <img src="{#$best.web_pic#}" width="100" height="80" alt="{#$best.web_name#}" style="border: 1px solid #ff6b35; border-radius: 5px;" />
                   	    </a>
                   	    <strong>
                   	        <a href="{#$best.web_link#}" title="{#$best.web_name#}" style="color: #ff6b35;">{#$best.web_name#}</a>
                   	    </strong>
                   	    <p style="color: #666;">{#$best.web_intro#}</p>
                   	    <address>
                   	        <a href="/go.php?url=http://{#$best.web_furl#}" target="_blank" class="visit" onClick="clickout({#$best.web_id#})" style="color: #ff6b35; font-size: 11px;">{#$best.web_url#}</a>
                   	    </address>
                   	</li>
                   	{#/foreach#}
               	</ul>
            </div>

            <!-- VIP升级提示 -->
            <div style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); color: #333; padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center; border: 3px solid #ffd700; box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);">
                <div style="font-size: 32px; margin-bottom: 10px;">👑</div>
                <h4 style="margin: 0 0 10px 0; color: #333;">升级VIP享特权</h4>
                <p style="margin: 0 0 15px 0; font-size: 13px; color: #666;">优先展示 • 快速审核 • 专属客服</p>
                <button onclick="showDonatePopup()" style="background: linear-gradient(45deg, #ff6b35, #ff8c42); color: white; padding: 10px 20px; border: none; border-radius: 25px; font-size: 14px; cursor: pointer; font-weight: bold; box-shadow: 0 4px 10px rgba(255, 107, 53, 0.4);">
                    💎 立即升级VIP
                </button>
            </div>
        </div>
    </div>
    {#include file="footer.html"#}
</div>

<!-- 统一弹窗 -->
<div id="donate-popup" style="display:none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div class="donate-popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 15px; max-width: 500px; width: 90%; border: 3px solid #ffd700;">
        <span class="close" onclick="closeDonatePopup()" style="position: absolute; top: 10px; right: 15px; font-size: 24px; cursor: pointer; color: #999;">&times;</span>
        <h3 style="color: #ffd700; font-family: 'Arial', sans-serif; font-size: 22px; text-align: center; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2); margin-top: 0;">
            👑 VIP服务价格表
        </h3>
        <div style="background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%); padding: 15px; border-radius: 10px; margin: 20px 0; color: #333;">
            <p style="margin: 0; line-height: 1.6; text-align: center; font-weight: bold;">
                <strong style="color: #ff6b35;">💎 VIP直链席位30元/每年</strong> - 顶部VIP位置<br>
                <strong style="color: #28a745;">⭐ 10元上推荐位</strong> - 首页推荐展示<br>
                <strong style="color: #007bff;">⚡ 5元快审服务</strong> - 1-3个工作日审核
            </p>
        </div>
        <p style="text-align: center; margin: 15px 0; color: #666;">
            备注格式：<strong style="color: #ffd700;">VIP/推荐/快审+网址</strong>
        </p>
        <div class="donate-qr-codes" style="display: flex; justify-content: space-around; margin: 20px 0;">
            <div style="text-align: center;">
                <h4 style="color: #28a745;">微信支付</h4>
                <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206265.png" alt="WeChat QR Code" style="width: 150px; height: 150px; border: 2px solid #28a745; border-radius: 10px;">
            </div>
            <div style="text-align: center;">
                <h4 style="color: #007bff;">支付宝支付</h4>
                <img src="https://cdn.jsdelivr.net/gh/zhuxiaoming2001/tuchuang/wzlingdi/202412022206984.png" alt="Alipay QR Code" style="width: 150px; height: 150px; border: 2px solid #007bff; border-radius: 10px;">
            </div>
        </div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 10px; margin-top: 20px; border-left: 4px solid #ffd700;">
            <h4 style="margin-top: 0; color: #333;">🌟 VIP服务说明：</h4>
            <ul style="margin: 0; padding-left: 20px; line-height: 1.6; color: #666;">
                <li>VIP位：展示在顶部VIP推广区，享受黄金位置</li>
                <li>推荐位：展示在首页推荐区域，获得更多曝光</li>
                <li>快审：1-3个工作日完成审核，快速上线</li>
                <li>专属客服：VIP专线支持，优先处理问题</li>
                <li>数据优先：实时统计，优先更新网站数据</li>
            </ul>
        </div>
    </div>
</div>

<script>
// 统一弹窗功能
function showDonatePopup() {
    document.getElementById('donate-popup').style.display = 'block';
}

function closeDonatePopup() {
    document.getElementById('donate-popup').style.display = 'none';
}

// 点击弹窗外部关闭
document.getElementById('donate-popup').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDonatePopup();
    }
});

// ESC键关闭弹窗
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeDonatePopup();
    }
});

// VIP特效
document.addEventListener('DOMContentLoaded', function() {
    // 添加VIP闪烁效果
    const vipElements = document.querySelectorAll('.vip-badge, .vip-crown');
    vipElements.forEach(function(element) {
        element.addEventListener('mouseenter', function() {
            this.style.animation = 'glow 0.5s ease-in-out infinite alternate';
        });
        element.addEventListener('mouseleave', function() {
            this.style.animation = 'glow 2s ease-in-out infinite alternate';
        });
    });
});
</script>

</body>
</html>