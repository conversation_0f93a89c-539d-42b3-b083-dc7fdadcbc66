<?php
// 开启错误显示
error_reporting(E_ALL);
ini_set('display_errors', 1);

require('common.php');

$pagetitle = '付费数据修复';

// 修复VIP付费记录的函数
function fix_vip_payment_records() {
    global $DB;

    $websites_table = $DB->table('websites');
    $payment_table = $DB->table('payment_records');
    $current_time = time();
    $fixed_count = 0;

    // 查找没有付费记录的VIP网站
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ctime, w.web_vip_expire
            FROM $websites_table w
            WHERE w.web_ispay = 1
            AND w.web_id NOT IN (SELECT DISTINCT web_id FROM $payment_table WHERE payment_type = 1)";

    $missing_vip = $DB->fetch_all($sql);

    foreach ($missing_vip as $vip) {
        $expire_time = $vip['web_vip_expire'] ?: ($vip['web_ctime'] + 365 * 24 * 3600);

        $payment_data = array(
            'web_id' => $vip['web_id'],
            'web_name' => $vip['web_name'],
            'web_url' => $vip['web_url'],
            'payment_type' => 1, // VIP
            'payment_amount' => 30.00, // 默认VIP价格
            'payment_time' => $vip['web_ctime'], // 使用网站创建时间
            'expire_time' => $expire_time,
            'operator' => 'system',
            'status' => 1,
            'remark' => '数据修复：补充VIP付费记录',
            'created_at' => $current_time
        );

        $DB->insert($payment_table, $payment_data);

        // 如果没有VIP到期时间，更新它
        if (!$vip['web_vip_expire']) {
            $DB->update($websites_table, array('web_vip_expire' => $expire_time), array('web_id' => $vip['web_id']));
        }

        $fixed_count++;
    }

    if ($fixed_count > 0) {
        echo "<div class='alert alert-success'>成功修复了 {$fixed_count} 个VIP网站的付费记录</div>";
    } else {
        echo "<div class='alert alert-info'>VIP付费记录数据一致，无需修复</div>";
    }
}

// 修复推荐付费记录的函数
function fix_recommend_payment_records() {
    global $DB;

    $websites_table = $DB->table('websites');
    $payment_table = $DB->table('payment_records');
    $current_time = time();
    $fixed_count = 0;

    // 查找没有付费记录的推荐网站
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ctime, w.web_recommend_expire
            FROM $websites_table w
            WHERE w.web_isbest = 1
            AND w.web_id NOT IN (SELECT DISTINCT web_id FROM $payment_table WHERE payment_type = 2)";

    $missing_recommend = $DB->fetch_all($sql);

    foreach ($missing_recommend as $recommend) {
        $expire_time = $recommend['web_recommend_expire'] ?: ($recommend['web_ctime'] + 365 * 24 * 3600);

        $payment_data = array(
            'web_id' => $recommend['web_id'],
            'web_name' => $recommend['web_name'],
            'web_url' => $recommend['web_url'],
            'payment_type' => 2, // 推荐
            'payment_amount' => 10.00, // 默认推荐价格
            'payment_time' => $recommend['web_ctime'], // 使用网站创建时间
            'expire_time' => $expire_time,
            'operator' => 'system',
            'status' => 1,
            'remark' => '数据修复：补充推荐付费记录',
            'created_at' => $current_time
        );

        $DB->insert($payment_table, $payment_data);

        // 如果没有推荐到期时间，更新它
        if (!$recommend['web_recommend_expire']) {
            $DB->update($websites_table, array('web_recommend_expire' => $expire_time), array('web_id' => $recommend['web_id']));
        }

        $fixed_count++;
    }

    if ($fixed_count > 0) {
        echo "<div class='alert alert-success'>成功修复了 {$fixed_count} 个推荐网站的付费记录</div>";
    } else {
        echo "<div class='alert alert-info'>推荐付费记录数据一致，无需修复</div>";
    }
}

// 创建付费记录表的函数
function create_payment_records_table() {
    global $DB;

    $sql = "CREATE TABLE IF NOT EXISTS `" . $DB->table('payment_records') . "` (
        `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
        `web_id` int(10) unsigned NOT NULL DEFAULT '0',
        `web_name` varchar(100) NOT NULL DEFAULT '',
        `web_url` varchar(255) NOT NULL DEFAULT '',
        `payment_type` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '付费类型：1=VIP，2=推荐，3=快审',
        `payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '付费金额',
        `payment_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '付费时间',
        `expire_time` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '到期时间',
        `operator` varchar(50) NOT NULL DEFAULT 'admin' COMMENT '操作员',
        `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态：1=有效，0=已过期',
        `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
        `created_at` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `web_id` (`web_id`),
        KEY `payment_type` (`payment_type`),
        KEY `payment_time` (`payment_time`)
    ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COMMENT='付费记录表';";

    $DB->query($sql);
    echo "<div class='alert alert-success'>付费记录表创建成功</div>";
}

// 检查是否有修复操作
$action = isset($_GET['action']) ? $_GET['action'] : '';

if ($action == 'fix_vip_records') {
    // 修复VIP付费记录
    fix_vip_payment_records();
} elseif ($action == 'fix_recommend_records') {
    // 修复推荐付费记录
    fix_recommend_payment_records();
} elseif ($action == 'fix_all') {
    // 修复所有付费记录
    fix_vip_payment_records();
    fix_recommend_payment_records();
}

// 获取统计数据
$websites_table = $DB->table('websites');
$payment_table = $DB->table('payment_records');

// 检查付费记录表是否存在
$check_table = $DB->query("SHOW TABLES LIKE '$payment_table'");
$payment_table_exists = $DB->num_rows($check_table) > 0;

if (!$payment_table_exists) {
    // 如果付费记录表不存在，创建它
    create_payment_records_table();
    $payment_table_exists = true;
}

// 获取实际数据
$actual_vip_count = $DB->get_count($websites_table, 'web_ispay = 1');
$actual_recommend_count = $DB->get_count($websites_table, 'web_isbest = 1');

// 获取付费记录数据
$vip_record_count = 0;
$recommend_record_count = 0;
if ($payment_table_exists) {
    $vip_record_count = $DB->get_count($payment_table, 'payment_type = 1');
    $recommend_record_count = $DB->get_count($payment_table, 'payment_type = 2');
}

// 检查不一致的VIP网站
$missing_vip_records = array();
if ($payment_table_exists && $actual_vip_count > $vip_record_count) {
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ctime 
            FROM $websites_table w 
            WHERE w.web_ispay = 1 
            AND w.web_id NOT IN (SELECT DISTINCT web_id FROM $payment_table WHERE payment_type = 1)";
    $missing_vip_records = $DB->fetch_all($sql);
}

// 检查不一致的推荐网站
$missing_recommend_records = array();
if ($payment_table_exists && $actual_recommend_count > $recommend_record_count) {
    $sql = "SELECT w.web_id, w.web_name, w.web_url, w.web_ctime 
            FROM $websites_table w 
            WHERE w.web_isbest = 1 
            AND w.web_id NOT IN (SELECT DISTINCT web_id FROM $payment_table WHERE payment_type = 2)";
    $missing_recommend_records = $DB->fetch_all($sql);
}







?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $pagetitle; ?> - 后台管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: #fff; padding: 20px; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }
        .alert { padding: 10px; margin: 10px 0; border-radius: 3px; }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .alert-info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .alert-danger { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .btn { display: inline-block; padding: 8px 16px; margin: 5px; text-decoration: none; border-radius: 3px; font-size: 14px; }
        .btn-primary { background: #007bff; color: #fff; }
        .btn-success { background: #28a745; color: #fff; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-secondary { background: #6c757d; color: #fff; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
        .stat-number { font-size: 24px; font-weight: bold; color: #007bff; }
        .stat-label { color: #666; font-size: 14px; }
        .mismatch { border-left-color: #dc3545; }
        .mismatch .stat-number { color: #dc3545; }
        table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .text-danger { color: #dc3545; }
        .text-success { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1><?php echo $pagetitle; ?></h1>
        
        <?php if ($action): ?>
            <div style="margin-bottom: 20px;">
                <a href="payment_fix.php" class="btn btn-secondary">返回检查页面</a>
                <a href="payment_stats.php" class="btn btn-primary">查看付费统计</a>
            </div>
        <?php endif; ?>
        
        <div class="stats-grid">
            <div class="stat-card <?php echo ($actual_vip_count != $vip_record_count) ? 'mismatch' : ''; ?>">
                <div class="stat-number"><?php echo $actual_vip_count; ?></div>
                <div class="stat-label">实际VIP网站数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $vip_record_count; ?></div>
                <div class="stat-label">VIP付费记录数量</div>
            </div>
            <div class="stat-card <?php echo ($actual_recommend_count != $recommend_record_count) ? 'mismatch' : ''; ?>">
                <div class="stat-number"><?php echo $actual_recommend_count; ?></div>
                <div class="stat-label">实际推荐网站数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-number"><?php echo $recommend_record_count; ?></div>
                <div class="stat-label">推荐付费记录数量</div>
            </div>
        </div>
        
        <?php if (!$payment_table_exists): ?>
            <div class="alert alert-danger">
                <strong>错误：</strong>付费记录表不存在，这可能是导致统计不准确的原因。
            </div>
        <?php endif; ?>
        
        <?php if ($actual_vip_count != $vip_record_count || $actual_recommend_count != $recommend_record_count): ?>
            <div class="alert alert-warning">
                <strong>数据不一致警告：</strong>实际网站数量与付费记录数量不匹配，建议进行数据修复。
            </div>
            
            <div style="margin: 20px 0;">
                <h3>修复选项</h3>
                <a href="?action=fix_vip_records" class="btn btn-warning">修复VIP付费记录</a>
                <a href="?action=fix_recommend_records" class="btn btn-warning">修复推荐付费记录</a>
                <a href="?action=fix_all" class="btn btn-success">修复所有付费记录</a>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <strong>数据一致：</strong>所有付费数据都是一致的，无需修复。
            </div>
        <?php endif; ?>
        
        <?php if (!empty($missing_vip_records)): ?>
            <div style="margin: 20px 0;">
                <h3>缺少付费记录的VIP网站 (<?php echo count($missing_vip_records); ?>个)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>网站名称</th>
                            <th>网站网址</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($missing_vip_records as $record): ?>
                        <tr>
                            <td><?php echo $record['web_id']; ?></td>
                            <td><?php echo htmlspecialchars($record['web_name']); ?></td>
                            <td><a href="<?php echo $record['web_url']; ?>" target="_blank"><?php echo htmlspecialchars($record['web_url']); ?></a></td>
                            <td><?php echo date('Y-m-d H:i:s', $record['web_ctime']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($missing_recommend_records)): ?>
            <div style="margin: 20px 0;">
                <h3>缺少付费记录的推荐网站 (<?php echo count($missing_recommend_records); ?>个)</h3>
                <table>
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>网站名称</th>
                            <th>网站网址</th>
                            <th>创建时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($missing_recommend_records as $record): ?>
                        <tr>
                            <td><?php echo $record['web_id']; ?></td>
                            <td><?php echo htmlspecialchars($record['web_name']); ?></td>
                            <td><a href="<?php echo $record['web_url']; ?>" target="_blank"><?php echo htmlspecialchars($record['web_url']); ?></a></td>
                            <td><?php echo date('Y-m-d H:i:s', $record['web_ctime']); ?></td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
            <h3>相关链接</h3>
            <a href="payment_stats.php" class="btn btn-primary">付费统计</a>
            <a href="payment_manage.php" class="btn btn-primary">付费管理</a>
            <a href="website.php" class="btn btn-secondary">网站管理</a>
            <a href="admin.php" class="btn btn-secondary">返回首页</a>
        </div>
    </div>
</body>
</html>
