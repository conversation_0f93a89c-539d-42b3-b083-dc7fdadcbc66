<?php
// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 0); // 不显示错误到输出

// 设置内容类型
header('Content-Type: text/html; charset=utf-8');

try {
    require('common.php');
} catch (Exception $e) {
    echo('<span style="color: #f00; font-weight: bold;">系统错误：无法加载配置</span>');
    exit;
}

// 检查管理员权限
if (!isset($myself['user_id']) || !$myself['user_id']) {
    echo('<span style="color: #f00; font-weight: bold;">权限不足</span>');
    exit;
}

$type = trim($_GET['type']);
$url = trim($_GET['url']);

// 测试响应
if ($type == 'test') {
    echo('<span style="color: #090;">AJAX接口正常工作</span>');
    exit;
}

if ($type == 'check' && !empty($url)) {
    // 使用改进的域名验证函数，支持带路径和查询参数的URL
    if (!is_valid_domain($url)) {
        echo('<span style="color: #f00; font-weight: bold;">域名格式不正确！</span>');
        exit;
    }

    try {
        // 转义URL防止SQL注入
        $safe_url = addslashes($url);

        $query = $DB->query("SELECT web_id, web_name, web_status, web_ctime FROM ".$DB->table('websites')." WHERE web_url='$safe_url'");

        if ($DB->num_rows($query)) {
            $row = $DB->fetch_array($query);
            $status_text = '';
            $status_color = '';

            switch($row['web_status']) {
                case 1:
                    $status_text = '该网站已被拉黑';
                    $status_color = '#333';
                    break;
                case 2:
                    $status_text = '该网站正在审核中';
                    $status_color = '#ff6600';
                    break;
                case 3:
                    $status_text = '该网站已收录（' . date('Y-m-d', $row['web_ctime']) . '）';
                    $status_color = '#f00';
                    break;
                case 4:
                    $status_text = '该网站审核不通过';
                    $status_color = '#f60';
                    break;
                default:
                    $status_text = '该域名已存在';
                    $status_color = '#f00';
            }

            echo('<span style="color: ' . $status_color . '; font-weight: bold;">' . htmlspecialchars($status_text) . '</span>');
            $DB->free_result($query);
        } else {
            echo('<span style="color: #090; font-weight: bold;">✓ 该域名可以添加</span>');
        }

    } catch (Exception $e) {
        echo('<span style="color: #f00; font-weight: bold;">数据库查询错误</span>');
    }

} else {
    if (empty($url)) {
        echo('<span style="color: #f00;">请输入网站域名</span>');
    } else {
        echo('<span style="color: #f00;">参数错误</span>');
    }
}
?>
