<?php
/**
 * 逐步测试站点地图生成
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');

// 创建一个逐步测试的站点地图函数
function test_sitemap_step_by_step() {
    global $DB, $options;
    
    header("Content-Type: application/xml;");
    
    // 步骤1: XML头部
    echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 步骤2: 首页
    echo "<url>\n";
    echo "<loc>".$options['site_url']."</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>1.0</priority>\n";
    echo "</url>\n";
    
    // 步骤3: 基本页面
    echo "<url>\n";
    echo "<loc>".$options['site_url']."?mod=webdir</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>0.9</priority>\n";
    echo "</url>\n";
    
    // 步骤4: 测试网站数据查询（只取1个）
    try {
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
        $sql .= " WHERE web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0) ORDER BY web_id DESC LIMIT 1";
        $query = $DB->query($sql);
        
        if ($query && $row = $DB->fetch_array($query)) {
            $web_link = str_replace('&', '&amp;', get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']));
            echo "<url>\n";
            echo "<loc>".$web_link."</loc>\n";
            echo "<lastmod>".date('c', $row['web_ctime'])."</lastmod>\n";
            echo "<changefreq>weekly</changefreq>\n";
            echo "<priority>0.6</priority>\n";
            echo "</url>\n";
            $DB->free_result($query);
        }
    } catch (Exception $e) {
        // 如果失败，添加一个注释
        echo "<!-- Website query failed: " . htmlspecialchars($e->getMessage()) . " -->\n";
    }
    
    // 步骤5: 测试分类数据（只取1个）
    try {
        $categories = get_all_category();
        $count = 0;
        foreach ($categories as $cate) {
            if ($cate['cate_mod'] == 'webdir' && $count < 1) {
                $cate_url = str_replace('&', '&amp;', $options['site_url'] . get_category_url('webdir', $cate['cate_id']));
                echo "<url>\n";
                echo "<loc>".$cate_url."</loc>\n";
                echo "<lastmod>".date('c')."</lastmod>\n";
                echo "<changefreq>weekly</changefreq>\n";
                echo "<priority>0.7</priority>\n";
                echo "</url>\n";
                $count++;
            }
        }
    } catch (Exception $e) {
        // 如果失败，添加一个注释
        echo "<!-- Category query failed: " . htmlspecialchars($e->getMessage()) . " -->\n";
    }
    
    // 步骤6: XML结束
    echo "</urlset>\n";
}

// 如果是生成请求
if (isset($_GET['generate'])) {
    test_sitemap_step_by_step();
    exit;
}

// 如果是测试原始函数
if (isset($_GET['original'])) {
    try {
        get_website_sitemap(0);
    } catch (Exception $e) {
        header("Content-Type: text/plain;");
        echo "Error: " . $e->getMessage();
    }
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>逐步测试站点地图</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
        .test-link.danger { background: #dc3545; }
        .test-link.danger:hover { background: #c82333; }
    </style>
</head>
<body>
    <h1>逐步测试站点地图生成</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section info">
        <h2>测试选项</h2>
        <p>选择要测试的版本：</p>
        <a href="?generate=1" target="_blank" class="test-link">测试逐步版本（安全）</a>
        <a href="?original=1" target="_blank" class="test-link danger">测试原始函数（可能失败）</a>
        <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link danger">在线测试原始webdir</a>
    </div>
    
    <div class="test-section">
        <h2>预期结果</h2>
        <ul>
            <li><strong>逐步版本</strong>：应该总是能生成完整的XML，即使某些部分失败</li>
            <li><strong>原始函数</strong>：如果失败，可以帮助我们定位问题所在</li>
            <li><strong>在线测试</strong>：这是实际的生产环境测试</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>问题诊断</h2>
        <p>如果逐步版本成功但原始函数失败，问题可能在于：</p>
        <ul>
            <li>数据量过大导致超时或内存不足</li>
            <li>某个特定的数据记录导致函数调用失败</li>
            <li>循环中的异常没有被正确处理</li>
            <li>数据库查询超时</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>系统信息</h2>
        <?php
        echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>\n";
        echo "<p><strong>内存限制:</strong> " . ini_get('memory_limit') . "</p>\n";
        echo "<p><strong>执行时间限制:</strong> " . ini_get('max_execution_time') . " 秒</p>\n";
        echo "<p><strong>错误报告:</strong> " . (error_reporting() ? '开启' : '关闭') . "</p>\n";
        
        // 检查数据库状态
        try {
            $website_count = $DB->get_count($DB->table('websites'), "web_status=3");
            echo "<p><strong>网站数量:</strong> $website_count</p>\n";
        } catch (Exception $e) {
            echo "<p><strong>数据库错误:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
        ?>
    </div>
</body>
</html>
