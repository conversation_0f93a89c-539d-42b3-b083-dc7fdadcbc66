/* 数据公示页面样式 */

.datastats-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
}

.page-title {
    text-align: center;
    color: #2c3e50;
    font-size: 32px;
    margin-bottom: 30px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

.stats-section {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    margin-bottom: 30px;
    padding: 25px;
    border: 1px solid #e1e8ed;
}

.section-title {
    color: #34495e;
    font-size: 24px;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 3px solid #3498db;
    font-weight: bold;
}

/* 服务器信息网格布局 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border-left: 4px solid #3498db;
    transition: all 0.3s ease;
}

.info-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.2);
}

.info-item .label {
    font-weight: bold;
    color: #2c3e50;
    font-size: 14px;
}

.info-item .value {
    color: #27ae60;
    font-weight: bold;
    text-align: right;
    font-size: 14px;
}

/* 今日统计 */
.today-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-label {
    display: block;
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-value {
    display: block;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* 爬虫统计表格 */
.spider-subtitle {
    text-align: center;
    font-style: italic;
    color: #7f8c8d;
    margin-bottom: 20px;
    font-size: 16px;
}

.table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.spider-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    font-size: 13px;
}

.spider-table thead {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.spider-table th {
    padding: 12px 8px;
    text-align: center;
    font-weight: bold;
    border: 1px solid #ddd;
    font-size: 12px;
    color: #f8f7f7;
}

.spider-table td {
    padding: 10px 8px;
    text-align: center;
    border: 1px solid #ddd;
    transition: background-color 0.3s ease;
}

.spider-table tbody tr:nth-child(even) {
    background-color: #f8f9fa;
}

.spider-table tbody tr:hover {
    background-color: #e3f2fd !important;
    cursor: pointer;
}

.spider-table td:first-child {
    font-weight: bold;
    color: #2c3e50;
    background-color: #ecf0f1;
}

/* 网站数据统计 */
.site-stats {
    display: flex;
    flex-wrap: wrap;
    gap: 30px;
    justify-content: space-around;
}

.stat-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    min-width: 300px;
}

.site-stats .stat-item {
    background: linear-gradient(135deg, #ff7e5f 0%, #feb47b 100%);
    padding: 15px;
    border-radius: 8px;
    color: white;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.site-stats .stat-label {
    font-size: 13px;
    margin-bottom: 8px;
}

.site-stats .stat-value {
    font-size: 20px;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .datastats-container {
        padding: 10px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .section-title {
        font-size: 20px;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .today-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .spider-table {
        font-size: 11px;
    }
    
    .spider-table th,
    .spider-table td {
        padding: 6px 4px;
    }
    
    .site-stats {
        flex-direction: column;
    }
    
    .stat-group {
        grid-template-columns: 1fr;
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .today-stats {
        grid-template-columns: 1fr;
    }
    
    .spider-table th,
    .spider-table td {
        padding: 4px 2px;
        font-size: 10px;
    }
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.stats-section {
    animation: fadeInUp 0.6s ease-out;
}

.stats-section:nth-child(2) { animation-delay: 0.1s; }
.stats-section:nth-child(3) { animation-delay: 0.2s; }
.stats-section:nth-child(4) { animation-delay: 0.3s; }
.stats-section:nth-child(5) { animation-delay: 0.4s; }

/* 滚动条样式 */
.table-container::-webkit-scrollbar {
    height: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 数字滚动动画效果 */
.stat-value, .spider-count, .load-value, .memory-value {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    transition: all 0.3s ease;
    display: inline-block;
    min-width: 60px;
    text-align: center;
}

.spider-count {
    min-width: 40px;
    font-size: 14px;
}

.load-value {
    min-width: 50px;
    font-size: 13px;
}

.memory-value {
    min-width: 50px;
}

.stat-value.animating {
    color: #3498db !important;
    text-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    transform: scale(1.05);
}

.stat-value.updated {
    animation: numberGlow 2s ease-in-out;
}

@keyframes numberGlow {
    0% {
        color: #27ae60;
        text-shadow: 0 0 20px rgba(39, 174, 96, 0.8);
        transform: scale(1.1);
    }
    50% {
        color: #2ecc71;
        text-shadow: 0 0 30px rgba(46, 204, 113, 1);
        transform: scale(1.15);
    }
    100% {
        color: inherit;
        text-shadow: none;
        transform: scale(1);
    }
}

.info-item .value {
    transition: all 0.3s ease;
}

.info-item .value.updating {
    color: #3498db !important;
    font-weight: bold;
}

/* 实时指示器 */
.live-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    background: #e74c3c;
    border-radius: 50%;
    margin-left: 5px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.5;
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 数据更新闪烁效果 */
@keyframes dataUpdate {
    0% { background-color: transparent; }
    50% { background-color: rgba(39, 174, 96, 0.2); }
    100% { background-color: transparent; }
}

.data-updated {
    animation: dataUpdate 1s ease-in-out;
}

/* 悬浮提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}
