<?php
/**
 * 站点地图测试脚本
 * 用于测试所有站点地图链接是否正常工作
 */

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/category.php');
require(APP_PATH.'module/website.php');
require(APP_PATH.'module/article.php');

// 测试的站点地图类型
$sitemap_types = array(
    'all' => '全部站点地图',
    'webdir' => '网站目录站点地图',
    'weblink' => '友情链接站点地图',
    'article' => '文章站点地图',
    'article_detail' => '文章详细站点地图',
    'category' => '分类站点地图',
    'vip' => 'VIP站点地图',
    'tags' => '标签站点地图',
    'pending' => '待审核站点地图',
    'rejected' => '审核不通过站点地图',
    'blacklist' => '黑名单站点地图',
    'search' => '搜索页面站点地图',
    'member' => '会员中心站点地图',
    'other' => '其他页面站点地图'
);

echo "<!DOCTYPE html>\n";
echo "<html>\n";
echo "<head>\n";
echo "<meta charset='utf-8'>\n";
echo "<title>站点地图测试</title>\n";
echo "<style>\n";
echo "body { font-family: Arial, sans-serif; margin: 20px; }\n";
echo ".test-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }\n";
echo ".success { background-color: #d4edda; border-color: #c3e6cb; }\n";
echo ".error { background-color: #f8d7da; border-color: #f5c6cb; }\n";
echo ".url { font-family: monospace; color: #007bff; }\n";
echo "</style>\n";
echo "</head>\n";
echo "<body>\n";
echo "<h1>站点地图功能测试</h1>\n";
echo "<p>测试时间: " . date('Y-m-d H:i:s') . "</p>\n";

$success_count = 0;
$total_count = count($sitemap_types);

foreach ($sitemap_types as $type => $name) {
    echo "<div class='test-item'>\n";
    echo "<h3>$name ($type)</h3>\n";
    
    $url = "?mod=sitemap&type=$type&format=xml";
    echo "<p class='url'>URL: $url</p>\n";
    
    try {
        // 捕获输出
        ob_start();
        
        // 模拟请求参数
        $_GET['mod'] = 'sitemap';
        $_GET['type'] = $type;
        $_GET['format'] = 'xml';
        $_GET['cid'] = 0;
        
        // 调用相应的函数
        switch ($type) {
            case 'webdir':
            case 'weblink':
                get_website_sitemap(0);
                break;
            case 'article':
                get_article_sitemap(0);
                break;
            case 'article_detail':
                get_article_detail_sitemap(0);
                break;
            case 'category':
                get_category_sitemap();
                break;
            case 'vip':
                get_vip_sitemap(0);
                break;
            case 'tags':
                get_tags_sitemap();
                break;
            case 'pending':
                get_pending_sitemap(0);
                break;
            case 'rejected':
                get_rejected_sitemap(0);
                break;
            case 'blacklist':
                get_blacklist_sitemap(0);
                break;
            case 'search':
                get_search_sitemap();
                break;
            case 'member':
                get_member_sitemap();
                break;
            case 'other':
                get_other_sitemap();
                break;
            case 'all':
            default:
                get_all_sitemap();
                break;
        }
        
        $output = ob_get_clean();
        
        // 检查输出是否为有效的XML
        if (strpos($output, '<?xml') === 0 && strpos($output, '</urlset>') !== false) {
            echo "<p style='color: green;'>✓ 测试通过 - 生成了有效的XML格式</p>\n";
            echo "<p>输出长度: " . strlen($output) . " 字符</p>\n";
            
            // 计算URL数量
            $url_count = substr_count($output, '<url>');
            echo "<p>包含URL数量: $url_count</p>\n";
            
            $success_count++;
            echo "</div>\n";
        } else {
            echo "<p style='color: red;'>✗ 测试失败 - 输出格式不正确</p>\n";
            echo "<p>输出内容: " . htmlspecialchars(substr($output, 0, 200)) . "...</p>\n";
            echo "</div>\n";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>✗ 测试失败 - 发生异常: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        echo "</div>\n";
    }
}

echo "<hr>\n";
echo "<h2>测试总结</h2>\n";
echo "<p>总测试项目: $total_count</p>\n";
echo "<p>成功项目: $success_count</p>\n";
echo "<p>失败项目: " . ($total_count - $success_count) . "</p>\n";
echo "<p>成功率: " . round(($success_count / $total_count) * 100, 2) . "%</p>\n";

if ($success_count == $total_count) {
    echo "<p style='color: green; font-weight: bold;'>🎉 所有站点地图测试通过！</p>\n";
} else {
    echo "<p style='color: red; font-weight: bold;'>⚠️ 部分站点地图测试失败，请检查相关函数。</p>\n";
}

echo "</body>\n";
echo "</html>\n";
?>
