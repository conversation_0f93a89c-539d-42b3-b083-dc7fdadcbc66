<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<title>违规检查配置</title>
<link href="../themes/system/skin/global.css" rel="stylesheet" type="text/css" />
<link href="../themes/system/skin/iframe.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="../public/scripts/jquery.min.js"></script>
</head>

<body>
<div id="wrapper">
    <div id="header">
        <h1>违规检查配置</h1>
        <div class="nav-tabs">
            <a href="violation_simple_working.php?action=list">违规列表</a>
            <a href="violation_simple_working.php?action=config" class="active">配置管理</a>
            <a href="violation_simple_working.php?action=logs">检查日志</a>
            <a href="violation_simple_working.php?action=check">手动检查</a>
        </div>
    </div>
    
    <div id="content">
        <!-- 消息提示 -->
        {#if isset($smarty.get.message)#}
        <div class="message {#$smarty.get.type#}">
            {#$smarty.get.message#}
        </div>
        {#/if#}
        
        <form method="post" action="violation_simple_working.php?action=save_config">
            <div class="form-container">
                <h2>违规关键词配置</h2>
                <div class="form-group">
                    <label for="violation_keywords">违规关键词列表：</label>
                    <textarea name="config[violation_keywords]" id="violation_keywords" rows="8" cols="80" class="form-control">
{#if isset($configs.violation_keywords)#}{#$configs.violation_keywords.config_value#}{#/if#}
                    </textarea>
                    <div class="help-text">
                        请用逗号分隔关键词，支持中英文。系统会检查网站标题、描述和内容中是否包含这些关键词。
                    </div>
                </div>
                
                <h2>检查参数配置</h2>
                <div class="form-group">
                    <label for="check_interval">检查间隔（小时）：</label>
                    <input type="number" name="config[check_interval]" id="check_interval" 
                           value="{#if isset($configs.check_interval)#}{#$configs.check_interval.config_value#}{#else#}24{#/if#}" 
                           min="1" max="168" class="form-control" style="width: 100px;">
                    <div class="help-text">
                        设置自动检查的时间间隔，建议24小时检查一次。
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="check_batch_size">每次检查数量：</label>
                    <input type="number" name="config[check_batch_size]" id="check_batch_size" 
                           value="{#if isset($configs.check_batch_size)#}{#$configs.check_batch_size.config_value#}{#else#}50{#/if#}" 
                           min="10" max="200" class="form-control" style="width: 100px;">
                    <div class="help-text">
                        每次检查的网站数量，建议50个，避免服务器压力过大。
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="auto_check_enabled">自动检查：</label>
                    <select name="config[auto_check_enabled]" id="auto_check_enabled" class="form-control" style="width: 150px;">
                        <option value="1" {#if isset($configs.auto_check_enabled) && $configs.auto_check_enabled.config_value == '1'#}selected{#/if#}>启用</option>
                        <option value="0" {#if isset($configs.auto_check_enabled) && $configs.auto_check_enabled.config_value == '0'#}selected{#/if#}>禁用</option>
                    </select>
                    <div class="help-text">
                        是否启用定时自动检查功能。
                    </div>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存配置</button>
                    <button type="button" class="btn btn-default" onclick="location.reload()">重置</button>
                </div>
            </div>
        </form>
        
        <div class="info-box">
            <h3>配置说明：</h3>
            <ul>
                <li><strong>违规关键词</strong>：系统会检查网站的标题、描述和页面内容是否包含这些关键词</li>
                <li><strong>检查间隔</strong>：定时任务的执行间隔，建议设置为24小时</li>
                <li><strong>检查数量</strong>：每次检查的网站数量，避免一次性检查过多网站</li>
                <li><strong>自动检查</strong>：可以临时禁用自动检查功能</li>
            </ul>
            
            <h3>定时任务设置：</h3>
            <p>请在服务器上设置crontab定时任务：</p>
            <code>0 2 * * * /usr/bin/php {#$smarty.const.ROOT_PATH#}cron_violation_check.php</code>
            <p><small>上述命令表示每天凌晨2点执行违规检查</small></p>
        </div>
        
        <div class="keyword-suggestions">
            <h3>常用违规关键词参考：</h3>
            <div class="keyword-category">
                <h4>色情相关：</h4>
                <p>色情,成人,裸体,性爱,黄色,三级,porn,sex,adult,nude</p>
            </div>
            <div class="keyword-category">
                <h4>赌博相关：</h4>
                <p>赌博,博彩,彩票,老虎机,百家乐,德州扑克,casino,gambling,bet</p>
            </div>
            <div class="keyword-category">
                <h4>违法相关：</h4>
                <p>毒品,枪支,炸药,假证,代开发票,洗钱,诈骗</p>
            </div>
            <div class="keyword-category">
                <h4>其他违规：</h4>
                <p>病毒,木马,钓鱼,欺诈,传销,高利贷</p>
            </div>
        </div>
    </div>
</div>

<style>
.nav-tabs {
    margin: 10px 0;
    border-bottom: 1px solid #ddd;
}

.nav-tabs a {
    display: inline-block;
    padding: 8px 16px;
    margin-right: 5px;
    text-decoration: none;
    color: #666;
    border: 1px solid #ddd;
    border-bottom: none;
    background: #f5f5f5;
}

.nav-tabs a.active {
    background: #fff;
    color: #333;
    font-weight: bold;
}

.message {
    padding: 10px;
    margin: 10px 0;
    border-radius: 3px;
}

.message.success {
    background: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
}

.message.error {
    background: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
}

.form-container {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 20px 0;
}

.form-group {
    margin: 20px 0;
}

.form-group label {
    display: block;
    font-weight: bold;
    margin-bottom: 5px;
}

.form-control {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.help-text {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.btn {
    display: inline-block;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 3px;
    font-size: 14px;
    border: 1px solid #ddd;
    cursor: pointer;
    margin-right: 10px;
}

.btn-primary {
    background: #337ab7;
    color: white;
    border-color: #337ab7;
}

.btn-default {
    background: #fff;
    color: #333;
}

.info-box {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin: 20px 0;
}

.info-box h3 {
    margin-top: 0;
    color: #333;
}

.info-box ul {
    margin: 10px 0;
    padding-left: 20px;
}

.info-box li {
    margin: 5px 0;
    color: #666;
}

.info-box code {
    background: #f5f5f5;
    padding: 5px 10px;
    border-radius: 3px;
    font-family: monospace;
    display: block;
    margin: 10px 0;
}

.keyword-suggestions {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
    margin: 20px 0;
}

.keyword-category {
    margin: 15px 0;
}

.keyword-category h4 {
    color: #333;
    margin: 10px 0 5px 0;
}

.keyword-category p {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
    margin: 5px 0;
}
</style>

</body>
</html>
