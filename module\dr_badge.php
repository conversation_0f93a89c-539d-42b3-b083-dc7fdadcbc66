<?php
/**
 * 迷你DR徽章生成器
 * 生成小尺寸的DR值徽章，只显示DR数值部分
 */

// 获取参数
$domain = isset($_GET['domain']) ? $_GET['domain'] : '';
$size = isset($_GET['size']) ? $_GET['size'] : 'medium'; // small, medium, large

if (empty($domain)) {
    $domain = 'example.com';
    $dr_value = 0;
} else {
    $dr_value = getDRValue($domain);
}

// 根据DR值获取颜色
function getDRColor($dr_value) {
    if ($dr_value >= 80) return '#4CAF50';      // 绿色
    if ($dr_value >= 60) return '#8BC34A';      // 浅绿
    if ($dr_value >= 40) return '#FFC107';      // 黄色
    if ($dr_value >= 20) return '#FF9800';      // 橙色
    if ($dr_value > 0) return '#F44336';        // 红色
    return '#757575';                           // 灰色
}

// 获取DR值描述
function getDRDescription($dr_value) {
    if ($dr_value >= 80) return 'Excellent';
    if ($dr_value >= 60) return 'Very Good';
    if ($dr_value >= 40) return 'Good';
    if ($dr_value >= 20) return 'Fair';
    if ($dr_value > 0) return 'Poor';
    return 'Unknown';
}

// 简单的DR值获取
function getDRValue($domain) {
    $known_domains = [
        'google.com' => 98,
        'facebook.com' => 96,
        'youtube.com' => 95,
        'wikipedia.org' => 93,
        'twitter.com' => 92,
        'instagram.com' => 91,
        'linkedin.com' => 89,
        'github.com' => 87,
        'stackoverflow.com' => 85,
        'reddit.com' => 84,
        'baidu.com' => 82,
        'www.baidu.com' => 82,
        '95dir.com' => 29
    ];

    if (isset($known_domains[$domain])) {
        return $known_domains[$domain];
    }

    // 为其他域名生成一致的估算值
    $hash = crc32($domain);
    return abs($hash) % 51 + 10; // 10-60范围
}

// 生成迷你SVG徽章 - 只显示DR数值
function generateMiniSVGBadge($domain, $dr_value, $size = 'medium') {
    $color = getDRColor($dr_value);
    $description = getDRDescription($dr_value);
    
    // 根据尺寸设置参数 - 调整宽度以适应"DR"文字
    switch ($size) {
        case 'large':
            $width = 65;
            $height = 22;
            $font_size = 11;
            $radius = 3;
            break;
        case 'small':
            $width = 45;
            $height = 16;
            $font_size = 9;
            $radius = 2;
            break;
        case 'medium':
        default:
            $width = 55;
            $height = 18;
            $font_size = 10;
            $radius = 2;
            break;
    }
    
    $svg = '<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" width="' . $width . '" height="' . $height . '">
    <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.2" />
            <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
        </linearGradient>
    </defs>
    
    <!-- 主背景 -->
    <rect x="0" y="0" width="' . $width . '" height="' . $height . '" 
          fill="' . $color . '" rx="' . $radius . '" ry="' . $radius . '"/>
    
    <!-- 渐变效果 -->
    <rect x="0" y="0" width="' . $width . '" height="' . $height . '" 
          fill="url(#grad)" rx="' . $radius . '" ry="' . $radius . '"/>
    
    <!-- DR值文本 -->
    <text x="' . ($width / 2) . '" y="' . ($height / 2 + $font_size / 3) . '"
          font-family="Arial, Helvetica, sans-serif"
          font-size="' . $font_size . '"
          font-weight="bold"
          fill="white"
          text-anchor="middle">DR ' . $dr_value . '</text>
    
    <!-- 标题 -->
    <title>' . htmlspecialchars($domain) . ' - DR: ' . $dr_value . ' (' . $description . ')</title>
</svg>';

    return $svg;
}

// 设置HTTP头
header('Content-Type: image/svg+xml');
header('Cache-Control: public, max-age=3600'); // 缓存1小时

// 输出SVG
echo generateMiniSVGBadge($domain, $dr_value, $size);
?>
