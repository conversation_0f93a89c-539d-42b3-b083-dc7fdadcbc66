<!DOCTYPE HTML>
<html>
<head>
<title>{#$site_title#}</title>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta name="Keywords" content="{#$site_keywords#}" />
<meta name="Description" content="{#$site_description#}" />
<meta name="Copyright" content="Powered By 95dir.com" />
<link href="{#$site_root#}themes/default/skin/style.css" rel="stylesheet" type="text/css" />
<link href="{#$site_root#}public/css/logo-preview.css" rel="stylesheet" type="text/css" />
<script type="text/javascript" src="{#$site_root#}public/scripts/common.js"></script>
<script type="text/javascript" src="{#$site_root#}public/js/logo-optimizer.js"></script>
{#include file="script.html"#}
</head>

<body>
{#include file="topbar.html"#}
<div id="wrapper">
    {#include file="header.html"#}
    <div id="mainbox" class="clearfix">
        <div id="mainbox-left">
            <!--<div class="sitepath">
                <span style="float: right;">{#get_adcode(1)#}</span>
                {#$site_path#}
            </div>-->

            <!-- 黑名单页面头部 -->
            <div class="blacklist-header">
                <div class="header-content">
                    <div class="header-left">
                        <h1 class="page-title">
                            <span class="icon">🛡️</span>
                            网站黑名单
                        </h1>
                        <p class="page-desc">为了维护网络安全，以下网站已被列入黑名单</p>
                    </div>
                    <div class="header-right">
                        <div class="stats-card">
                            <div class="stat-number">{#$total#}</div>
                            <div class="stat-label">违规网站</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类筛选 -->
            <div class="filter-section">
                <div class="filter-header">
                    <h3>🔍 按违规类型筛选</h3>
                </div>
                <div class="filter-tabs">
                    <a href="?mod=blacklist" class="filter-tab {#if $current_category == ''#}active{#/if#}">
                        全部 ({#$total#})
                    </a>
                    {#foreach from=$categories key=cat_id item=cat_name#}
                    {#if $cat_id > 0#}
                    <a href="?mod=blacklist&category={#$cat_id#}" class="filter-tab {#if $current_category == $cat_id#}active{#/if#}">
                        {#$cat_name#}
                    </a>
                    {#/if#}
                    {#/foreach#}
                </div>
            </div>

            <!-- 黑名单列表 -->
            <div class="blacklist-list-section">
                {#if $websites#}
                    <div class="blacklist-table">
                        <div class="table-header">
                            <div class="col-name">网站名称</div>
                            <div class="col-domain">域名</div>
                            <div class="col-category">违规类型</div>
                            <div class="col-reason">违规原因</div>
                            <div class="col-time">拉黑时间</div>
                            <div class="col-action">操作</div>
                        </div>
                        
                        <div class="table-body">
                            {#foreach from=$websites item=website#}
                            <div class="table-row">
                                <div class="col-name">
                                    <div class="website-name">
                                        <a href="?mod=blacklist_detail&id={#$website.web_id#}" class="website-link" title="查看详情">
                                            {#$website.web_name#} {#if $website.is_today#}<span class="new-icon">new</span>{#/if#}
                                        </a>
                                        
                                    </div>
                                </div>
                                <div class="col-domain">
                                    <div class="masked-domain" title="域名已脱敏处理">
                                        <script>
                                        document.write(function(){
                                            var domain = '{#$website.domain#}';
                                            if(domain.length > 8) {
                                                return domain.substring(0, 3) + '****' + domain.substring(domain.length-3);
                                            } else if(domain.length > 4) {
                                                return domain.substring(0, 2) + '****' + domain.substring(domain.length-2);
                                            } else {
                                                return '****';
                                            }
                                        }());
                                        </script>
                                        <noscript>****</noscript>
                                    </div>
                                </div>
                                <div class="col-category">
                                    <span class="category-badge category-{#$website.web_blacklist_category#}">
                                        {#$website.category_name#}
                                    </span>
                                </div>
                                <div class="col-reason">
                                    <div class="reason-text" title="{#$website.web_blacklist_reason#}">
                                        {#$website.reason_short#}
                                    </div>
                                </div>
                                <div class="col-time">
                                    <div class="time-info">
                                        <div class="date">{#$website.blacklist_time_formatted#}</div>
                                        <div class="operator">by {#$website.web_blacklist_operator#}</div>
                                    </div>
                                </div>
                                <div class="col-action">
                                    <div class="action-buttons">
                                        <a href="?mod=blacklist_detail&id={#$website.web_id#}" class="btn-detail" title="查看详情">
                                            <span class="btn-icon">👁️</span>
                                            详情
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {#/foreach#}
                        </div>
                    </div>

                    {#if $showpage#}
                    <div class="pagination-wrapper">
                        {#$showpage#}
                    </div>
                    {#/if#}
                {#else#}
                    <div class="no-data">
                        <div class="no-data-icon">✅</div>
                        <h3>暂无黑名单网站</h3>
                        <p>目前没有发现违规网站，系统运行良好！</p>
                    </div>
                {#/if#}
            </div>
        </div>

        <div id="mainbox-right">
            <!-- 安全提醒 -->
            <div class="sidebar-widget">
                <div class="widget-header">
                    <h3><span class="icon">🛡️</span> 安全提醒</h3>
                </div>
                <div class="widget-content">
                    <div class="safety-tips">
                        <div class="tip-item">
                            <span class="tip-icon">⚠️</span>
                            <span class="tip-text">黑名单网站可能包含恶意内容</span>
                        </div>
                        <div class="tip-item">
                            <span class="tip-icon">🔒</span>
                            <span class="tip-text">请勿访问或提交个人信息</span>
                        </div>
                        <div class="tip-item">
                            <span class="tip-icon">📢</span>
                            <span class="tip-text">发现可疑网站请及时举报</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VIP推荐网站 -->
            <div class="sidebar-widget">
                <div class="widget-header">
                    <h3><span class="icon">👑</span> VIP推荐网站</h3>
                </div>
                <div class="widget-content">
                    <div class="vip-sites">
                        {#foreach from=get_websites(0, 6, false, true) item=vip#}
                        <div class="vip-item">
                            <div class="vip-info">
                                <a href="?mod=vip_detail&id={#$vip.web_id#}" class="vip-name" title="{#$vip.web_name#}">
                                    <span class="vip-badge">VIP</span>
                                    {#$vip.web_name#}
                                </a>
                                <div class="vip-desc">{#$vip.web_intro|truncate:40#}</div>
                                <div class="vip-stats">
                                    <span class="stat">👁️ {#$vip.web_views#}</span>
                                    <span class="stat">⭐ 推荐</span>
                                </div>
                            </div>
                        </div>
                        {#foreachelse#}
                        <div class="vip-item">
                            <div class="vip-info">
                                <a href="?mod=vip_list" class="vip-name">
                                    <span class="vip-badge">VIP</span>
                                    查看VIP网站列表
                                </a>
                                <div class="vip-desc">优质VIP网站推荐</div>
                                <div class="vip-stats">
                                    <span class="stat">⭐ 推荐</span>
                                </div>
                            </div>
                        </div>
                        {#/foreach#}
                    </div>
                </div>
            </div>

            <!-- 推荐文章 -->
            <div class="sidebar-widget">
                <div class="widget-header">
                    <h3><span class="icon">📰</span> 推荐文章</h3>
                </div>
                <div class="widget-content">
                    <div class="article-list">
                        {#foreach from=get_articles(0, 8) item=article#}
                        <div class="article-item">
                            <a href="{#$article.art_link#}" class="article-title" title="{#$article.art_title#}">
                                {#$article.art_title#}
                            </a>
                            <div class="article-meta">
                                <span class="article-date">{#$article.art_ctime|date_format:'%m-%d'#}</span>
                                <span class="article-views">👁️ {#$article.art_views#}</span>
                            </div>
                        </div>
                        {#foreachelse#}
                        <div class="article-item">
                            <a href="?mod=article" class="article-title">
                                查看更多文章
                            </a>
                            <div class="article-meta">
                                <span class="article-date">最新</span>
                                <span class="article-views">📰 文章</span>
                            </div>
                        </div>
                        {#/foreach#}
                    </div>
                </div>
            </div>

            <!-- 违规统计 -->
            <div class="sidebar-widget">
                <div class="widget-header">
                    <h3><span class="icon">📊</span> 违规统计</h3>
                </div>
                <div class="widget-content">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{#$total#}</div>
                            <div class="stat-label">总违规网站</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">0</div>
                            <div class="stat-label">本月新增</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 广告位 -->
            <div class="sidebar-widget">
                <div class="widget-content">
                    {#get_adcode(2)#}
                </div>
            </div>
        </div>
    </div>

    {#include file="footer.html"#}
</div>

<style>
/* 黑名单页面专用样式 */
.blacklist-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px;
    color: white;
}

.header-left .page-title {
    margin: 0 0 8px 0;
    font-size: 28px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 12px;
}

.header-left .page-title .icon {
    font-size: 32px;
}

.header-left .page-desc {
    margin: 0;
    font-size: 16px;
    opacity: 0.9;
}

.header-right .stats-card {
    background: rgba(255, 255, 255, 0.2);
    padding: 20px;
    border-radius: 12px;
    text-align: center;
    backdrop-filter: blur(10px);
}

.stats-card .stat-number {
    display: block;
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card .stat-label {
    font-size: 14px;
    opacity: 0.9;
}

/* 筛选区域 */
.filter-section {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.filter-header h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
}

.filter-tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.filter-tab {
    padding: 10px 18px;
    background: #f8f9fa;
    color: #666;
    text-decoration: none;
    border-radius: 25px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.filter-tab:hover {
    background: #e9ecef;
    color: #495057;
    transform: translateY(-1px);
}

.filter-tab.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-color: #667eea;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* 黑名单列表 */
.blacklist-list-section {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
}

.blacklist-table {
    width: 100%;
}

.table-header {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 2.5fr 1.2fr 1fr;
    gap: 15px;
    padding: 20px 25px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    font-size: 14px;
}

.table-body {
    max-height: 600px;
    overflow-y: auto;
}

.table-row {
    display: grid;
    grid-template-columns: 2fr 1.5fr 1fr 2.5fr 1.2fr 1fr;
    gap: 15px;
    padding: 20px 25px;
    border-bottom: 1px solid #f1f3f4;
    transition: all 0.3s ease;
    align-items: center;
}

.table-row:hover {
    background: #f8f9fa;
    transform: translateX(3px);
}

.table-row:last-child {
    border-bottom: none;
}

.website-name {
    font-weight: 600;
    color: #333;
    font-size: 15px;
}

.website-link {
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    display: block;
    padding: 2px 0;
}

.website-link:hover {
    color: #667eea;
    text-decoration: underline;
}

.masked-domain {
    font-family: 'Courier New', monospace;
    background: #f8f9fa;
    padding: 6px 12px;
    border-radius: 6px;
    color: #666;
    font-size: 13px;
    border: 1px solid #e9ecef;
}

.category-badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    color: white;
    text-align: center;
    display: inline-block;
}

.category-0 { background: linear-gradient(135deg, #6c757d, #5a6268); }
.category-1 { background: linear-gradient(135deg, #dc3545, #c82333); }
.category-2 { background: linear-gradient(135deg, #fd7e14, #e55a00); }
.category-3 { background: linear-gradient(135deg, #ffc107, #e0a800); }
.category-4 { background: linear-gradient(135deg, #e83e8c, #d91a72); }
.category-5 { background: linear-gradient(135deg, #6f42c1, #59359a); }
.category-6 { background: linear-gradient(135deg, #20c997, #1aa179); }
.category-7 { background: linear-gradient(135deg, #17a2b8, #138496); }
.category-8 { background: linear-gradient(135deg, #343a40, #23272b); }

.reason-text {
    color: #666;
    font-size: 14px;
    line-height: 1.4;
}

.time-info .date {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.time-info .operator {
    font-size: 12px;
    color: #999;
    margin-top: 2px;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.btn-detail {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    text-decoration: none;
    border-radius: 20px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.btn-detail:hover {
    background: linear-gradient(135deg, #5a6fd8, #6a42a0);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.btn-detail .btn-icon {
    font-size: 14px;
}

/* 无数据状态 */
.no-data {
    text-align: center;
    padding: 80px 20px;
    color: #666;
}

.no-data-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.no-data h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 24px;
}

.no-data p {
    margin: 0;
    font-size: 16px;
    opacity: 0.8;
}

/* 侧边栏样式 */
.sidebar-widget {
    background: white;
    border-radius: 12px;
    margin-bottom: 25px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.sidebar-widget:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.widget-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 18px 20px;
    border-bottom: 1px solid #dee2e6;
}

.widget-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-header .icon {
    font-size: 18px;
}

.widget-content {
    padding: 20px;
}

/* 安全提醒 */
.safety-tips {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.tip-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.tip-icon {
    font-size: 16px;
    margin-top: 2px;
}

.tip-text {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

/* VIP网站 */
.vip-sites {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.vip-item {
    padding: 15px 0;
    border-bottom: 1px solid #f1f3f4;
}

.vip-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.vip-name {
    display: flex;
    align-items: center;
    gap: 8px;
    text-decoration: none;
    color: #333;
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 6px;
}

.vip-name:hover {
    color: #667eea;
}

.vip-badge {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #333;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 10px;
    font-weight: 700;
    text-transform: uppercase;
}

.vip-desc {
    color: #666;
    font-size: 13px;
    line-height: 1.4;
    margin-bottom: 8px;
}

.vip-stats {
    display: flex;
    gap: 12px;
}

.vip-stats .stat {
    font-size: 12px;
    color: #999;
}

/* 推荐文章 */
.article-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.article-item {
    padding: 12px 0;
    border-bottom: 1px solid #f1f3f4;
}

.article-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.article-title {
    display: block;
    color: #333;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 6px;
}

.article-title:hover {
    color: #667eea;
}

.article-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.article-date, .article-views {
    font-size: 12px;
    color: #999;
}

/* 统计卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.stat-card {
    text-align: center;
    padding: 20px 15px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.stat-card .stat-number {
    display: block;
    font-size: 24px;
    font-weight: 700;
    color: #dc3545;
    margin-bottom: 5px;
}

.stat-card .stat-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
}

/* 分页样式 */
.pagination-wrapper {
    padding: 25px;
    text-align: center;
    border-top: 1px solid #f1f3f4;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 3px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.pagination a {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #dee2e6;
}

.pagination a:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.pagination .current {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: 1px solid #667eea;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .filter-tabs {
        justify-content: center;
    }

    .table-header, .table-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .col-action {
        margin-top: 10px;
        padding-top: 10px;
        border-top: 1px solid #f1f3f4;
    }

    .table-header {
        display: none;
    }

    .table-row {
        padding: 15px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        margin-bottom: 10px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }
}
</style>
