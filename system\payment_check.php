<?php
require('common.php');

echo "<html><head><title>付费系统检查</title></head><body>";
echo "<h2>付费系统状态检查</h2>";

// 检查付费记录表
$payment_table = $DB->table('payment_records');
$check_payment = $DB->query("SHOW TABLES LIKE '$payment_table'");
if ($DB->num_rows($check_payment) > 0) {
    echo "<p style='color: green;'>✓ 付费记录表存在</p>";
} else {
    echo "<p style='color: red;'>✗ 付费记录表不存在</p>";
    echo "<p><a href='payment_init.php' style='background: #007bff; color: #fff; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>点击初始化数据库</a></p>";
}

// 检查价格配置表
$price_table = $DB->table('payment_prices');
$check_price = $DB->query("SHOW TABLES LIKE '$price_table'");
if ($DB->num_rows($check_price) > 0) {
    echo "<p style='color: green;'>✓ 价格配置表存在</p>";
    
    // 检查是否有默认价格配置
    $price_count = $DB->get_count($price_table, '1=1');
    if ($price_count > 0) {
        echo "<p style='color: green;'>✓ 已有 $price_count 个价格配置</p>";
    } else {
        echo "<p style='color: orange;'>⚠ 价格配置表为空，需要添加默认配置</p>";
        echo "<p><a href='payment_init.php' style='background: #ffc107; color: #000; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>点击添加默认配置</a></p>";
    }
} else {
    echo "<p style='color: red;'>✗ 价格配置表不存在</p>";
    echo "<p><a href='payment_init.php' style='background: #007bff; color: #fff; padding: 10px 15px; text-decoration: none; border-radius: 3px;'>点击初始化数据库</a></p>";
}

// 检查模块文件
$module_file = APP_PATH . 'module/payment_price.php';
if (file_exists($module_file)) {
    echo "<p style='color: green;'>✓ 价格模块文件存在</p>";
} else {
    echo "<p style='color: red;'>✗ 价格模块文件不存在</p>";
}

// 检查模板文件
$templates = array('payment_stats.html', 'payment_manage.html', 'payment_price.html');
foreach ($templates as $template) {
    $template_path = 'themes/system/' . $template;
    if (file_exists('../' . $template_path)) {
        echo "<p style='color: green;'>✓ 模板文件存在: $template</p>";
    } else {
        echo "<p style='color: red;'>✗ 模板文件不存在: $template</p>";
    }
}

echo "<hr>";
echo "<h3>测试链接</h3>";
echo "<p><a href='payment_stats.php'>付费统计</a></p>";
echo "<p><a href='payment_manage.php'>付费管理</a></p>";
echo "<p><a href='payment_price.php'>价格配置</a></p>";
echo "<p><a href='payment_cron.php'>过期检查</a></p>";

echo "</body></html>";
?>
