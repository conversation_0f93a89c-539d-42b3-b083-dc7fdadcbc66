<?php
/**
 * 测试URL生成问题
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

define('IN_IWEBDIR', true);
define('ROOT_PATH', str_replace('\\', '/', dirname(__FILE__)).'/');
define('APP_PATH', ROOT_PATH.'source/');
define('MOD_PATH', ROOT_PATH.'module/');

require(APP_PATH.'init.php');
require(APP_PATH.'module/prelink.php');

// 创建一个简化版本的get_website_url函数
function get_website_url_simple($web_id, $abs_path = false, $web_name = '', $web_url = '') {
    global $options;

    if ($abs_path) {
        $url_prefix = $options['site_url'];
    } else {
        $url_prefix = $options['site_root'];
    }

    // 使用简单的URL结构，避免复杂的字符串处理
    if ($options['link_struct'] == 1) {
        $strurl = $url_prefix . 'siteinfo-' . $web_id . '.html';
    } elseif ($options['link_struct'] == 2) {
        $strurl = $url_prefix . 'siteinfo/' . $web_id . '.html';
    } elseif ($options['link_struct'] == 3) {
        $strurl = $url_prefix . 'siteinfo/' . $web_id;
    } else {
        $strurl = $url_prefix . '?mod=siteinfo&wid=' . $web_id;
    }

    return $strurl;
}

// 测试站点地图生成，使用简化的URL函数
function test_sitemap_with_simple_urls() {
    global $DB, $options;
    
    header("Content-Type: application/xml;");
    echo "<?xml version=\"1.0\" encoding=\"utf-8\" ?>\n";
    echo "<urlset xmlns=\"http://www.sitemaps.org/schemas/sitemap/0.9\">\n";
    
    // 首页
    echo "<url>\n";
    echo "<loc>".$options['site_url']."</loc>\n";
    echo "<lastmod>".date('c')."</lastmod>\n";
    echo "<changefreq>daily</changefreq>\n";
    echo "<priority>1.0</priority>\n";
    echo "</url>\n";
    
    // 测试网站数据，使用简化的URL生成
    try {
        $where = "web_status=3 AND (web_violation_status IS NULL OR web_violation_status=0)";
        $sql = "SELECT web_id, web_name, web_url, web_ctime FROM ".$DB->table('websites');
        $sql .= " WHERE $where ORDER BY web_id DESC LIMIT 10";
        $query = $DB->query($sql);
        
        if ($query) {
            while ($row = $DB->fetch_array($query)) {
                try {
                    // 使用简化的URL生成函数
                    $web_link = str_replace('&', '&amp;', get_website_url_simple($row['web_id'], true));
                    echo "<url>\n";
                    echo "<loc>".$web_link."</loc>\n";
                    echo "<lastmod>".date('c', $row['web_ctime'])."</lastmod>\n";
                    echo "<changefreq>weekly</changefreq>\n";
                    echo "<priority>0.6</priority>\n";
                    echo "</url>\n";
                } catch (Exception $e) {
                    // 添加错误注释
                    echo "<!-- Error processing website ID {$row['web_id']}: " . htmlspecialchars($e->getMessage()) . " -->\n";
                    continue;
                }
            }
            $DB->free_result($query);
        }
    } catch (Exception $e) {
        echo "<!-- Database error: " . htmlspecialchars($e->getMessage()) . " -->\n";
    }
    
    echo "</urlset>\n";
}

// 如果是测试简化版本
if (isset($_GET['test_simple'])) {
    test_sitemap_with_simple_urls();
    exit;
}

// 如果是测试URL生成
if (isset($_GET['test_urls'])) {
    header("Content-Type: text/plain; charset=utf-8");
    
    echo "URL生成测试\n";
    echo "=============\n\n";
    
    // 获取一些测试数据
    $sql = "SELECT web_id, web_name, web_url FROM ".$DB->table('websites')." WHERE web_status=3 LIMIT 5";
    $query = $DB->query($sql);
    
    if ($query) {
        while ($row = $DB->fetch_array($query)) {
            echo "网站ID: {$row['web_id']}\n";
            echo "网站名称: {$row['web_name']}\n";
            echo "网站URL: {$row['web_url']}\n";
            
            try {
                $original_url = get_website_url($row['web_id'], true, $row['web_name'], $row['web_url']);
                echo "原始URL函数: $original_url\n";
            } catch (Exception $e) {
                echo "原始URL函数错误: " . $e->getMessage() . "\n";
            }
            
            try {
                $simple_url = get_website_url_simple($row['web_id'], true);
                echo "简化URL函数: $simple_url\n";
            } catch (Exception $e) {
                echo "简化URL函数错误: " . $e->getMessage() . "\n";
            }
            
            echo "\n";
        }
        $DB->free_result($query);
    }
    
    exit;
}

?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试URL生成问题</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .test-link { display: inline-block; margin: 10px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; }
        .test-link:hover { background: #0056b3; }
    </style>
</head>
<body>
    <h1>测试URL生成问题</h1>
    <p>测试时间: <?php echo date('Y-m-d H:i:s'); ?></p>
    
    <div class="test-section warning">
        <h2>问题分析</h2>
        <p>基于您的反馈，webdir和weblink站点地图缺少结束标签，而其他站点地图正常。这很可能是因为：</p>
        <ul>
            <li><strong>get_website_url函数的复杂字符串处理</strong> - 包含复杂的正则表达式和多字节字符处理</li>
            <li><strong>某些网站名称或URL包含特殊字符</strong> - 导致正则表达式或字符串函数失败</li>
            <li><strong>内存或性能问题</strong> - 复杂的字符串处理可能导致脚本超时或内存不足</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>测试选项</h2>
        
        <div style="margin: 10px 0;">
            <strong>1. 测试URL生成对比：</strong><br>
            <a href="?test_urls=1" target="_blank" class="test-link">对比原始和简化URL生成</a>
            <small>（查看哪些网站的URL生成可能有问题）</small>
        </div>
        
        <div style="margin: 10px 0;">
            <strong>2. 测试简化版站点地图：</strong><br>
            <a href="?test_simple=1" target="_blank" class="test-link">测试使用简化URL的站点地图</a>
            <small>（使用简单的URL结构，避免复杂字符串处理）</small>
        </div>
        
        <div style="margin: 10px 0;">
            <strong>3. 对比测试：</strong><br>
            <a href="https://www.95dir.com/?mod=sitemap&type=webdir&format=xml" target="_blank" class="test-link">原始webdir站点地图</a>
            <small>（当前有问题的版本）</small>
        </div>
    </div>
    
    <div class="test-section info">
        <h2>解决方案</h2>
        <p>如果简化版本的站点地图能正常生成完整的XML，说明问题确实出在复杂的URL生成逻辑上。解决方案：</p>
        <ol>
            <li><strong>临时方案：</strong>在站点地图中使用简化的URL结构</li>
            <li><strong>长期方案：</strong>优化get_website_url函数，添加更好的错误处理</li>
            <li><strong>性能优化：</strong>考虑缓存URL生成结果</li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>当前配置</h2>
        <?php
        echo "<p><strong>链接结构设置：</strong> " . $options['link_struct'] . "</p>\n";
        echo "<p><strong>站点URL：</strong> " . $options['site_url'] . "</p>\n";
        echo "<p><strong>站点根目录：</strong> " . $options['site_root'] . "</p>\n";
        ?>
    </div>
</body>
</html>
