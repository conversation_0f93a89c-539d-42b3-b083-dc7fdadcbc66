<?php
function is_valid_dir($str) {
	if (preg_match('/^[a-zA-Z][a-zA-Z0-9_-]*$/', $str)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_url($url) {
	if (preg_match('/^http(s)?:\/\//i', $url)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_email($email) {
	if (preg_match('/^[\w\-\.]+@[\w\-\.]+(\.\w+)+$/', $email)) {
		return true;
	} else {
		return false;
	}
}

function is_valid_domain($domain) {
	// 移除协议前缀
	$domain = preg_replace('/^https?:\/\//', '', $domain);
	$domain = preg_replace('/^www\./', '', $domain);
	$domain = rtrim($domain, '/');

	// 使用 parse_url 解析URL，支持带路径和查询参数的URL
	$parsed = parse_url('http://' . $domain);

	// 如果解析失败，返回false
	if ($parsed === false || !isset($parsed['host'])) {
		return false;
	}

	// 提取主机名进行验证
	$host = $parsed['host'];

	// 基本域名格式验证（只验证主机名部分）
	if (preg_match('/^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$/', $host)) {
		return true;
	} else {
		return false;
	}
}
?>