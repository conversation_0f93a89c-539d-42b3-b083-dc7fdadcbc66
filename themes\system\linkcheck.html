{#include file="header.html"#}

<div class="main">
    <div class="title">
        <h2>{#$pagetitle#}</h2>
    </div>
    
    <!-- 统计信息 -->
    <div class="box">
        <h3>链接检测统计</h3>
        <table class="list">
            <tr>
                <th width="150">项目</th>
                <th>数量</th>
                <th>说明</th>
            </tr>
            <tr>
                <td>总网站数</td>
                <td><strong>{#$stats.total#}</strong></td>
                <td>已审核通过的网站总数</td>
            </tr>
            <tr>
                <td>已链接</td>
                <td><span style="color: #090;"><strong>{#$stats.linked#}</strong></span></td>
                <td>检测到友情链接的网站</td>
            </tr>
            <tr>
                <td>未链接</td>
                <td><span style="color: #f00;"><strong>{#$stats.unlinked#}</strong></span></td>
                <td>未检测到友情链接的网站</td>
            </tr>
            <tr>
                <td>付费网站</td>
                <td><span style="color: #666;"><strong>{#$stats.paid#}</strong></span></td>
                <td>付费网站不检测友情链接</td>
            </tr>
        </table>
    </div>
    
    <!-- 检测配置 -->
    <div class="box">
        <h3>检测配置</h3>
        <form method="post" action="{#$fileurl#}?action=update_config">
            <table class="form">
                <tr>
                    <th width="150">链接名称：</th>
                    <td>
                        <input type="text" name="check_link_name" value="{#$check_link_name#}" class="ipt" size="30" />
                        <span class="tips">要检查的友情链接文本</span>
                    </td>
                </tr>
                <tr>
                    <th>链接域名：</th>
                    <td>
                        <input type="text" name="check_link_url" value="{#$check_link_url#}" class="ipt" size="30" />
                        <span class="tips">要检查的友情链接域名（不含http://）</span>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <input type="submit" value="更新配置" class="btn" />
                    </td>
                </tr>
            </table>
        </form>
    </div>
    
    <!-- 单个检测 -->
    <div class="box">
        <h3>单个网站检测</h3>
        <table class="form">
            <tr>
                <th width="150">网站URL：</th>
                <td>
                    <input type="text" id="single_url" placeholder="例：https://jis16.com/" class="ipt" size="50" />
                    <input type="button" value="检测" class="btn" onclick="checkSingleWebsite()" />
                </td>
            </tr>
            <tr>
                <th>检测结果：</th>
                <td>
                    <div id="single_result" style="padding: 10px; background: #f9f9f9; min-height: 50px;">
                        点击"检测"按钮开始检测...
                    </div>
                </td>
            </tr>
        </table>
    </div>
    
    <!-- 批量检测 -->
    <div class="box">
        <h3>批量检测</h3>
        <table class="form">
            <tr>
                <th width="150">检测数量：</th>
                <td>
                    <select id="batch_limit" class="sel">
                        <option value="10">10个网站</option>
                        <option value="20">20个网站</option>
                        <option value="50">50个网站</option>
                        <option value="100">100个网站</option>
                    </select>
                    <input type="button" value="开始批量检测" class="btn" onclick="startBatchCheck()" />
                    <span class="tips">建议分批检测，避免服务器压力过大</span>
                </td>
            </tr>
            <tr>
                <th>检测进度：</th>
                <td>
                    <div id="batch_progress" style="padding: 10px; background: #f9f9f9; min-height: 100px;">
                        点击"开始批量检测"按钮开始...
                    </div>
                </td>
            </tr>
        </table>
    </div>
    
    <!-- 最近未链接的网站 -->
    {#if $recent_unlinked#}
    <div class="box">
        <h3>最近未链接的网站</h3>
        <table class="list">
            <tr>
                <th width="80">ID</th>
                <th>网站名称</th>
                <th>网站URL</th>
                <th width="100">操作</th>
            </tr>
            {#foreach from=$recent_unlinked item=web#}
            <tr>
                <td>{#$web.web_id#}</td>
                <td>{#$web.web_name#}</td>
                <td><a href="http://{#$web.web_url#}" target="_blank">{#$web.web_url#}</a></td>
                <td>
                    <a href="javascript:void(0)" onclick="checkSingleUrl('{#$web.web_url#}')" class="btn-small">检测</a>
                </td>
            </tr>
            {#/foreach#}
        </table>
    </div>
    {#/if#}
</div>

<script>
// 单个网站检测
function checkSingleWebsite() {
    const url = document.getElementById('single_url').value.trim();
    if (!url) {
        alert('请输入网站URL');
        return;
    }
    
    const resultDiv = document.getElementById('single_result');
    resultDiv.innerHTML = '<img src="skin/loading.gif" width="16" height="16"> 正在检测，请稍候...';
    
    fetch('{#$fileurl#}?action=check_single&url=' + encodeURIComponent(url))
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div style="margin-bottom: 10px;">';
                html += '<strong>检测结果：</strong>' + (data.has_link ? '<span style="color: #090;">已链接</span>' : '<span style="color: #f00;">未链接</span>');
                html += '</div>';
                html += '<div style="margin-bottom: 10px;"><strong>详情：</strong>' + data.details + '</div>';
                if (data.patterns_found && data.patterns_found.length > 0) {
                    html += '<div><strong>匹配模式：</strong>' + data.patterns_found.join('、') + '</div>';
                }
                resultDiv.innerHTML = html;
            } else {
                resultDiv.innerHTML = '<span style="color: #f00;">检测失败：' + data.message + '</span>';
            }
        })
        .catch(error => {
            resultDiv.innerHTML = '<span style="color: #f00;">检测出错：' + error.message + '</span>';
        });
}

// 快速检测（从列表点击）
function checkSingleUrl(url) {
    document.getElementById('single_url').value = url;
    checkSingleWebsite();
}

// 批量检测
function startBatchCheck() {
    const limit = document.getElementById('batch_limit').value;
    const progressDiv = document.getElementById('batch_progress');
    
    progressDiv.innerHTML = '<img src="skin/loading.gif" width="16" height="16"> 正在批量检测 ' + limit + ' 个网站，请稍候...';
    
    const formData = new FormData();
    formData.append('limit', limit);
    formData.append('status', '3');
    
    fetch('{#$fileurl#}?action=batch_check', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                let html = '<div style="margin-bottom: 15px;">';
                html += '<strong>批量检测完成！</strong><br>';
                html += '检测数量：' + data.checked_count + ' 个<br>';
                html += '状态更新：' + data.updated_count + ' 个<br>';
                html += '</div>';
                
                if (data.results && data.results.length > 0) {
                    html += '<table class="list" style="width: 100%;">';
                    html += '<tr><th>网站</th><th>原状态</th><th>新状态</th><th>检测详情</th></tr>';
                    
                    data.results.forEach(function(result) {
                        html += '<tr>';
                        html += '<td>' + result.web_name + '<br><small>' + result.web_url + '</small></td>';
                        html += '<td>' + result.old_status + '</td>';
                        html += '<td style="color: ' + (result.new_status === '已链接' ? '#090' : '#f00') + ';">' + result.new_status + '</td>';
                        html += '<td><small>' + result.details + '</small></td>';
                        html += '</tr>';
                    });
                    
                    html += '</table>';
                }
                
                progressDiv.innerHTML = html;
                
                // 刷新页面统计
                setTimeout(function() {
                    location.reload();
                }, 3000);
                
            } else {
                progressDiv.innerHTML = '<span style="color: #f00;">批量检测失败</span>';
            }
        })
        .catch(error => {
            progressDiv.innerHTML = '<span style="color: #f00;">检测出错：' + error.message + '</span>';
        });
}
</script>

{#include file="footer.html"#}
